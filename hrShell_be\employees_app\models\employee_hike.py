from datetime import timezone
from django.db import models
from django.utils import timezone
from enum import Enum


class HikeStatus(str, Enum):
    PENDING = 'pending'
    APPROVED = 'approved'
    REJECTED = 'rejected'

    @classmethod
    def choices(cls):
        return [(item.value, item.name.title()) for item in cls]

def get_current_date():
    """
    Returns the current date (without time)
    """
    return timezone.now().date()

class EmployeeHike(models.Model):
    """
    Employee hike model for storing hike information
    """
    employee = models.ForeignKey('Employee', on_delete=models.CASCADE)
    hike_percentage = models.DecimalField(max_digits=5, decimal_places=2)
    hike_effective_date = models.DateField(default=get_current_date)
    hike_reason = models.TextField(blank=True, null=True)
    department = models.ForeignKey('Department', on_delete=models.CASCADE)
    previous_salary = models.DecimalField(max_digits=10, decimal_places=2)
    new_salary = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(
        max_length=10,
        choices=HikeStatus.choices(),
        default=HikeStatus.PENDING.value
    )
    approved_by = models.ForeignKey(
        'Employee',
        on_delete=models.CASCADE,
        related_name='approved_hikes',
        null=True,
        blank=True
    )


    def __str__(self):
        return f"{self.employee} - {self.hike_percentage}% hike on {self.hike_effective_date}"
