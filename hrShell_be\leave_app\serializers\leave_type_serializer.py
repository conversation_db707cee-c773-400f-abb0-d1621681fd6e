from rest_framework import serializers
from leave_app.models.leave_type import LeaveType, LeaveTypeStatus


class LeaveTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for the LeaveType model
    """
    organization_name = serializers.ReadOnlyField(source='organization.name')
    status_display = serializers.ReadOnlyField(source='get_status_display')
    
    class Meta:
        model = LeaveType
        fields = [
            'id', 'name', 'code', 'description', 'organization', 'organization_name',
            'is_paid', 'max_days_per_year', 'min_days_per_request', 'max_days_per_request',
            'carry_forward_allowed', 'max_carry_forward_days', 'encashment_allowed',
            'max_encashment_days', 'applicable_during_probation', 'probation_period_percentage',
            'requires_documentation', 'documentation_instructions', 'status', 'status_display',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_probation_period_percentage(self, value):
        """
        Validate that probation_period_percentage is between 0 and 100
        """
        if value < 0 or value > 100:
            raise serializers.ValidationError("Probation period percentage must be between 0 and 100")
        return value
    
    def validate(self, data):
        """
        Validate that max_days_per_request is less than or equal to max_days_per_year
        """
        max_days_per_year = data.get('max_days_per_year', 0)
        max_days_per_request = data.get('max_days_per_request', 0)
        
        if max_days_per_year > 0 and max_days_per_request > 0 and max_days_per_request > max_days_per_year:
            raise serializers.ValidationError({
                "max_days_per_request": "Max days per request cannot be greater than max days per year"
            })
        
        return data
