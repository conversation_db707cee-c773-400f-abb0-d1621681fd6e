from django.db import models
import json
from django.conf import settings
from django.db import connection
from common.utils.vector_operations import (
    create_embedding,
    format_embedding_for_postgres,
    get_embedding_dimension
)

class VectorizedDocument(models.Model):
    """
    A model for storing documents with vector embeddings.
    Note: The actual vector embedding is stored in PostgreSQL using raw SQL
    since Django ORM doesn't support pgvector's vector type natively.
    """
    title = models.CharField(max_length=255)
    content = models.TextField()
    metadata = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'vectorized_documents'

    def save(self, *args, **kwargs):
        # First, save the model to get an ID
        super().save(*args, **kwargs)

        # Then, create and store the embedding using raw SQL
        self._save_embedding()

    def _save_embedding(self):
        """Save the embedding for this document using raw SQL"""
        # Create embedding
        embedding = create_embedding(self.content)
        embedding_str = format_embedding_for_postgres(embedding)

        # Check if the embedding table exists, create it if not
        self._ensure_embedding_table()

        # Check if an embedding already exists for this document
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT COUNT(*) FROM document_embeddings WHERE document_id = %s",
                [self.id]
            )
            count = cursor.fetchone()[0]

            if count > 0:
                # Update existing embedding
                cursor.execute(
                    """
                    UPDATE document_embeddings
                    SET embedding = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE document_id = %s
                    """,
                    [embedding_str, self.id]
                )
            else:
                # Insert new embedding
                cursor.execute(
                    """
                    INSERT INTO document_embeddings (document_id, embedding)
                    VALUES (%s, %s)
                    """,
                    [self.id, embedding_str]
                )

    def _ensure_embedding_table(self):
        """Ensure the embedding table exists"""
        dimension = get_embedding_dimension()

        with connection.cursor() as cursor:
            # Create the embeddings table if it doesn't exist
            cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS document_embeddings (
                id SERIAL PRIMARY KEY,
                document_id INTEGER NOT NULL REFERENCES {self._meta.db_table}(id) ON DELETE CASCADE,
                embedding vector({dimension}) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
            """)

            # Create index for vector search
            cursor.execute("""
            CREATE INDEX IF NOT EXISTS document_embeddings_embedding_idx
            ON document_embeddings USING ivfflat (embedding vector_l2_ops);
            """)

    @classmethod
    def search_similar(cls, query_text, limit=5):
        """Search for similar documents using vector similarity"""
        # Create embedding for query
        query_embedding = create_embedding(query_text)
        query_embedding_str = format_embedding_for_postgres(query_embedding)

        # Search database
        with connection.cursor() as cursor:
            cursor.execute(f"""
            SELECT
                vd.id,
                vd.title,
                vd.content,
                vd.metadata,
                de.embedding <-> %s as distance
            FROM
                {cls._meta.db_table} vd
            JOIN
                document_embeddings de ON vd.id = de.document_id
            ORDER BY
                distance ASC
            LIMIT %s;
            """, [query_embedding_str, limit])

            results = []
            for row in cursor.fetchall():
                id, title, content, metadata, distance = row
                results.append({
                    'id': id,
                    'title': title,
                    'content': content,
                    'metadata': metadata,
                    'distance': distance
                })

            return results
