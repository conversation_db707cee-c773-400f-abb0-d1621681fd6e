from django.db import models
from django.core.validators import Min<PERSON><PERSON>ueValidator
from recruitment_app.models.job_requisition import JobRequisition
from recruitment_app.models.recruitment_source import RecruitmentSource
from employees_app.models.employee import Employee


class JobPosting(models.Model):
    """
    Model to represent job postings published to various channels.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('expired', 'Expired'),
        ('closed', 'Closed'),
    ]
    
    VISIBILITY_CHOICES = [
        ('public', 'Public'),
        ('internal', 'Internal Only'),
        ('private', 'Private/Referral Only'),
    ]
    
    # Basic Information
    job_requisition = models.ForeignKey(JobRequisition, on_delete=models.CASCADE, related_name='job_postings')
    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True)
    description = models.TextField()
    requirements = models.TextField()
    responsibilities = models.TextField()
    qualifications = models.TextField()
    benefits = models.TextField(blank=True, null=True)
    
    # Posting Details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    visibility = models.CharField(max_length=20, choices=VISIBILITY_CHOICES, default='public')
    is_remote = models.BooleanField(default=False)
    allows_remote = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)
    
    # Dates
    published_date = models.DateField(null=True, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    
    # Salary Information
    display_salary = models.BooleanField(default=False)
    salary_min = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], null=True, blank=True)
    salary_max = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], null=True, blank=True)
    salary_currency = models.CharField(max_length=3, default='USD')
    salary_period = models.CharField(max_length=20, default='yearly')
    
    # SEO and Metadata
    meta_title = models.CharField(max_length=255, blank=True, null=True)
    meta_description = models.TextField(blank=True, null=True)
    meta_keywords = models.CharField(max_length=255, blank=True, null=True)
    
    # Sources
    sources = models.ManyToManyField(RecruitmentSource, related_name='job_postings', blank=True)
    
    # Stakeholders
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='job_postings_created')
    updated_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='job_postings_updated')
    
    # Statistics
    view_count = models.PositiveIntegerField(default=0)
    application_count = models.PositiveIntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-published_date', 'title']
        verbose_name = 'Job Posting'
        verbose_name_plural = 'Job Postings'
    
    def __str__(self):
        return self.title
