from django.db import models
from django.core.validators import MinValueValidator
from recruitment_app.models.application import Application
from employees_app.models.employee import Employee


class Offer(models.Model):
    """
    Model to represent job offers made to candidates.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending_approval', 'Pending Approval'),
        ('approved', 'Approved'),
        ('sent', 'Sent'),
        ('accepted', 'Accepted'),
        ('negotiating', 'Negotiating'),
        ('declined', 'Declined'),
        ('expired', 'Expired'),
        ('withdrawn', 'Withdrawn'),
    ]
    
    # Basic Information
    application = models.OneToOneField(Application, on_delete=models.CASCADE, related_name='offer')
    offer_date = models.DateField()
    expiry_date = models.DateField()
    
    # Offer Details
    position = models.CharField(max_length=255)
    department = models.CharField(max_length=255)
    location = models.CharField(max_length=255)
    employment_type = models.CharField(max_length=50)
    
    # Salary Information
    base_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    bonus = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], default=0)
    stock_options = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], default=0)
    benefits = models.TextField(blank=True, null=True)
    
    # Joining Information
    joining_date = models.DateField()
    probation_period = models.PositiveIntegerField(help_text="Probation period in months", default=3)
    
    # Status Information
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Approval Information
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='offers_approved')
    approval_date = models.DateField(blank=True, null=True)
    
    # Offer Letter
    offer_letter = models.FileField(upload_to='offer_letters/%Y/%m/', blank=True, null=True)
    
    # Response Information
    response_date = models.DateField(blank=True, null=True)
    negotiation_details = models.TextField(blank=True, null=True)
    decline_reason = models.TextField(blank=True, null=True)
    
    # Withdrawal Information
    withdrawal_reason = models.TextField(blank=True, null=True)
    withdrawn_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='offers_withdrawn')
    withdrawal_date = models.DateField(blank=True, null=True)
    
    # Notes
    notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-offer_date']
        verbose_name = 'Offer'
        verbose_name_plural = 'Offers'
    
    def __str__(self):
        return f"Offer to {self.application.candidate} for {self.position}"
