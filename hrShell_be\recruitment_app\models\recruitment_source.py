from django.db import models
from organization_app.models.organization import Organization


class RecruitmentSource(models.Model):
    """
    Model to represent recruitment sources like job boards, career pages, etc.
    """
    SOURCE_TYPE_CHOICES = [
        ('career_page', 'Career Page'),
        ('job_board', 'Job Board'),
        ('social_media', 'Social Media'),
        ('referral', 'Employee Referral'),
        ('agency', 'Recruitment Agency'),
        ('university', 'University/College'),
        ('event', 'Job Fair/Event'),
        ('direct', 'Direct Application'),
        ('other', 'Other'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='recruitment_sources')
    source_type = models.CharField(max_length=20, choices=SOURCE_TYPE_CHOICES)
    description = models.TextField(blank=True, null=True)
    
    # Contact Information
    website = models.URLField(blank=True, null=True)
    contact_person = models.CharField(max_length=100, blank=True, null=True)
    contact_email = models.EmailField(blank=True, null=True)
    contact_phone = models.CharField(max_length=20, blank=True, null=True)
    
    # Integration Details
    api_key = models.CharField(max_length=255, blank=True, null=True)
    api_endpoint = models.URLField(blank=True, null=True)
    integration_active = models.BooleanField(default=False)
    
    # Cost Information
    cost_per_post = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    cost_per_hire = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    currency = models.CharField(max_length=3, default='USD')
    
    # Status
    is_active = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = 'Recruitment Source'
        verbose_name_plural = 'Recruitment Sources'
    
    def __str__(self):
        return f"{self.name} ({self.get_source_type_display()})"
