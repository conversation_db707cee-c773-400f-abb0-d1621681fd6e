from django.db import models


class LeaveType(models.TextChoices):
    CASUAL = 'casual', 'Casual Leave'
    SICK = 'sick', 'Sick Leave'
    ANNUAL = 'annual', 'Annual Leave'
    MATERNITY = 'maternity', 'Maternity Leave'
    PATERNITY = 'paternity', 'Paternity Leave'
    UNPAID = 'unpaid', 'Unpaid Leave'


class LeaveStatus(models.TextChoices):
    PENDING = 'pending', 'Pending'
    APPROVED = 'approved', 'Approved'
    REJECTED = 'rejected', 'Rejected'


class EmployeeLeave(models.Model):
    """
    Employee leave model for tracking leave requests
    """
    employee = models.ForeignKey(
        'Employee',
        on_delete=models.CASCADE,
        related_name='leaves'
    )
    leave_type = models.CharField(
        max_length=20,
        choices=LeaveType.choices
    )
    start_date = models.DateField()
    end_date = models.DateField()
    total_days = models.IntegerField(editable=False)
    reason = models.TextField(blank=True, null=True)
    status = models.Char<PERSON><PERSON>(
        max_length=10,
        choices=LeaveStatus.choices,
        default=LeaveStatus.PENDING
    )
    approved_by = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_leaves'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-start_date']
        verbose_name = "Employee Leave"
        verbose_name_plural = "Employee Leaves"

    def __str__(self):
        return f"{self.employee} - {self.get_leave_type_display()} ({self.start_date} to {self.end_date})"

    def save(self, *args, **kwargs):
        # Calculate total days
        delta = self.end_date - self.start_date
        self.total_days = delta.days + 1
        super().save(*args, **kwargs)
