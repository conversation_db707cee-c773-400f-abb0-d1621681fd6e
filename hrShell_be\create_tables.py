import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hrShell_be.settings')
django.setup()

# Get database settings
from django.conf import settings

# Connect to the database
conn = psycopg2.connect(
    dbname=settings.DATABASES['default']['NAME'],
    user=settings.DATABASES['default']['USER'],
    password=settings.DATABASES['default']['PASSWORD'],
    host=settings.DATABASES['default']['HOST'],
    port=settings.DATABASES['default']['PORT']
)
conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)

# Create a cursor
cursor = conn.cursor()

# Create the tables
try:
    # Create the department table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS "employees_app_department" (
        "id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
        "name" varchar(100) NOT NULL UNIQUE,
        "description" text NULL,
        "status" varchar(10) NOT NULL,
        "created_at" timestamp with time zone NOT NULL,
        "updated_at" timestamp with time zone NOT NULL
    );
    """)
    
    # Create the employee table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS "employees_app_employee" (
        "id" bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
        "first_name" varchar(100) NOT NULL,
        "last_name" varchar(100) NOT NULL,
        "email" varchar(254) NOT NULL UNIQUE,
        "phone" varchar(20) NULL,
        "position" varchar(100) NOT NULL,
        "hire_date" date NOT NULL,
        "gender" varchar(10) NOT NULL,
        "status" varchar(10) NOT NULL,
        "profile_picture" varchar(100) NULL,
        "resume" varchar(100) NULL,
        "contract" varchar(100) NULL,
        "created_at" timestamp with time zone NOT NULL,
        "updated_at" timestamp with time zone NOT NULL,
        "department_id" bigint NULL
    );
    """)
    
    # Create the indexes
    cursor.execute("""
    CREATE INDEX IF NOT EXISTS "employees_app_department_name_a0e668e8_like"
    ON "employees_app_department" ("name" varchar_pattern_ops);
    """)
    
    cursor.execute("""
    CREATE INDEX IF NOT EXISTS "employees_app_employee_email_ba02c03b_like"
    ON "employees_app_employee" ("email" varchar_pattern_ops);
    """)
    
    cursor.execute("""
    CREATE INDEX IF NOT EXISTS "employees_app_employee_department_id_d7ae232e"
    ON "employees_app_employee" ("department_id");
    """)
    
    # Add the foreign key constraint
    cursor.execute("""
    ALTER TABLE "employees_app_employee"
    ADD CONSTRAINT "employees_app_employ_department_id_d7ae232e_fk_employees"
    FOREIGN KEY ("department_id") REFERENCES "employees_app_department" ("id")
    DEFERRABLE INITIALLY DEFERRED;
    """)
    
    # Add the manager field to department
    cursor.execute("""
    ALTER TABLE "employees_app_department"
    ADD COLUMN IF NOT EXISTS "manager_id" bigint NULL;
    """)
    
    cursor.execute("""
    ALTER TABLE "employees_app_department"
    ADD CONSTRAINT "employees_app_depart_manager_id_dc37528a_fk_employees"
    FOREIGN KEY ("manager_id") REFERENCES "employees_app_employee" ("id")
    DEFERRABLE INITIALLY DEFERRED;
    """)
    
    cursor.execute("""
    CREATE INDEX IF NOT EXISTS "employees_app_department_manager_id_dc37528a"
    ON "employees_app_department" ("manager_id");
    """)
    
    print("Tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
finally:
    # Close the cursor and connection
    cursor.close()
    conn.close()
