# Generated by Django 5.1.7 on 2025-05-10 08:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employees_app', '0012_employeehike_status_alter_employeehike_approved_by'),
        ('organization_app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Holiday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('date', models.DateField()),
                ('description', models.TextField(blank=True, null=True)),
                ('holiday_type', models.CharField(choices=[('national', 'NATIONAL'), ('regional', 'REGIONAL'), ('religious', 'RELIGIOUS'), ('company', 'COMPANY')], default='company', max_length=20)),
                ('is_recurring', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Whether this holiday recurs every year on the same date')),
                ('is_half_day', models.BooleanField(default=False, help_text='Whether this is a half-day holiday')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='holidays', to='organization_app.location')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='holidays', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Holiday',
                'verbose_name_plural': 'Holidays',
                'ordering': ['date'],
            },
        ),
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_paid', models.BooleanField(default=True)),
                ('max_days_per_year', models.PositiveIntegerField(default=0)),
                ('min_days_per_request', models.PositiveIntegerField(default=1)),
                ('max_days_per_request', models.PositiveIntegerField(default=0)),
                ('carry_forward_allowed', models.BooleanField(default=False)),
                ('max_carry_forward_days', models.PositiveIntegerField(default=0)),
                ('encashment_allowed', models.BooleanField(default=False)),
                ('max_encashment_days', models.PositiveIntegerField(default=0)),
                ('applicable_during_probation', models.BooleanField(default=False)),
                ('probation_period_percentage', models.PositiveIntegerField(default=0, help_text='Percentage of leave allowed during probation (0-100)')),
                ('requires_documentation', models.BooleanField(default=False)),
                ('documentation_instructions', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'ACTIVE'), ('inactive', 'INACTIVE')], default='active', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_types', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Leave Type',
                'verbose_name_plural': 'Leave Types',
                'ordering': ['name'],
                'unique_together': {('organization', 'name')},
            },
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('total_days', models.DecimalField(decimal_places=2, editable=False, max_digits=5)),
                ('half_day', models.BooleanField(default=False, help_text='Whether this is a half-day leave')),
                ('first_half', models.BooleanField(default=True, help_text="If half_day is True, whether it's the first half or second half")),
                ('reason', models.TextField()),
                ('attachment', models.FileField(blank=True, null=True, upload_to='leave_attachments/')),
                ('status', models.CharField(choices=[('pending', 'PENDING'), ('approved', 'APPROVED'), ('rejected', 'REJECTED'), ('cancelled', 'CANCELLED')], default='pending', max_length=10)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leave_requests', to='employees_app.employee')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='employees_app.employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requests', to='leave_app.leavetype')),
            ],
            options={
                'verbose_name': 'Leave Request',
                'verbose_name_plural': 'Leave Requests',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='LeaveApproval',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.PositiveIntegerField(default=1, help_text='Approval level (1, 2, 3, etc.)')),
                ('status', models.CharField(choices=[('pending', 'PENDING'), ('approved', 'APPROVED'), ('rejected', 'REJECTED'), ('skipped', 'SKIPPED')], default='pending', max_length=10)),
                ('comments', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('approver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_approvals', to='employees_app.employee')),
                ('leave_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approvals', to='leave_app.leaverequest')),
            ],
            options={
                'verbose_name': 'Leave Approval',
                'verbose_name_plural': 'Leave Approvals',
                'ordering': ['leave_request', 'level'],
                'unique_together': {('leave_request', 'approver', 'level')},
            },
        ),
        migrations.CreateModel(
            name='LeavePolicy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('employee_type', models.CharField(blank=True, max_length=50, null=True)),
                ('accrual_method', models.CharField(choices=[('yearly', 'YEARLY'), ('monthly', 'MONTHLY'), ('quarterly', 'QUARTERLY'), ('biannually', 'BIANNUALLY')], default='yearly', max_length=20)),
                ('accrual_frequency', models.CharField(choices=[('start_of_period', 'START_OF_PERIOD'), ('end_of_period', 'END_OF_PERIOD'), ('prorated', 'PRORATED')], default='start_of_period', max_length=20)),
                ('max_accrual', models.PositiveIntegerField(default=0)),
                ('carry_forward_limit', models.PositiveIntegerField(default=0)),
                ('carry_forward_expiry_months', models.PositiveIntegerField(default=0, help_text='Number of months after which carried forward leaves expire (0 = never)')),
                ('encashment_limit', models.PositiveIntegerField(default=0)),
                ('probation_period_days', models.PositiveIntegerField(default=90)),
                ('apply_sandwich_rule', models.BooleanField(default=False, help_text='If True, weekends/holidays between leave days are counted as leave')),
                ('requires_approval', models.BooleanField(default=True)),
                ('auto_approve_after_days', models.PositiveIntegerField(default=0, help_text='Number of days after which leave is auto-approved (0 = never)')),
                ('min_days_before_application', models.PositiveIntegerField(default=0, help_text='Minimum days before leave date to apply')),
                ('max_days_before_application', models.PositiveIntegerField(default=0, help_text='Maximum days before leave date to apply (0 = no limit)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_unit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='leave_policies', to='organization_app.businessunit')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='leave_policies', to='employees_app.department')),
                ('designation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='leave_policies', to='organization_app.designation')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='leave_policies', to='organization_app.location')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_policies', to='organization_app.organization')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='policies', to='leave_app.leavetype')),
            ],
            options={
                'verbose_name': 'Leave Policy',
                'verbose_name_plural': 'Leave Policies',
                'ordering': ['name'],
                'unique_together': {('organization', 'leave_type', 'department', 'location', 'designation', 'business_unit', 'employee_type')},
            },
        ),
        migrations.CreateModel(
            name='LeaveBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField(default=2025)),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('accrued', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('used', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('adjusted', models.DecimalField(decimal_places=2, default=0, help_text='Manual adjustments to leave balance', max_digits=6)),
                ('encashed', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_balances', to='employees_app.employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='balances', to='leave_app.leavetype')),
            ],
            options={
                'verbose_name': 'Leave Balance',
                'verbose_name_plural': 'Leave Balances',
                'ordering': ['-year', 'employee__first_name'],
                'unique_together': {('employee', 'leave_type', 'year')},
            },
        ),
        migrations.CreateModel(
            name='WeekOff',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.IntegerField(choices=[(0, 'MONDAY'), (1, 'TUESDAY'), (2, 'WEDNESDAY'), (3, 'THURSDAY'), (4, 'FRIDAY'), (5, 'SATURDAY'), (6, 'SUNDAY')], default=6)),
                ('is_half_day', models.BooleanField(default=False, help_text='Whether this is a half-day off')),
                ('first_half', models.BooleanField(default=True, help_text="If is_half_day is True, whether it's the first half or second half")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='week_offs', to='employees_app.department')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='week_offs', to='organization_app.location')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='week_offs', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Week Off',
                'verbose_name_plural': 'Week Offs',
                'ordering': ['day_of_week'],
                'unique_together': {('organization', 'location', 'department', 'day_of_week')},
            },
        ),
    ]
