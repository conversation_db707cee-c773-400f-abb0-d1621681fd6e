import django_filters
from django.db.models import Q
from recruitment_app.models.job_requisition import JobRequisition
from recruitment_app.models.job_posting import JobPosting
from recruitment_app.models.candidate import Candidate
from recruitment_app.models.application import Application
from recruitment_app.models.interview import Interview, InterviewFeedback
from recruitment_app.models.offer import Offer
from recruitment_app.models.onboarding import Onboarding
from recruitment_app.models.recruitment_source import RecruitmentSource
from recruitment_app.models.skill import Skill


class JobRequisitionFilter(django_filters.FilterSet):
    """
    Filter for JobRequisition model.
    """
    title = django_filters.CharFilter(lookup_expr='icontains')
    code = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    business_unit = django_filters.NumberFilter()
    department = django_filters.NumberFilter()
    designation = django_filters.NumberFilter()
    location = django_filters.NumberFilter()
    employment_type = django_filters.ChoiceFilter(choices=JobRequisition.EMPLOYMENT_TYPE_CHOICES)
    experience_level = django_filters.ChoiceFilter(choices=JobRequisition.EXPERIENCE_LEVEL_CHOICES)
    min_experience_gte = django_filters.NumberFilter(field_name='min_experience', lookup_expr='gte')
    min_experience_lte = django_filters.NumberFilter(field_name='min_experience', lookup_expr='lte')
    max_experience_gte = django_filters.NumberFilter(field_name='max_experience', lookup_expr='gte')
    max_experience_lte = django_filters.NumberFilter(field_name='max_experience', lookup_expr='lte')
    min_salary_gte = django_filters.NumberFilter(field_name='min_salary', lookup_expr='gte')
    min_salary_lte = django_filters.NumberFilter(field_name='min_salary', lookup_expr='lte')
    max_salary_gte = django_filters.NumberFilter(field_name='max_salary', lookup_expr='gte')
    max_salary_lte = django_filters.NumberFilter(field_name='max_salary', lookup_expr='lte')
    number_of_openings_gte = django_filters.NumberFilter(field_name='number_of_openings', lookup_expr='gte')
    number_of_openings_lte = django_filters.NumberFilter(field_name='number_of_openings', lookup_expr='lte')
    priority = django_filters.ChoiceFilter(choices=JobRequisition.PRIORITY_CHOICES)
    requested_date_gte = django_filters.DateFilter(field_name='requested_date', lookup_expr='gte')
    requested_date_lte = django_filters.DateFilter(field_name='requested_date', lookup_expr='lte')
    target_hiring_date_gte = django_filters.DateFilter(field_name='target_hiring_date', lookup_expr='gte')
    target_hiring_date_lte = django_filters.DateFilter(field_name='target_hiring_date', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=JobRequisition.STATUS_CHOICES)
    requested_by = django_filters.NumberFilter()
    approved_by = django_filters.NumberFilter()
    is_replacement = django_filters.BooleanFilter()
    replacing_employee = django_filters.NumberFilter()
    budget_approved = django_filters.BooleanFilter()
    
    class Meta:
        model = JobRequisition
        fields = [
            'title', 'code', 'organization', 'business_unit', 'department', 'designation', 'location',
            'employment_type', 'experience_level', 'min_experience', 'max_experience', 'min_salary',
            'max_salary', 'number_of_openings', 'priority', 'requested_date', 'target_hiring_date',
            'status', 'requested_by', 'approved_by', 'is_replacement', 'replacing_employee', 'budget_approved'
        ]


class JobPostingFilter(django_filters.FilterSet):
    """
    Filter for JobPosting model.
    """
    job_requisition = django_filters.NumberFilter()
    title = django_filters.CharFilter(lookup_expr='icontains')
    slug = django_filters.CharFilter(lookup_expr='icontains')
    status = django_filters.ChoiceFilter(choices=JobPosting.STATUS_CHOICES)
    visibility = django_filters.ChoiceFilter(choices=JobPosting.VISIBILITY_CHOICES)
    is_remote = django_filters.BooleanFilter()
    allows_remote = django_filters.BooleanFilter()
    is_featured = django_filters.BooleanFilter()
    published_date_gte = django_filters.DateFilter(field_name='published_date', lookup_expr='gte')
    published_date_lte = django_filters.DateFilter(field_name='published_date', lookup_expr='lte')
    expiry_date_gte = django_filters.DateFilter(field_name='expiry_date', lookup_expr='gte')
    expiry_date_lte = django_filters.DateFilter(field_name='expiry_date', lookup_expr='lte')
    display_salary = django_filters.BooleanFilter()
    salary_min_gte = django_filters.NumberFilter(field_name='salary_min', lookup_expr='gte')
    salary_min_lte = django_filters.NumberFilter(field_name='salary_min', lookup_expr='lte')
    salary_max_gte = django_filters.NumberFilter(field_name='salary_max', lookup_expr='gte')
    salary_max_lte = django_filters.NumberFilter(field_name='salary_max', lookup_expr='lte')
    salary_currency = django_filters.CharFilter()
    salary_period = django_filters.CharFilter()
    sources = django_filters.ModelMultipleChoiceFilter(queryset=RecruitmentSource.objects.all())
    created_by = django_filters.NumberFilter()
    view_count_gte = django_filters.NumberFilter(field_name='view_count', lookup_expr='gte')
    view_count_lte = django_filters.NumberFilter(field_name='view_count', lookup_expr='lte')
    application_count_gte = django_filters.NumberFilter(field_name='application_count', lookup_expr='gte')
    application_count_lte = django_filters.NumberFilter(field_name='application_count', lookup_expr='lte')
    
    class Meta:
        model = JobPosting
        fields = [
            'job_requisition', 'title', 'slug', 'status', 'visibility', 'is_remote', 'allows_remote',
            'is_featured', 'published_date', 'expiry_date', 'display_salary', 'salary_min', 'salary_max',
            'salary_currency', 'salary_period', 'sources', 'created_by', 'view_count', 'application_count'
        ]


class CandidateFilter(django_filters.FilterSet):
    """
    Filter for Candidate model.
    """
    organization = django_filters.NumberFilter()
    first_name = django_filters.CharFilter(lookup_expr='icontains')
    last_name = django_filters.CharFilter(lookup_expr='icontains')
    email = django_filters.CharFilter(lookup_expr='icontains')
    phone = django_filters.CharFilter(lookup_expr='icontains')
    gender = django_filters.ChoiceFilter(choices=Candidate.GENDER_CHOICES)
    date_of_birth_gte = django_filters.DateFilter(field_name='date_of_birth', lookup_expr='gte')
    date_of_birth_lte = django_filters.DateFilter(field_name='date_of_birth', lookup_expr='lte')
    city = django_filters.CharFilter(lookup_expr='icontains')
    state = django_filters.CharFilter(lookup_expr='icontains')
    country = django_filters.CharFilter(lookup_expr='icontains')
    headline = django_filters.CharFilter(lookup_expr='icontains')
    total_experience_gte = django_filters.NumberFilter(field_name='total_experience', lookup_expr='gte')
    total_experience_lte = django_filters.NumberFilter(field_name='total_experience', lookup_expr='lte')
    current_employer = django_filters.CharFilter(lookup_expr='icontains')
    current_designation = django_filters.CharFilter(lookup_expr='icontains')
    current_salary_gte = django_filters.NumberFilter(field_name='current_salary', lookup_expr='gte')
    current_salary_lte = django_filters.NumberFilter(field_name='current_salary', lookup_expr='lte')
    expected_salary_gte = django_filters.NumberFilter(field_name='expected_salary', lookup_expr='gte')
    expected_salary_lte = django_filters.NumberFilter(field_name='expected_salary', lookup_expr='lte')
    notice_period_gte = django_filters.NumberFilter(field_name='notice_period', lookup_expr='gte')
    notice_period_lte = django_filters.NumberFilter(field_name='notice_period', lookup_expr='lte')
    skills = django_filters.ModelMultipleChoiceFilter(queryset=Skill.objects.all())
    highest_qualification = django_filters.CharFilter(lookup_expr='icontains')
    source = django_filters.NumberFilter()
    status = django_filters.ChoiceFilter(choices=Candidate.STATUS_CHOICES)
    rating_gte = django_filters.NumberFilter(field_name='rating', lookup_expr='gte')
    rating_lte = django_filters.NumberFilter(field_name='rating', lookup_expr='lte')
    tags = django_filters.CharFilter(lookup_expr='icontains')
    
    class Meta:
        model = Candidate
        fields = [
            'organization', 'first_name', 'last_name', 'email', 'phone', 'gender', 'date_of_birth',
            'city', 'state', 'country', 'headline', 'total_experience', 'current_employer',
            'current_designation', 'current_salary', 'expected_salary', 'notice_period', 'skills',
            'highest_qualification', 'source', 'status', 'rating', 'tags'
        ]


class ApplicationFilter(django_filters.FilterSet):
    """
    Filter for Application model.
    """
    job_posting = django_filters.NumberFilter()
    candidate = django_filters.NumberFilter()
    application_date_gte = django_filters.DateTimeFilter(field_name='application_date', lookup_expr='gte')
    application_date_lte = django_filters.DateTimeFilter(field_name='application_date', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=Application.STATUS_CHOICES)
    current_stage = django_filters.ChoiceFilter(choices=Application.STAGE_CHOICES)
    source = django_filters.NumberFilter()
    is_shortlisted = django_filters.BooleanFilter()
    rating_gte = django_filters.NumberFilter(field_name='rating', lookup_expr='gte')
    rating_lte = django_filters.NumberFilter(field_name='rating', lookup_expr='lte')
    skills_match_percentage_gte = django_filters.NumberFilter(field_name='skills_match_percentage', lookup_expr='gte')
    skills_match_percentage_lte = django_filters.NumberFilter(field_name='skills_match_percentage', lookup_expr='lte')
    expected_salary_gte = django_filters.NumberFilter(field_name='expected_salary', lookup_expr='gte')
    expected_salary_lte = django_filters.NumberFilter(field_name='expected_salary', lookup_expr='lte')
    available_from_gte = django_filters.DateFilter(field_name='available_from', lookup_expr='gte')
    available_from_lte = django_filters.DateFilter(field_name='available_from', lookup_expr='lte')
    notice_period_gte = django_filters.NumberFilter(field_name='notice_period', lookup_expr='gte')
    notice_period_lte = django_filters.NumberFilter(field_name='notice_period', lookup_expr='lte')
    rejection_stage = django_filters.ChoiceFilter(choices=Application.STAGE_CHOICES)
    rejected_by = django_filters.NumberFilter()
    rejection_date_gte = django_filters.DateTimeFilter(field_name='rejection_date', lookup_expr='gte')
    rejection_date_lte = django_filters.DateTimeFilter(field_name='rejection_date', lookup_expr='lte')
    withdrawal_date_gte = django_filters.DateTimeFilter(field_name='withdrawal_date', lookup_expr='gte')
    withdrawal_date_lte = django_filters.DateTimeFilter(field_name='withdrawal_date', lookup_expr='lte')
    assigned_to = django_filters.NumberFilter()
    last_status_change_gte = django_filters.DateTimeFilter(field_name='last_status_change', lookup_expr='gte')
    last_status_change_lte = django_filters.DateTimeFilter(field_name='last_status_change', lookup_expr='lte')
    
    class Meta:
        model = Application
        fields = [
            'job_posting', 'candidate', 'application_date', 'status', 'current_stage', 'source',
            'is_shortlisted', 'rating', 'skills_match_percentage', 'expected_salary', 'available_from',
            'notice_period', 'rejection_stage', 'rejected_by', 'rejection_date', 'withdrawal_date',
            'assigned_to', 'last_status_change'
        ]


class InterviewFilter(django_filters.FilterSet):
    """
    Filter for Interview model.
    """
    application = django_filters.NumberFilter()
    round = django_filters.ChoiceFilter(choices=Interview.ROUND_CHOICES)
    interview_type = django_filters.ChoiceFilter(choices=Interview.TYPE_CHOICES)
    scheduled_date_gte = django_filters.DateFilter(field_name='scheduled_date', lookup_expr='gte')
    scheduled_date_lte = django_filters.DateFilter(field_name='scheduled_date', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=Interview.STATUS_CHOICES)
    actual_start_time_gte = django_filters.DateTimeFilter(field_name='actual_start_time', lookup_expr='gte')
    actual_start_time_lte = django_filters.DateTimeFilter(field_name='actual_start_time', lookup_expr='lte')
    actual_end_time_gte = django_filters.DateTimeFilter(field_name='actual_end_time', lookup_expr='gte')
    actual_end_time_lte = django_filters.DateTimeFilter(field_name='actual_end_time', lookup_expr='lte')
    interviewers = django_filters.ModelMultipleChoiceFilter(queryset=Interview.interviewers.field.related_model.objects.all())
    scheduled_by = django_filters.NumberFilter()
    cancelled_by = django_filters.NumberFilter()
    
    class Meta:
        model = Interview
        fields = [
            'application', 'round', 'interview_type', 'scheduled_date', 'status', 'actual_start_time',
            'actual_end_time', 'interviewers', 'scheduled_by', 'cancelled_by'
        ]


class InterviewFeedbackFilter(django_filters.FilterSet):
    """
    Filter for InterviewFeedback model.
    """
    interview = django_filters.NumberFilter()
    interviewer = django_filters.NumberFilter()
    overall_rating_gte = django_filters.NumberFilter(field_name='overall_rating', lookup_expr='gte')
    overall_rating_lte = django_filters.NumberFilter(field_name='overall_rating', lookup_expr='lte')
    recommendation = django_filters.ChoiceFilter(choices=InterviewFeedback.RECOMMENDATION_CHOICES)
    submitted_at_gte = django_filters.DateTimeFilter(field_name='submitted_at', lookup_expr='gte')
    submitted_at_lte = django_filters.DateTimeFilter(field_name='submitted_at', lookup_expr='lte')
    
    class Meta:
        model = InterviewFeedback
        fields = [
            'interview', 'interviewer', 'overall_rating', 'recommendation', 'submitted_at'
        ]


class OfferFilter(django_filters.FilterSet):
    """
    Filter for Offer model.
    """
    application = django_filters.NumberFilter()
    offer_date_gte = django_filters.DateFilter(field_name='offer_date', lookup_expr='gte')
    offer_date_lte = django_filters.DateFilter(field_name='offer_date', lookup_expr='lte')
    expiry_date_gte = django_filters.DateFilter(field_name='expiry_date', lookup_expr='gte')
    expiry_date_lte = django_filters.DateFilter(field_name='expiry_date', lookup_expr='lte')
    position = django_filters.CharFilter(lookup_expr='icontains')
    department = django_filters.CharFilter(lookup_expr='icontains')
    location = django_filters.CharFilter(lookup_expr='icontains')
    employment_type = django_filters.CharFilter(lookup_expr='icontains')
    base_salary_gte = django_filters.NumberFilter(field_name='base_salary', lookup_expr='gte')
    base_salary_lte = django_filters.NumberFilter(field_name='base_salary', lookup_expr='lte')
    joining_date_gte = django_filters.DateFilter(field_name='joining_date', lookup_expr='gte')
    joining_date_lte = django_filters.DateFilter(field_name='joining_date', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=Offer.STATUS_CHOICES)
    approved_by = django_filters.NumberFilter()
    approval_date_gte = django_filters.DateFilter(field_name='approval_date', lookup_expr='gte')
    approval_date_lte = django_filters.DateFilter(field_name='approval_date', lookup_expr='lte')
    response_date_gte = django_filters.DateFilter(field_name='response_date', lookup_expr='gte')
    response_date_lte = django_filters.DateFilter(field_name='response_date', lookup_expr='lte')
    withdrawn_by = django_filters.NumberFilter()
    withdrawal_date_gte = django_filters.DateFilter(field_name='withdrawal_date', lookup_expr='gte')
    withdrawal_date_lte = django_filters.DateFilter(field_name='withdrawal_date', lookup_expr='lte')
    
    class Meta:
        model = Offer
        fields = [
            'application', 'offer_date', 'expiry_date', 'position', 'department', 'location',
            'employment_type', 'base_salary', 'joining_date', 'status', 'approved_by', 'approval_date',
            'response_date', 'withdrawn_by', 'withdrawal_date'
        ]


class OnboardingFilter(django_filters.FilterSet):
    """
    Filter for Onboarding model.
    """
    offer = django_filters.NumberFilter()
    candidate = django_filters.NumberFilter()
    start_date_gte = django_filters.DateFilter(field_name='start_date', lookup_expr='gte')
    start_date_lte = django_filters.DateFilter(field_name='start_date', lookup_expr='lte')
    end_date_gte = django_filters.DateFilter(field_name='end_date', lookup_expr='gte')
    end_date_lte = django_filters.DateFilter(field_name='end_date', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=Onboarding.STATUS_CHOICES)
    hr_buddy = django_filters.NumberFilter()
    manager = django_filters.NumberFilter()
    documents_submitted = django_filters.BooleanFilter()
    documents_verified = django_filters.BooleanFilter()
    equipment_assigned = django_filters.BooleanFilter()
    system_access_provided = django_filters.BooleanFilter()
    orientation_completed = django_filters.BooleanFilter()
    training_completed = django_filters.BooleanFilter()
    
    class Meta:
        model = Onboarding
        fields = [
            'offer', 'candidate', 'start_date', 'end_date', 'status', 'hr_buddy', 'manager',
            'documents_submitted', 'documents_verified', 'equipment_assigned', 'system_access_provided',
            'orientation_completed', 'training_completed'
        ]


class RecruitmentSourceFilter(django_filters.FilterSet):
    """
    Filter for RecruitmentSource model.
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    code = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    source_type = django_filters.ChoiceFilter(choices=RecruitmentSource.SOURCE_TYPE_CHOICES)
    website = django_filters.CharFilter(lookup_expr='icontains')
    contact_person = django_filters.CharFilter(lookup_expr='icontains')
    contact_email = django_filters.CharFilter(lookup_expr='icontains')
    integration_active = django_filters.BooleanFilter()
    cost_per_post_gte = django_filters.NumberFilter(field_name='cost_per_post', lookup_expr='gte')
    cost_per_post_lte = django_filters.NumberFilter(field_name='cost_per_post', lookup_expr='lte')
    cost_per_hire_gte = django_filters.NumberFilter(field_name='cost_per_hire', lookup_expr='gte')
    cost_per_hire_lte = django_filters.NumberFilter(field_name='cost_per_hire', lookup_expr='lte')
    currency = django_filters.CharFilter()
    is_active = django_filters.BooleanFilter()
    
    class Meta:
        model = RecruitmentSource
        fields = [
            'name', 'code', 'organization', 'source_type', 'website', 'contact_person', 'contact_email',
            'integration_active', 'cost_per_post', 'cost_per_hire', 'currency', 'is_active'
        ]


class SkillFilter(django_filters.FilterSet):
    """
    Filter for Skill model.
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    code = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    skill_type = django_filters.ChoiceFilter(choices=Skill.SKILL_TYPE_CHOICES)
    is_active = django_filters.BooleanFilter()
    parent_skill = django_filters.NumberFilter()
    
    class Meta:
        model = Skill
        fields = [
            'name', 'code', 'organization', 'skill_type', 'is_active', 'parent_skill'
        ]
