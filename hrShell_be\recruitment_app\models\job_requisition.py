from django.db import models
from django.core.validators import MinValueValidator
from organization_app.models import Organization, BusinessUnit, Department, Designation, Location
from employees_app.models.employee import Employee


class JobRequisition(models.Model):
    """
    Model to represent job requisitions created by managers or HR.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending_approval', 'Pending Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('on_hold', 'On Hold'),
        ('cancelled', 'Cancelled'),
        ('closed', 'Closed'),
    ]

    EMPLOYMENT_TYPE_CHOICES = [
        ('full_time', 'Full Time'),
        ('part_time', 'Part Time'),
        ('contract', 'Contract'),
        ('internship', 'Internship'),
        ('temporary', 'Temporary'),
    ]

    EXPERIENCE_LEVEL_CHOICES = [
        ('entry', 'Entry Level'),
        ('junior', 'Junior'),
        ('mid', 'Mid Level'),
        ('senior', 'Senior'),
        ('lead', 'Lead'),
        ('manager', 'Manager'),
        ('director', 'Director'),
        ('executive', 'Executive'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    # Basic Information
    title = models.CharField(max_length=255)
    code = models.CharField(max_length=50, unique=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='job_requisitions')
    business_unit = models.ForeignKey(BusinessUnit, on_delete=models.CASCADE, related_name='job_requisitions')
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='job_requisitions')
    designation = models.ForeignKey(Designation, on_delete=models.CASCADE, related_name='job_requisitions')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='job_requisitions')
    employment_type = models.CharField(max_length=20, choices=EMPLOYMENT_TYPE_CHOICES)
    experience_level = models.CharField(max_length=20, choices=EXPERIENCE_LEVEL_CHOICES)
    min_experience = models.PositiveIntegerField(default=0)
    max_experience = models.PositiveIntegerField(null=True, blank=True)
    min_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], null=True, blank=True)
    max_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], null=True, blank=True)

    # Requisition Details
    number_of_openings = models.PositiveIntegerField(default=1)
    description = models.TextField()
    requirements = models.TextField()
    responsibilities = models.TextField()
    qualifications = models.TextField()
    skills_required = models.TextField()
    benefits = models.TextField(blank=True, null=True)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')

    # Dates and Status
    requested_date = models.DateField(auto_now_add=True)
    target_hiring_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')

    # Stakeholders
    requested_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='requisitions_requested')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='requisitions_approved')
    approval_date = models.DateField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, null=True)

    # Replacement Information
    is_replacement = models.BooleanField(default=False)
    replacing_employee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='replacement_requisitions')

    # Budget Information
    budget_approved = models.BooleanField(default=False)
    budget_code = models.CharField(max_length=50, blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-requested_date', 'status']
        verbose_name = 'Job Requisition'
        verbose_name_plural = 'Job Requisitions'

    def __str__(self):
        return f"{self.code} - {self.title}"
