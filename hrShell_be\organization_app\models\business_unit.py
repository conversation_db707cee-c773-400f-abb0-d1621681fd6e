from django.db import models
from enum import Enum


class BusinessUnitStatus(Enum):
    ACTIVE = 'active'
    INACTIVE = 'inactive'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class BusinessUnit(models.Model):
    """
    BusinessUnit model for storing divisions or business units
    """
    # Basic information
    name = models.CharField(max_length=200)
    organization = models.ForeignKey(
        'organization_app.Organization',
        on_delete=models.CASCADE,
        related_name='business_units'
    )
    
    # Details
    description = models.TextField(blank=True, null=True)
    code = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    
    # Hierarchy
    parent = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='children'
    )
    
    # Head of business unit
    head = models.ForeignKey(
        'employees_app.Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_business_units'
    )
    
    # Location
    primary_location = models.ForeignKey(
        'organization_app.Location',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='business_units'
    )
    
    # Status
    status = models.CharField(
        max_length=10,
        choices=BusinessUnitStatus.choices(),
        default=BusinessUnitStatus.ACTIVE.value
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = "Business Unit"
        verbose_name_plural = "Business Units"
        unique_together = ('organization', 'name')
    
    def __str__(self):
        return f"{self.name} ({self.organization.name})"
    
    @property
    def department_count(self):
        return self.departments.count()
