from django.db import models
from enum import Enum


class LeaveTypeStatus(Enum):
    ACTIVE = 'active'
    INACTIVE = 'inactive'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class LeaveType(models.Model):
    """
    Leave Type model for storing different types of leave
    """
    # Basic information
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    
    # Organization
    organization = models.ForeignKey(
        'organization_app.Organization',
        on_delete=models.CASCADE,
        related_name='leave_types'
    )
    
    # Leave properties
    is_paid = models.BooleanField(default=True)
    max_days_per_year = models.PositiveIntegerField(default=0)
    min_days_per_request = models.PositiveIntegerField(default=1)
    max_days_per_request = models.PositiveIntegerField(default=0)
    
    # Advanced properties
    carry_forward_allowed = models.BooleanField(default=False)
    max_carry_forward_days = models.PositiveIntegerField(default=0)
    encashment_allowed = models.BooleanField(default=False)
    max_encashment_days = models.PositiveIntegerField(default=0)
    
    # Probation period
    applicable_during_probation = models.BooleanField(default=False)
    probation_period_percentage = models.PositiveIntegerField(
        default=0,
        help_text="Percentage of leave allowed during probation (0-100)"
    )
    
    # Documentation
    requires_documentation = models.BooleanField(default=False)
    documentation_instructions = models.TextField(blank=True, null=True)
    
    # Status
    status = models.CharField(
        max_length=10,
        choices=LeaveTypeStatus.choices(),
        default=LeaveTypeStatus.ACTIVE.value
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = "Leave Type"
        verbose_name_plural = "Leave Types"
        unique_together = ('organization', 'name')
    
    def __str__(self):
        return f"{self.name} ({self.organization.name})"
