from django.contrib import admin
from organization_app.models.organization import Organization
from organization_app.models.location import Location
from organization_app.models.business_unit import BusinessUnit
from organization_app.models.designation import Designation
from organization_app.models.organization_policy import OrganizationPolicy
from organization_app.models.organization_document import OrganizationDocument


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ('name', 'registration_number', 'business_type', 'status')
    list_filter = ('business_type', 'status')
    search_fields = ('name', 'registration_number', 'gst_number', 'pan_number', 'ein_number')


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'city', 'state', 'country', 'is_headquarters', 'status')
    list_filter = ('country', 'state', 'is_headquarters', 'status')
    search_fields = ('name', 'city', 'address_line1', 'postal_code')


@admin.register(BusinessUnit)
class BusinessUnitAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'code', 'parent', 'head', 'status')
    list_filter = ('organization', 'status')
    search_fields = ('name', 'code', 'description')


@admin.register(Designation)
class DesignationAdmin(admin.ModelAdmin):
    list_display = ('title', 'organization', 'level', 'department', 'pay_grade')
    list_filter = ('organization', 'level', 'department')
    search_fields = ('title', 'description')


@admin.register(OrganizationPolicy)
class OrganizationPolicyAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'policy_type', 'effective_from', 'effective_to')
    list_filter = ('organization', 'policy_type')
    search_fields = ('name', 'description', 'content')


@admin.register(OrganizationDocument)
class OrganizationDocumentAdmin(admin.ModelAdmin):
    list_display = ('title', 'organization', 'document_type', 'version', 'effective_date', 'expiry_date')
    list_filter = ('organization', 'document_type')
    search_fields = ('title', 'description')
