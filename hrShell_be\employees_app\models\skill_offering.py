from django.db import models


class SkillLevel(models.TextChoices):
    BEGINNER = 'beginner', 'Beginner'
    INTERMEDIATE = 'intermediate', 'Intermediate'
    ADVANCED = 'advanced', 'Advanced'
    EXPERT = 'expert', 'Expert'


class SkillType(models.TextChoices):
    TECHNICAL = 'technical', 'Technical'
    SOFT = 'soft', 'Soft Skill'
    DOMAIN = 'domain', 'Domain Knowledge'
    LANGUAGE = 'language', 'Language'
    CERTIFICATION = 'certification', 'Certification'


class SkillOffering(models.Model):
    """
    Skill offering model for tracking employee skills
    """
    employee = models.ForeignKey(
        'Employee',
        on_delete=models.CASCADE,
        related_name='skills'
    )
    skill_name = models.CharField(max_length=100)
    skill_level = models.CharField(
        max_length=20,
        choices=SkillLevel.choices
    )
    years_of_experience = models.DecimalField(max_digits=5, decimal_places=2)
    is_primary_skill = models.BooleanField(default=False)
    department = models.ForeignKey(
        'Department',
        on_delete=models.SET_NULL,
        null=True,
        related_name='department_skills'
    )
    actual_skill_type = models.CharField(
        max_length=20,
        choices=SkillType.choices
    )

    class Meta:
        unique_together = ['employee', 'skill_name']
        ordering = ['-is_primary_skill', 'skill_name']
        verbose_name = "Skill Offering"
        verbose_name_plural = "Skill Offerings"

    def __str__(self):
        return f"{self.employee} - {self.skill_name} ({self.get_skill_level_display()})"
