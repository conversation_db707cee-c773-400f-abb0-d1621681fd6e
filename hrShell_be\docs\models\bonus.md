# Bonus Models

## Bonus

The Bonus model represents bonuses and incentives given to employees.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `employee` | ForeignKey | Employee this bonus is for | Yes |
| `bonus_type` | String | Type of bonus | Yes |
| `amount` | Decimal | Bonus amount | Yes |
| `payment_date` | Date | Date of payment | Yes |
| `reference_period_start` | Date | Start date of the reference period | No |
| `reference_period_end` | Date | End date of the reference period | No |
| `status` | String | Status of the bonus | Yes (default: draft) |
| `description` | Text | Description of the bonus | No |
| `is_taxable` | Boolean | Whether the bonus is taxable | Yes (default: True) |
| `is_prorated` | Boolean | Whether the bonus is prorated | Yes (default: False) |
| `approved_by` | ForeignKey | Employee who approved the bonus | No |
| `approval_date` | Date | Date of approval | No |
| `rejection_reason` | Text | Reason for rejection | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Bonus Type Choices
- `performance`: Performance Bonus
- `annual`: Annual Bonus
- `festival`: Festival Bonus
- `incentive`: Sales Incentive
- `joining`: Joining Bonus
- `retention`: Retention Bonus
- `referral`: Referral Bonus
- `other`: Other

#### Status Choices
- `draft`: Draft
- `pending`: Pending Approval
- `approved`: Approved
- `rejected`: Rejected
- `paid`: Paid
- `cancelled`: Cancelled

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `employee` | Employee | The employee this bonus is for |
| `approved_by` | Employee | The employee who approved the bonus |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, bonus type, and amount |

### Example

```python
bonus = Bonus.objects.create(
    employee=john_doe,
    bonus_type="performance",
    amount=50000.00,
    payment_date=date(2023, 6, 1),
    reference_period_start=date(2022, 4, 1),
    reference_period_end=date(2023, 3, 31),
    status="draft",
    description="Annual performance bonus for FY 2022-23",
    is_taxable=True,
    is_prorated=False
)
```

## BonusBatch

The BonusBatch model represents a batch of bonuses for multiple employees.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the batch | Yes |
| `organization` | ForeignKey | Organization this batch belongs to | Yes |
| `bonus_type` | String | Type of bonus | Yes |
| `payment_date` | Date | Date of payment | Yes |
| `reference_period_start` | Date | Start date of the reference period | No |
| `reference_period_end` | Date | End date of the reference period | No |
| `status` | String | Status of the batch | Yes (default: draft) |
| `description` | Text | Description of the batch | No |
| `total_amount` | Decimal | Total amount of all bonuses in the batch | Yes (default: 0) |
| `created_by` | ForeignKey | Employee who created the batch | No |
| `approved_by` | ForeignKey | Employee who approved the batch | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Bonus Type Choices
- Same as Bonus model

#### Status Choices
- `draft`: Draft
- `processing`: Processing
- `completed`: Completed
- `cancelled`: Cancelled

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this batch belongs to |
| `created_by` | Employee | The employee who created the batch |
| `approved_by` | Employee | The employee who approved the batch |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name, bonus type, and payment date |

### Example

```python
bonus_batch = BonusBatch.objects.create(
    name="Annual Performance Bonus 2023",
    organization=acme_corp,
    bonus_type="performance",
    payment_date=date(2023, 6, 1),
    reference_period_start=date(2022, 4, 1),
    reference_period_end=date(2023, 3, 31),
    status="draft",
    description="Annual performance bonus for FY 2022-23",
    created_by=jane_smith
)
```

## API Endpoints

### Bonus Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/bonuses/` | GET | List all bonuses |
| `/api/v1/bonuses/` | POST | Create a new bonus |
| `/api/v1/bonuses/{id}/` | GET | Retrieve a specific bonus |
| `/api/v1/bonuses/{id}/` | PUT | Update a specific bonus |
| `/api/v1/bonuses/{id}/` | DELETE | Delete a specific bonus |
| `/api/v1/bonuses/status/{status}/` | GET | List all bonuses with a specific status |
| `/api/v1/bonuses/employee/{employee_id}/` | GET | List all bonuses for a specific employee |
| `/api/v1/bonuses/type/{bonus_type}/` | GET | List all bonuses of a specific type |
| `/api/v1/bonuses/{id}/approve/` | POST | Approve a specific bonus |
| `/api/v1/bonuses/{id}/reject/` | POST | Reject a specific bonus |
| `/api/v1/bonuses/{id}/mark-paid/` | POST | Mark a specific bonus as paid |
| `/api/v1/bonuses/create-batch/` | POST | Create bonuses for multiple employees in a batch |

### BonusBatch Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/bonus-batches/` | GET | List all bonus batches |
| `/api/v1/bonus-batches/` | POST | Create a new bonus batch |
| `/api/v1/bonus-batches/{id}/` | GET | Retrieve a specific bonus batch |
| `/api/v1/bonus-batches/{id}/` | PUT | Update a specific bonus batch |
| `/api/v1/bonus-batches/{id}/` | DELETE | Delete a specific bonus batch |
| `/api/v1/bonus-batches/status/{status}/` | GET | List all bonus batches with a specific status |
| `/api/v1/bonus-batches/organization/{organization_id}/` | GET | List all bonus batches for a specific organization |
| `/api/v1/bonus-batches/{id}/bonuses/` | GET | List all bonuses for a specific batch |
| `/api/v1/bonus-batches/{id}/process/` | POST | Process a bonus batch by creating individual bonuses |

## Bonus Process

1. **Bonus Creation**: Create individual bonuses or a bonus batch
2. **Bonus Approval**: HR/Finance approves or rejects the bonuses
3. **Bonus Payment**: Approved bonuses are paid to employees
4. **Tax Calculation**: Taxable bonuses are included in tax calculations

## Bonus Types and Calculations

### Performance Bonus
- Based on employee performance
- Usually a percentage of annual salary
- Formula: `Annual Salary * Performance Rating * Bonus Percentage`

### Annual Bonus
- Fixed amount or percentage of annual salary
- Paid annually
- Formula: `Annual Salary * Bonus Percentage`

### Festival Bonus
- Fixed amount or percentage of monthly salary
- Paid during festivals
- Formula: `Monthly Salary * Bonus Percentage`

### Sales Incentive
- Based on sales targets
- Formula: `Sales Amount * Incentive Percentage`

### Joining Bonus
- Fixed amount paid upon joining
- Usually paid in installments
- Formula: `Fixed Amount`

### Retention Bonus
- Fixed amount or percentage of annual salary
- Paid to retain employees
- Formula: `Annual Salary * Retention Percentage`

### Referral Bonus
- Fixed amount paid for referring new employees
- Usually paid after the referred employee completes a probation period
- Formula: `Fixed Amount`
