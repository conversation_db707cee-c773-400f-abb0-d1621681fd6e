"""
Vector operations for HR Shell.
This module provides comprehensive functionality for:
1. Creating vector embeddings from text
2. Storing embeddings in PostgreSQL with pgvector
3. Searching for similar content using vector similarity
4. Processing documents (PDF, text) and storing their embeddings
5. Integrating with Lang<PERSON>hain for advanced vector operations

Usage:
- Use the utility functions directly for custom vector operations
- Use the VectorProcessor class for processing documents
- Use the VectorSearch class for searching the vector database
"""

import os
import sys
import json
import logging
import django
from django.conf import settings
from django.db import connection
import numpy as np
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Try to import optional dependencies
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logging.warning("sentence_transformers not available. Vector embedding functionality will be limited.")

try:
    from langchain_postgres import PGVector
    from langchain_huggingface import HuggingFaceEmbeddings
    from langchain_community.document_loaders import PyPDFLoader, TextLoader
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("langchain_postgres not available. Vector search functionality will be limited.")

# Set up Django environment if not already set
if not os.environ.get('DJANGO_SETTINGS_MODULE'):
    # Add the parent directory to sys.path
    parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, parent_dir)
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hrShell_be.settings')
    django.setup()

# Set up logging
logger = logging.getLogger(__name__)

# Initialize the sentence transformer model
model = None

def get_model():
    """Get or initialize the sentence transformer model"""
    global model
    if not SENTENCE_TRANSFORMERS_AVAILABLE:
        raise ImportError("sentence_transformers is not installed. Please install it with 'pip install sentence-transformers'.")

    if model is None:
        model_name = getattr(settings, 'SENTENCE_TRANSFORMER_MODEL', 'all-MiniLM-L6-v2')
        model = SentenceTransformer(model_name)
    return model

def create_embedding(text):
    """Create a vector embedding from text"""
    model = get_model()
    embedding = model.encode(text)
    return embedding

def format_embedding_for_postgres(embedding):
    """Format a numpy array as a string for PostgreSQL vector type"""
    return f"[{','.join(str(x) for x in embedding)}]"

def get_embedding_dimension():
    """Get the dimension of the embeddings produced by the model"""
    model = get_model()
    # Create a test embedding to determine the dimension
    test_embedding = model.encode("Test")
    return len(test_embedding)

def get_db_connection_params():
    """Get database connection parameters from Django settings"""
    db_settings = settings.DATABASES['default']
    return {
        "dbname": db_settings['NAME'],
        "user": db_settings['USER'],
        "password": db_settings['PASSWORD'],
        "host": db_settings['HOST'],
        "port": db_settings['PORT'],
    }

def get_connection_string():
    """Get database connection string for LangChain"""
    db_settings = settings.DATABASES['default']
    return f"postgresql+psycopg://{db_settings['USER']}:{db_settings['PASSWORD']}@{db_settings['HOST']}:{db_settings['PORT']}/{db_settings['NAME']}"

def ensure_vector_extension():
    """Ensure the pgvector extension is installed"""
    with connection.cursor() as cursor:
        cursor.execute("CREATE EXTENSION IF NOT EXISTS vector;")
        return True

def create_vector_table(table_name, dimension=None):
    """Create a table with a vector column"""
    if dimension is None:
        # Get the actual dimension from the model
        dimension = get_embedding_dimension()

    with connection.cursor() as cursor:
        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id SERIAL PRIMARY KEY,
            content TEXT NOT NULL,
            embedding vector({dimension}) NOT NULL,
            metadata JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        """)

        # Create index for vector search
        cursor.execute(f"""
        CREATE INDEX IF NOT EXISTS {table_name}_embedding_idx
        ON {table_name} USING ivfflat (embedding vector_l2_ops);
        """)

        return True

def insert_vector(table_name, content, metadata=None):
    """Insert content with vector embedding into the database"""
    # Create embedding
    embedding = create_embedding(content)
    embedding_str = format_embedding_for_postgres(embedding)

    # Insert into database
    with connection.cursor() as cursor:
        if metadata:
            metadata_json = json.dumps(metadata)
            cursor.execute(f"""
            INSERT INTO {table_name} (content, embedding, metadata)
            VALUES (%s, %s, %s)
            RETURNING id;
            """, [content, embedding_str, metadata_json])
        else:
            cursor.execute(f"""
            INSERT INTO {table_name} (content, embedding)
            VALUES (%s, %s)
            RETURNING id;
            """, [content, embedding_str])

        result = cursor.fetchone()
        return result[0] if result else None

def search_similar(table_name, query, limit=5):
    """Search for similar content using vector similarity"""
    # Create embedding for query
    query_embedding = create_embedding(query)
    query_embedding_str = format_embedding_for_postgres(query_embedding)

    # Search database
    with connection.cursor() as cursor:
        cursor.execute(f"""
        SELECT id, content, metadata, embedding <-> %s as distance
        FROM {table_name}
        ORDER BY distance ASC
        LIMIT %s;
        """, [query_embedding_str, limit])

        results = []
        for row in cursor.fetchall():
            id, content, metadata, distance = row
            results.append({
                'id': id,
                'content': content,
                'metadata': metadata,
                'distance': distance
            })

        return results

class VectorProcessor:
    """Process documents and store their embeddings in the vector database"""

    def __init__(self, collection_name="hr_shell_collection"):
        """Initialize the vector processor"""
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("langchain_postgres is not installed. Please install it with 'pip install langchain-postgres langchain-huggingface'.")

        # Ensure pgvector extension is installed
        ensure_vector_extension()

        # Initialize embeddings
        self.embeddings = HuggingFaceEmbeddings(model_name=settings.SENTENCE_TRANSFORMER_MODEL)

        # Initialize vector store
        connection_string = get_connection_string()
        self.collection_name = collection_name

        self.vector_store = PGVector(
            embeddings=self.embeddings,
            collection_name=self.collection_name,
            connection=connection_string,
            use_jsonb=True,
        )
        logger.info(f"Vector store initialized with collection: {collection_name}")

    def process_pdf(self, pdf_path, metadata=None):
        """Process a PDF file and store its embeddings"""
        try:
            # Load the PDF file
            logger.info(f"Loading PDF from: {pdf_path}")
            loader = PyPDFLoader(pdf_path)
            pages = loader.load()

            # Add metadata if provided
            if metadata:
                for page in pages:
                    page.metadata.update(metadata)

            # Add documents to the vector store
            logger.info(f"Adding {len(pages)} pages to vector store")
            self.vector_store.add_documents(pages)
            logger.info("Documents added successfully")

            return len(pages)
        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}")
            raise

    def process_text(self, text, metadata=None):
        """Process text and store its embedding"""
        try:
            # Create a document from the text
            from langchain_core.documents import Document
            doc = Document(page_content=text, metadata=metadata or {})

            # Add document to the vector store
            logger.info("Adding text to vector store")
            self.vector_store.add_documents([doc])
            logger.info("Text added successfully")

            return 1
        except Exception as e:
            logger.error(f"Error processing text: {str(e)}")
            raise

class VectorSearch:
    """Search for similar content in the vector database"""

    def __init__(self, collection_name="hr_shell_collection"):
        """Initialize the vector search"""
        if not LANGCHAIN_AVAILABLE:
            logger.warning("langchain_postgres is not installed. Using fallback search method.")
            self.use_fallback = True
            self.collection_name = collection_name
            return

        # Initialize embeddings
        self.use_fallback = False
        self.embeddings = HuggingFaceEmbeddings(model_name=settings.SENTENCE_TRANSFORMER_MODEL)

        # Initialize vector store
        connection_string = get_connection_string()
        self.collection_name = collection_name

        try:
            self.vector_store = PGVector(
                embeddings=self.embeddings,
                collection_name=self.collection_name,
                connection=connection_string,
                use_jsonb=True,
            )
            logger.info(f"Vector search initialized with collection: {collection_name}")
        except Exception as e:
            logger.error(f"Error initializing vector store: {str(e)}")
            logger.warning("Using fallback search method.")
            self.use_fallback = True

    def search(self, query, limit=5):
        """Search for similar content"""
        if self.use_fallback:
            logger.info("Using fallback search method")
            # Return empty results for now
            return []

        try:
            # Search for similar documents
            docs = self.vector_store.similarity_search(query, k=limit)

            results = []
            for doc in docs:
                results.append({
                    'content': doc.page_content,
                    'metadata': doc.metadata,
                    'distance': 0.0  # PGVector doesn't return distances directly
                })

            return results
        except Exception as e:
            logger.error(f"Error searching vector database: {str(e)}")
            return []

# Example usage
if __name__ == "__main__":
    # Process a PDF file
    try:
        # Get the path to the sample PDF
        pdf_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "sample.pdf")

        if os.path.exists(pdf_path):
            # Process the PDF
            processor = VectorProcessor()
            num_pages = processor.process_pdf(pdf_path, metadata={"source": "sample.pdf"})
            print(f"Processed {num_pages} pages from {pdf_path}")

            # Search for similar content
            search = VectorSearch()
            results = search.search("HR management system")

            print("\nSearch results:")
            for i, result in enumerate(results):
                print(f"Result {i+1}:")
                print(f"Content: {result['content'][:100]}...")
                print(f"Metadata: {result['metadata']}")
                print()
        else:
            print(f"PDF file not found: {pdf_path}")
    except Exception as e:
        print(f"Error: {str(e)}")
