# Swagger Endpoint Grouping Implementation Guide

This guide provides comprehensive instructions for organizing Django REST Framework API endpoints into logical groups in Swagger UI documentation using drf-yasg.

## Overview

Your HR Shell API endpoints are now organized into the following logical groups:

1. **Authentication** - Login, registration, user management
2. **Employee Management** - Employee CRUD, departments, skills, documents  
3. **Organization Structure** - Organizations, locations, business units, designations
4. **Leave Management** - Leave types, policies, requests, approvals
5. **Payroll Management** - Salary structures, payroll processing, components
6. **Recruitment & Hiring** - Job requisitions, applications, interviews
7. **Onboarding & Offboarding** - Workflows, tasks, document management
8. **AI Assistant** - Vector chat, knowledge base queries
9. **System** - Health checks, utilities

## Implementation Methods

### Method 1: Class-Level Tagging (Recommended)

Use the `@swagger_auto_schema` decorator at the class level to tag all endpoints in a ViewSet:

```python
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

@swagger_auto_schema(
    tags=['Leave Management'],
    operation_description="Leave type management endpoints"
)
class LeaveTypeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing leave types
    """
    # Your viewset implementation
```

### Method 2: Method-Level Tagging

For individual methods or custom actions, use method-level decorators:

```python
@swagger_auto_schema(
    operation_description="Get all active leave types",
    operation_summary="List Active Leave Types",
    responses={
        200: openapi.Response(
            description="List of active leave types",
            schema=LeaveTypeSerializer(many=True)
        )
    },
    tags=['Leave Management']
)
@action(detail=False, methods=['get'])
def active_leave_types(self, request):
    # Your method implementation
```

### Method 3: Function-Based Views

For function-based views:

```python
@swagger_auto_schema(
    method='get',
    tags=['System'],
    operation_description="Check if the API is up and running",
    responses={
        200: openapi.Response(
            description="API is healthy",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='ok')
                }
            )
        )
    }
)
@api_view(['GET'])
def health_check(request):
    return Response({"status": "ok"})
```

## Complete Implementation Examples

### 1. Authentication Views

```python
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class LoginView(TokenObtainPairView):
    @swagger_auto_schema(
        operation_summary="User Login",
        operation_description="Authenticate user with username and password",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['username', 'password'],
            properties={
                'username': openapi.Schema(type=openapi.TYPE_STRING),
                'password': openapi.Schema(type=openapi.TYPE_STRING, format='password'),
            }
        ),
        responses={
            200: openapi.Response(description="Login successful"),
            400: openapi.Response(description="Invalid credentials")
        },
        tags=['Authentication']
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)
```

### 2. Employee Management Views

```python
@swagger_auto_schema(
    tags=['Employee Management'],
    operation_description="Employee management endpoints"
)
class EmployeeViewSet(viewsets.ModelViewSet):
    queryset = Employee.objects.all()
    serializer_class = EmployeeSerializer
    
    @swagger_auto_schema(
        operation_summary="List Active Employees",
        tags=['Employee Management']
    )
    @action(detail=False, methods=['get'])
    def active(self, request):
        # Implementation
```

### 3. Payroll Management Views

```python
@swagger_auto_schema(
    tags=['Payroll Management'],
    operation_description="Payroll management endpoints"
)
class PayrollViewSet(viewsets.ModelViewSet):
    queryset = Payroll.objects.all()
    serializer_class = PayrollSerializer
```

## Current Implementation Status

The following views have been updated with proper tagging:

✅ **Completed:**
- `employees_app/views/employee_views.py` - Employee Management
- `employees_app/views/auth_views.py` - Authentication  
- `organization_app/views/organization_views.py` - Organization Structure
- `leave_app/views/leave_type_views.py` - Leave Management
- `payroll_app/views/payroll_views.py` - Payroll Management
- `recruitment_app/views/job_requisition_views.py` - Recruitment & Hiring
- `common/views/ai_assistant_views.py` - AI Assistant
- `common/views/health_check_view.py` - System

🔄 **Remaining to be updated:**
- Other leave management views (leave_policy, leave_request, etc.)
- Other payroll views (salary_structure, employee_salary, etc.)
- Other recruitment views (application, candidate, interview, etc.)
- Onboarding & offboarding views
- Other employee views (department, employee_detail, etc.)
- Organization views (location, designation, business_unit, etc.)

## Benefits of This Implementation

1. **Organized Documentation**: Endpoints are grouped logically in Swagger UI
2. **Better Developer Experience**: Easier to find and understand related endpoints
3. **Consistent Structure**: All endpoints follow the same tagging pattern
4. **Maintainable**: Easy to add new endpoints to existing groups
5. **Professional Appearance**: Clean, organized API documentation

## Swagger UI Display

After implementation, your Swagger UI will display endpoints grouped under collapsible sections:

```
📁 Authentication
  POST /api/auth/login/
  POST /api/auth/register/

📁 Employee Management  
  GET /api/v1/employees/
  POST /api/v1/employees/
  GET /api/v1/employees/active/

📁 Leave Management
  GET /api/v1/leave-types/
  POST /api/v1/leave-types/
  GET /api/v1/leave-types/active_leave_types/

📁 Payroll Management
  GET /api/v1/payrolls/
  POST /api/v1/payrolls/
  
📁 System
  GET /health/
```

## Next Steps

1. **Apply to Remaining Views**: Update all remaining views with appropriate tags
2. **Test Documentation**: Visit `/swagger-ui/` to verify grouping works correctly
3. **Add Detailed Schemas**: Enhance request/response schemas for better documentation
4. **Update Integration Guide**: Document the new grouping in your integration guides

## Configuration

Your current drf-yasg configuration in `common/swagger.py` supports this grouping automatically. The `tagsSorter: "alpha"` setting in your Swagger UI template ensures tags are sorted alphabetically.

## Troubleshooting

- **Tags not appearing**: Ensure you've imported `swagger_auto_schema` and `openapi`
- **Endpoints not grouped**: Check that the `tags` parameter is correctly specified
- **Duplicate endpoints**: Make sure you're not mixing class-level and method-level tags incorrectly
