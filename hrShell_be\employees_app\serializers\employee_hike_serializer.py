from rest_framework import serializers
from employees_app.models.employee_hike import EmployeeHike, HikeStatus
from employees_app.serializers.employee_serializer import EmployeeListSerializer


class EmployeeHikeSerializer(serializers.ModelSerializer):
    """
    Serializer for the EmployeeHike model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    department_name = serializers.ReadOnlyField(source='department.name')
    approved_by_name = serializers.ReadOnlyField(source='approved_by.full_name')
    status_display = serializers.ReadOnlyField(source='get_status_display')

    class Meta:
        model = EmployeeHike
        fields = [
            'id', 'employee', 'employee_name', 'hike_percentage',
            'hike_effective_date', 'hike_reason', 'department',
            'department_name', 'previous_salary', 'new_salary',
            'status', 'status_display', 'approved_by', 'approved_by_name'
        ]

    def validate_hike_percentage(self, value):
        """
        Validate that the hike percentage is within a reasonable range
        """
        if value < 0:
            raise serializers.ValidationError("Hike percentage cannot be negative.")
        if value > 100:
            raise serializers.ValidationError("Hike percentage cannot exceed 100%.")
        return value

    def validate(self, data):
        """
        Validate that the new salary is greater than the previous salary
        """
        if 'previous_salary' in data and 'new_salary' in data:
            if data['new_salary'] <= data['previous_salary']:
                raise serializers.ValidationError("New salary must be greater than previous salary.")

            # Verify that the hike percentage matches the salary difference
            if 'hike_percentage' in data:
                expected_new_salary = data['previous_salary'] * (1 + data['hike_percentage'] / 100)
                if abs(data['new_salary'] - expected_new_salary) > 0.01:  # Allow for small rounding differences
                    raise serializers.ValidationError("Hike percentage does not match the salary difference.")

        return data


class EmployeeHikeListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for listing employee hikes
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    department_name = serializers.ReadOnlyField(source='department.name')
    status_display = serializers.ReadOnlyField(source='get_status_display')

    class Meta:
        model = EmployeeHike
        fields = [
            'id', 'employee_name', 'hike_percentage',
            'hike_effective_date', 'department_name',
            'previous_salary', 'new_salary', 'status', 'status_display'
        ]


class EmployeeHikeDetailSerializer(EmployeeHikeSerializer):
    """
    Detailed serializer for the EmployeeHike model
    """
    employee = EmployeeListSerializer(read_only=True)

    class Meta(EmployeeHikeSerializer.Meta):
        pass
