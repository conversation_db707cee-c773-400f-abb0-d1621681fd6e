from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.utils import timezone
from organization_app.models.organization import Organization
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from leave_app.models.leave_type import LeaveType
from leave_app.models.leave_policy import LeavePolicy
from leave_app.models.leave_balance import LeaveBalance
from leave_app.models.leave_request import LeaveRequest
from leave_app.models.leave_approval import LeaveApproval
from leave_app.models.holiday import Holiday
from leave_app.models.week_off import WeekOff
from datetime import date, timedelta
import json


class LeaveTypeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test organization
        cls.organization = Organization.objects.create(
            name="Test Organization",
            registration_number="TEST123",
            business_type="private_limited"
        )
        
        # Create test leave types
        cls.leave_type1 = LeaveType.objects.create(
            name="Casual Leave",
            code="CL",
            description="Leave for personal matters",
            organization=cls.organization,
            is_paid=True,
            max_days_per_year=12,
            status="active"
        )
        
        cls.leave_type2 = LeaveType.objects.create(
            name="Sick Leave",
            code="SL",
            description="Leave for health issues",
            organization=cls.organization,
            is_paid=True,
            max_days_per_year=10,
            status="active"
        )
    
    def setUp(self):
        self.client = APIClient()
        # Add authentication if needed
        # self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + token)
    
    def test_list_leave_types(self):
        url = reverse('leave:leavetype-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_retrieve_leave_type(self):
        url = reverse('leave:leavetype-detail', args=[self.leave_type1.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Casual Leave')
        self.assertEqual(response.data['code'], 'CL')
    
    def test_create_leave_type(self):
        url = reverse('leave:leavetype-list')
        data = {
            'name': 'Annual Leave',
            'code': 'AL',
            'description': 'Annual paid leave',
            'organization': self.organization.id,
            'is_paid': True,
            'max_days_per_year': 20,
            'status': 'active'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(LeaveType.objects.count(), 3)
        self.assertEqual(LeaveType.objects.get(code='AL').name, 'Annual Leave')
    
    def test_update_leave_type(self):
        url = reverse('leave:leavetype-detail', args=[self.leave_type1.id])
        data = {
            'name': 'Casual Leave Updated',
            'code': 'CL',
            'description': 'Updated description',
            'organization': self.organization.id,
            'is_paid': True,
            'max_days_per_year': 15,
            'status': 'active'
        }
        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.leave_type1.refresh_from_db()
        self.assertEqual(self.leave_type1.name, 'Casual Leave Updated')
        self.assertEqual(self.leave_type1.max_days_per_year, 15)
    
    def test_delete_leave_type(self):
        url = reverse('leave:leavetype-detail', args=[self.leave_type2.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(LeaveType.objects.count(), 1)
    
    def test_active_leave_types(self):
        url = reverse('leave:leavetype-active-leave-types')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
        
        # Change one leave type to inactive
        self.leave_type2.status = 'inactive'
        self.leave_type2.save()
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)


# Add more test classes for other views as needed
