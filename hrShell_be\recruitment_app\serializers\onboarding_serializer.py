from rest_framework import serializers
from recruitment_app.models.onboarding import Onboarding, OnboardingTask
from recruitment_app.serializers.offer_serializer import OfferSerializer
from recruitment_app.serializers.candidate_serializer import CandidateSerializer
from employees_app.serializers.employee_serializer import EmployeeSerializer


class OnboardingTaskSerializer(serializers.ModelSerializer):
    """
    Serializer for the OnboardingTask model.
    """
    assigned_to_details = EmployeeSerializer(source='assigned_to', read_only=True)
    completed_by_details = EmployeeSerializer(source='completed_by', read_only=True)

    class Meta:
        model = OnboardingTask
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
        ref_name = 'RecruitmentOnboardingTask'


class OnboardingSerializer(serializers.ModelSerializer):
    """
    Serializer for the Onboarding model.
    """
    offer_details = OfferSerializer(source='offer', read_only=True)
    candidate_details = CandidateSerializer(source='candidate', read_only=True)
    hr_buddy_details = EmployeeSerializer(source='hr_buddy', read_only=True)
    manager_details = EmployeeSerializer(source='manager', read_only=True)
    tasks = OnboardingTaskSerializer(many=True, read_only=True)

    class Meta:
        model = Onboarding
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

    def validate(self, data):
        """
        Validate the onboarding data.
        """
        # Validate start and end dates
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("Start date cannot be later than end date.")

        return data

    def create(self, validated_data):
        """
        Create a new onboarding record with default tasks.
        """
        onboarding = Onboarding.objects.create(**validated_data)

        # Create default onboarding tasks
        default_tasks = [
            {
                'title': 'Complete Employment Forms',
                'description': 'Complete all required employment forms including tax forms, benefits enrollment, etc.',
                'category': 'documentation',
                'due_date': onboarding.start_date,
            },
            {
                'title': 'Prepare Workstation',
                'description': 'Set up computer, desk, and other necessary equipment.',
                'category': 'equipment',
                'due_date': onboarding.start_date,
            },
            {
                'title': 'System Access Setup',
                'description': 'Create accounts and provide access to necessary systems and tools.',
                'category': 'access',
                'due_date': onboarding.start_date,
            },
            {
                'title': 'Orientation Session',
                'description': 'Schedule and conduct orientation session to introduce company policies, culture, etc.',
                'category': 'training',
                'due_date': onboarding.start_date,
            },
            {
                'title': 'Team Introduction',
                'description': 'Introduce the new employee to their team and key stakeholders.',
                'category': 'introduction',
                'due_date': onboarding.start_date,
            },
        ]

        for task_data in default_tasks:
            OnboardingTask.objects.create(onboarding=onboarding, **task_data)

        return onboarding

    def update(self, instance, validated_data):
        """
        Update an onboarding record and handle status changes.
        """
        old_status = instance.status

        # Update the onboarding record
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Handle status changes
        if instance.status != old_status and instance.status == 'completed':
            # Create employee record if onboarding is completed
            # This would typically be handled by a signal or a separate API call
            pass

        instance.save()
        return instance
