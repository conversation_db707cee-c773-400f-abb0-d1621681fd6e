from django.contrib import admin
from onboarding_offboarding_app.models.onboarding import OnboardingPlan, OnboardingTask
from onboarding_offboarding_app.models.offboarding import OffboardingRequest, OffboardingTask
from onboarding_offboarding_app.models.document import Document, DocumentTemplate
from onboarding_offboarding_app.models.feedback import ExitFeedback


class OnboardingTaskInline(admin.TabularInline):
    model = OnboardingTask
    extra = 1
    fields = ('name', 'category', 'status', 'due_date', 'completed_date', 'assigned_to', 'is_mandatory')


@admin.register(OnboardingPlan)
class OnboardingPlanAdmin(admin.ModelAdmin):
    list_display = ('employee', 'status', 'start_date', 'end_date', 'department', 'mentor')
    list_filter = ('status', 'department', 'welcome_email_sent', 'orientation_completed', 'it_setup_completed')
    search_fields = ('employee__first_name', 'employee__last_name', 'employee__email', 'notes')
    date_hierarchy = 'start_date'
    inlines = [OnboardingTaskInline]
    fieldsets = (
        ('Basic Information', {
            'fields': ('employee', 'status', 'start_date', 'end_date', 'department')
        }),
        ('Stakeholders', {
            'fields': ('mentor', 'assigned_by')
        }),
        ('Progress', {
            'fields': ('welcome_email_sent', 'welcome_email_sent_at', 'orientation_completed', 'orientation_completed_at', 'it_setup_completed', 'it_setup_completed_at')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
    )


@admin.register(OnboardingTask)
class OnboardingTaskAdmin(admin.ModelAdmin):
    list_display = ('name', 'plan', 'category', 'status', 'due_date', 'completed_date', 'assigned_to')
    list_filter = ('status', 'category', 'is_mandatory')
    search_fields = ('name', 'description', 'notes', 'plan__employee__first_name', 'plan__employee__last_name')
    date_hierarchy = 'due_date'
    fieldsets = (
        ('Basic Information', {
            'fields': ('plan', 'name', 'description', 'category')
        }),
        ('Task Details', {
            'fields': ('status', 'due_date', 'completed_date', 'sequence', 'is_mandatory')
        }),
        ('Assignment', {
            'fields': ('assigned_to', 'assigned_to_role', 'completed_by')
        }),
        ('Document', {
            'fields': ('document',)
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
    )


class OffboardingTaskInline(admin.TabularInline):
    model = OffboardingTask
    extra = 1
    fields = ('name', 'category', 'status', 'due_date', 'completed_date', 'assigned_to', 'is_mandatory')


@admin.register(OffboardingRequest)
class OffboardingRequestAdmin(admin.ModelAdmin):
    list_display = ('employee', 'status', 'exit_type', 'resignation_date', 'last_working_day', 'department', 'manager')
    list_filter = ('status', 'exit_type', 'department', 'rehire_eligible', 'exit_interview_scheduled', 'final_settlement_processed')
    search_fields = ('employee__first_name', 'employee__last_name', 'employee__email', 'exit_reason', 'notes')
    date_hierarchy = 'resignation_date'
    inlines = [OffboardingTaskInline]
    fieldsets = (
        ('Basic Information', {
            'fields': ('employee', 'status', 'exit_type', 'resignation_date', 'last_working_day', 'notice_period_days')
        }),
        ('Stakeholders', {
            'fields': ('department', 'manager', 'approved_by', 'approval_date')
        }),
        ('Reason for Leaving', {
            'fields': ('exit_reason', 'exit_reason_category', 'rehire_eligible')
        }),
        ('Exit Interview', {
            'fields': ('exit_interview_scheduled', 'exit_interview_date')
        }),
        ('Final Settlement', {
            'fields': ('final_settlement_processed', 'final_settlement_date', 'final_settlement_amount')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
    )


@admin.register(OffboardingTask)
class OffboardingTaskAdmin(admin.ModelAdmin):
    list_display = ('name', 'request', 'category', 'status', 'due_date', 'completed_date', 'assigned_to')
    list_filter = ('status', 'category', 'is_mandatory')
    search_fields = ('name', 'description', 'notes', 'request__employee__first_name', 'request__employee__last_name')
    date_hierarchy = 'due_date'
    fieldsets = (
        ('Basic Information', {
            'fields': ('request', 'name', 'description', 'category')
        }),
        ('Task Details', {
            'fields': ('status', 'due_date', 'completed_date', 'sequence', 'is_mandatory')
        }),
        ('Assignment', {
            'fields': ('assigned_to', 'assigned_to_role', 'completed_by')
        }),
        ('Document', {
            'fields': ('document',)
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
    )


@admin.register(DocumentTemplate)
class DocumentTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'document_type', 'is_active')
    list_filter = ('document_type', 'is_active', 'organization')
    search_fields = ('name', 'description')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'organization', 'document_type')
        }),
        ('Template Content', {
            'fields': ('template_file', 'template_content')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ('name', 'employee', 'document_type', 'status', 'verified_by', 'verified_at')
    list_filter = ('document_type', 'status')
    search_fields = ('name', 'description', 'employee__first_name', 'employee__last_name')
    date_hierarchy = 'created_at'
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'employee', 'document_type')
        }),
        ('Template', {
            'fields': ('template',)
        }),
        ('Document Content', {
            'fields': ('document_file',)
        }),
        ('Status', {
            'fields': ('status', 'verified_by', 'verified_at', 'rejection_reason')
        }),
    )


@admin.register(ExitFeedback)
class ExitFeedbackAdmin(admin.ModelAdmin):
    list_display = ('employee', 'offboarding_request', 'overall_experience', 'would_recommend', 'would_return', 'interview_date')
    list_filter = ('would_recommend', 'would_return')
    search_fields = ('employee__first_name', 'employee__last_name', 'reason_for_leaving', 'what_did_you_like', 'what_could_be_improved', 'additional_comments')
    date_hierarchy = 'interview_date'
    fieldsets = (
        ('Basic Information', {
            'fields': ('employee', 'offboarding_request')
        }),
        ('Feedback on Company', {
            'fields': ('overall_experience', 'work_environment', 'work_life_balance', 'compensation_benefits', 'career_growth', 'management')
        }),
        ('Detailed Feedback', {
            'fields': ('reason_for_leaving', 'what_did_you_like', 'what_could_be_improved', 'would_recommend', 'would_return')
        }),
        ('Additional Comments', {
            'fields': ('additional_comments',)
        }),
        ('Interview Details', {
            'fields': ('conducted_by', 'interview_date')
        }),
    )
