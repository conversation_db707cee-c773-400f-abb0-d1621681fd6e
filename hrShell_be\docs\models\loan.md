# Loan Models

## Loan

The Loan model represents loans given to employees.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `employee` | ForeignKey | Employee this loan is for | Yes |
| `loan_type` | String | Type of loan | Yes |
| `loan_amount` | Decimal | Loan amount | Yes |
| `interest_rate` | Decimal | Interest rate (%) | Yes (default: 0) |
| `term_months` | Integer | Term in months | Yes |
| `emi_amount` | Decimal | EMI amount | Yes |
| `start_date` | Date | Start date of the loan | Yes |
| `end_date` | Date | End date of the loan | Yes |
| `remaining_amount` | Decimal | Remaining amount to be paid | Yes |
| `status` | String | Status of the loan | Yes (default: pending) |
| `purpose` | Text | Purpose of the loan | No |
| `approved_by` | ForeignKey | Employee who approved the loan | No |
| `approval_date` | Date | Date of approval | No |
| `rejection_reason` | Text | Reason for rejection | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Loan Type Choices
- `personal`: Personal Loan
- `home`: Home Loan
- `vehicle`: Vehicle Loan
- `education`: Education Loan
- `medical`: Medical Loan
- `advance`: Salary Advance
- `other`: Other

#### Status Choices
- `pending`: Pending Approval
- `approved`: Approved
- `rejected`: Rejected
- `active`: Active
- `closed`: Closed
- `defaulted`: Defaulted

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `employee` | Employee | The employee this loan is for |
| `approved_by` | Employee | The employee who approved the loan |
| `installments` | LoanInstallment | The installments for this loan |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, loan type, and loan amount |

### Example

```python
loan = Loan.objects.create(
    employee=john_doe,
    loan_type="personal",
    loan_amount=100000.00,
    interest_rate=5.00,
    term_months=10,
    emi_amount=10500.00,
    start_date=date(2023, 5, 1),
    end_date=date(2024, 2, 28),
    remaining_amount=100000.00,
    status="pending",
    purpose="Personal emergency"
)
```

## LoanInstallment

The LoanInstallment model tracks loan installment payments.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `loan` | ForeignKey | Loan this installment belongs to | Yes |
| `installment_number` | Integer | Installment number | Yes |
| `due_date` | Date | Due date of the installment | Yes |
| `amount` | Decimal | Installment amount | Yes |
| `principal_amount` | Decimal | Principal amount | Yes |
| `interest_amount` | Decimal | Interest amount | Yes |
| `paid_amount` | Decimal | Paid amount | Yes (default: 0) |
| `payment_date` | Date | Date of payment | No |
| `status` | String | Status of the installment | Yes (default: pending) |
| `remarks` | Text | Any remarks about this installment | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Status Choices
- `pending`: Pending
- `paid`: Paid
- `missed`: Missed
- `partial`: Partially Paid

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `loan` | Loan | The loan this installment belongs to |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, installment number, and amount |

### Example

```python
installment = LoanInstallment.objects.create(
    loan=loan,
    installment_number=1,
    due_date=date(2023, 5, 31),
    amount=10500.00,
    principal_amount=10000.00,
    interest_amount=500.00,
    status="pending"
)
```

## API Endpoints

### Loan Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/loans/` | GET | List all loans |
| `/api/v1/loans/` | POST | Create a new loan |
| `/api/v1/loans/{id}/` | GET | Retrieve a specific loan |
| `/api/v1/loans/{id}/` | PUT | Update a specific loan |
| `/api/v1/loans/{id}/` | DELETE | Delete a specific loan |
| `/api/v1/loans/status/{status}/` | GET | List all loans with a specific status |
| `/api/v1/loans/employee/{employee_id}/` | GET | List all loans for a specific employee |
| `/api/v1/loans/active/` | GET | List all active loans |
| `/api/v1/loans/{id}/installments/` | GET | List all installments for a specific loan |
| `/api/v1/loans/{id}/approve/` | POST | Approve a specific loan |
| `/api/v1/loans/{id}/reject/` | POST | Reject a specific loan |
| `/api/v1/loans/{id}/activate/` | POST | Activate a specific loan and generate installments |
| `/api/v1/loans/{id}/close/` | POST | Close a specific loan |

### LoanInstallment Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/loan-installments/` | GET | List all loan installments |
| `/api/v1/loan-installments/` | POST | Create a new loan installment |
| `/api/v1/loan-installments/{id}/` | GET | Retrieve a specific loan installment |
| `/api/v1/loan-installments/{id}/` | PUT | Update a specific loan installment |
| `/api/v1/loan-installments/{id}/` | DELETE | Delete a specific loan installment |
| `/api/v1/loan-installments/status/{status}/` | GET | List all loan installments with a specific status |
| `/api/v1/loan-installments/loan/{loan_id}/` | GET | List all installments for a specific loan |
| `/api/v1/loan-installments/employee/{employee_id}/` | GET | List all installments for a specific employee |
| `/api/v1/loan-installments/due/` | GET | List all installments due in the current month |
| `/api/v1/loan-installments/{id}/pay/` | POST | Mark an installment as paid |

## Loan Process

1. **Loan Application**: Employee applies for a loan
2. **Loan Approval**: HR/Finance approves or rejects the loan
3. **Loan Activation**: If approved, the loan is activated and installments are generated
4. **Loan Repayment**: Employee repays the loan in installments
5. **Loan Closure**: Once all installments are paid, the loan is closed

## Loan Calculation

### EMI Calculation

The EMI (Equated Monthly Installment) is calculated using the formula:

```
EMI = [P x R x (1+R)^N] / [(1+R)^N - 1]
```

Where:
- P = Principal amount (loan amount)
- R = Rate of interest per month (annual interest rate / 12 / 100)
- N = Number of installments (term in months)

### Amortization Schedule

The amortization schedule shows the breakdown of each installment into principal and interest:

```
Interest for month M = Remaining principal at the end of month M-1 x R
Principal for month M = EMI - Interest for month M
Remaining principal at the end of month M = Remaining principal at the end of month M-1 - Principal for month M
```

## Loan Deduction from Salary

When a loan is active, the EMI amount is automatically deducted from the employee's salary each month. This is done by adding a deduction component to the employee's payroll item during payroll processing.
