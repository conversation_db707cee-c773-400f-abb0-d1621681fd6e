# Generated by Django 5.1.5 on 2025-05-03 20:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees_app', '0010_alter_employeehike_hike_effective_date_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('resume', models.FileField(blank=True, null=True, upload_to='employee_documents/resumes/')),
                ('contract', models.FileField(blank=True, null=True, upload_to='employee_documents/contracts/')),
                ('signed_offer_letter', models.FileField(blank=True, null=True, upload_to='employee_documents/offer_letters/')),
                ('experience_certificates', models.FileField(blank=True, null=True, upload_to='employee_documents/experience_certificates/')),
                ('qualification_certificates', models.<PERSON><PERSON>ield(blank=True, null=True, upload_to='employee_documents/qualification_certificates/')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Employee Document',
                'verbose_name_plural': 'Employee Documents',
            },
        ),
        migrations.CreateModel(
            name='EmployeeLeave',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('leave_type', models.CharField(choices=[('casual', 'Casual Leave'), ('sick', 'Sick Leave'), ('annual', 'Annual Leave'), ('maternity', 'Maternity Leave'), ('paternity', 'Paternity Leave'), ('unpaid', 'Unpaid Leave')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('total_days', models.IntegerField(editable=False)),
                ('reason', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leaves', to='employees_app.employee')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leaves', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Employee Leave',
                'verbose_name_plural': 'Employee Leaves',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='JobHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_job_title', models.CharField(max_length=100)),
                ('previous_department', models.CharField(max_length=100)),
                ('previous_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('reason', models.TextField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_job_changes', to='employees_app.employee')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_history', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Job History',
                'verbose_name_plural': 'Job Histories',
                'ordering': ['-end_date'],
            },
        ),
        migrations.CreateModel(
            name='Salary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('basic', models.DecimalField(decimal_places=2, max_digits=10)),
                ('allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('deduction', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('net_amount_payable', models.DecimalField(decimal_places=2, editable=False, max_digits=10)),
                ('payment_method', models.CharField(choices=[('bank_transfer', 'Bank Transfer'), ('check', 'Check'), ('cash', 'Cash')], default='bank_transfer', max_length=20)),
                ('bank_account_details', models.TextField(blank=True, null=True)),
                ('salary_effective_date', models.DateField()),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salaries', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Salary',
                'verbose_name_plural': 'Salaries',
                'ordering': ['-salary_effective_date'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('check_in', models.TimeField(blank=True, null=True)),
                ('check_out', models.TimeField(blank=True, null=True)),
                ('total_hour', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent'), ('half_day', 'Half Day'), ('leave', 'Leave')], default='present', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Employee Attendance',
                'verbose_name_plural': 'Employee Attendances',
                'ordering': ['-date'],
                'unique_together': {('employee', 'date')},
            },
        ),
        migrations.CreateModel(
            name='SkillOffering',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('skill_name', models.CharField(max_length=100)),
                ('skill_level', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced'), ('expert', 'Expert')], max_length=20)),
                ('years_of_experience', models.DecimalField(decimal_places=2, max_digits=5)),
                ('is_primary_skill', models.BooleanField(default=False)),
                ('actual_skill_type', models.CharField(choices=[('technical', 'Technical'), ('soft', 'Soft Skill'), ('domain', 'Domain Knowledge'), ('language', 'Language'), ('certification', 'Certification')], max_length=20)),
                ('department', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='department_skills', to='employees_app.department')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skills', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Skill Offering',
                'verbose_name_plural': 'Skill Offerings',
                'ordering': ['-is_primary_skill', 'skill_name'],
                'unique_together': {('employee', 'skill_name')},
            },
        ),
    ]
