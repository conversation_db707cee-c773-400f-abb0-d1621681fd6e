import os
import re
import json
from django.conf import settings
from django.urls import URLPattern, URLResolver, get_resolver
from django.core.management import call_command
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hrShell_be.settings')
django.setup()

def get_all_urls(urlpatterns, base=''):
    """
    Recursively extract all URLs from URLPattern and URLResolver objects.
    """
    urls = []
    for pattern in urlpatterns:
        if isinstance(pattern, URLPattern):
            # Extract the URL pattern
            pattern_str = str(pattern.pattern)
            # Remove regex syntax
            pattern_str = pattern_str.replace('^', '').replace('$', '')
            # Combine with base URL
            url = base + pattern_str
            # Add to list
            urls.append({
                'url': url,
                'name': pattern.name,
                'view': str(pattern.callback.__name__) if pattern.callback else ''
            })
        elif isinstance(pattern, URLResolver):
            # Recursively process URL resolvers
            resolver_base = base + str(pattern.pattern)
            urls.extend(get_all_urls(pattern.url_patterns, resolver_base))
    return urls

def main():
    """
    Main function to extract and print all URLs.
    """
    # Get the URL resolver
    resolver = get_resolver()
    
    # Extract all URLs
    all_urls = get_all_urls(resolver.url_patterns)
    
    # Group URLs by app
    urls_by_app = {}
    for url_info in all_urls:
        url = url_info['url']
        
        # Skip admin URLs
        if url.startswith('admin/'):
            continue
            
        # Determine the app
        app = 'other'
        if 'api/v1/' in url:
            parts = url.split('api/v1/')
            if len(parts) > 1:
                path = parts[1].split('/')
                if path and path[0]:
                    app = path[0]
        elif 'api/token' in url:
            app = 'auth'
        elif 'swagger' in url or 'redoc' in url:
            app = 'docs'
        
        # Add to the appropriate app group
        if app not in urls_by_app:
            urls_by_app[app] = []
        urls_by_app[app].append(url_info)
    
    # Print URLs by app
    for app, urls in sorted(urls_by_app.items()):
        print(f"\n## {app.upper()} Endpoints")
        for url_info in sorted(urls, key=lambda x: x['url']):
            print(f"- {url_info['url']} ({url_info['view']})")

if __name__ == '__main__':
    main()
