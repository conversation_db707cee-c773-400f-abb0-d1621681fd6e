from rest_framework import serializers
from payroll_app.models.loan import Loan, LoanInstallment


class LoanInstallmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = LoanInstallment
        fields = [
            'id', 'loan', 'installment_number', 'due_date', 'amount',
            'principal_amount', 'interest_amount', 'paid_amount', 'payment_date',
            'status', 'remarks', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class LoanSerializer(serializers.ModelSerializer):
    employee_name = serializers.SerializerMethodField()
    loan_type_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    approved_by_name = serializers.SerializerMethodField()
    installments = LoanInstallmentSerializer(many=True, read_only=True)
    
    class Meta:
        model = Loan
        fields = [
            'id', 'employee', 'employee_name', 'loan_type', 'loan_type_display',
            'loan_amount', 'interest_rate', 'term_months', 'emi_amount', 'start_date',
            'end_date', 'remaining_amount', 'status', 'status_display', 'purpose',
            'approved_by', 'approved_by_name', 'approval_date', 'rejection_reason',
            'installments', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'installments']
    
    def get_employee_name(self, obj):
        return f"{obj.employee.first_name} {obj.employee.last_name}" if obj.employee else None
    
    def get_loan_type_display(self, obj):
        return obj.get_loan_type_display()
    
    def get_status_display(self, obj):
        return obj.get_status_display()
    
    def get_approved_by_name(self, obj):
        if not obj.approved_by:
            return None
        return f"{obj.approved_by.first_name} {obj.approved_by.last_name}"
    
    def validate(self, data):
        # Validate loan amount and EMI
        if data.get('loan_amount', 0) <= 0:
            raise serializers.ValidationError({"loan_amount": "Loan amount must be greater than 0"})
        
        if data.get('emi_amount', 0) <= 0:
            raise serializers.ValidationError({"emi_amount": "EMI amount must be greater than 0"})
        
        if data.get('emi_amount', 0) > data.get('loan_amount', 0):
            raise serializers.ValidationError({"emi_amount": "EMI amount cannot be greater than loan amount"})
        
        # Validate term months
        if data.get('term_months', 0) <= 0:
            raise serializers.ValidationError({"term_months": "Term months must be greater than 0"})
        
        # Validate dates
        if 'start_date' in data and 'end_date' in data:
            if data['start_date'] >= data['end_date']:
                raise serializers.ValidationError({"end_date": "End date must be after start date"})
        
        return data
