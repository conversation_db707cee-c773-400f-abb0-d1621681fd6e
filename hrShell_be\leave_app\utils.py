from datetime import timedelta
from django.utils import timezone
from leave_app.models.holiday import Holiday
from leave_app.models.week_off import WeekO<PERSON>


def get_working_days(start_date, end_date, employee=None, organization=None):
    """
    Calculate the number of working days between two dates, excluding holidays and week offs.
    
    Args:
        start_date: Start date
        end_date: End date
        employee: Employee object (optional)
        organization: Organization object (optional)
    
    Returns:
        float: Number of working days
    """
    if start_date > end_date:
        return 0
    
    # Get organization from employee if not provided
    if not organization and employee and hasattr(employee, 'department') and employee.department:
        if hasattr(employee.department, 'business_unit') and employee.department.business_unit:
            if hasattr(employee.department.business_unit, 'organization'):
                organization = employee.department.business_unit.organization
    
    # Get location from employee if available
    location = None
    if employee and hasattr(employee, 'department') and employee.department:
        if hasattr(employee.department, 'business_unit') and employee.department.business_unit:
            if hasattr(employee.department.business_unit, 'primary_location'):
                location = employee.department.business_unit.primary_location
    
    # Get department from employee if available
    department = None
    if employee and hasattr(employee, 'department'):
        department = employee.department
    
    # Get holidays for the date range
    holidays = []
    if organization:
        holiday_query = Holiday.objects.filter(
            organization=organization,
            date__range=[start_date, end_date]
        )
        
        if location:
            holiday_query = holiday_query.filter(location=location) | holiday_query.filter(location__isnull=True)
        else:
            holiday_query = holiday_query.filter(location__isnull=True)
        
        holidays = list(holiday_query.values_list('date', 'is_half_day'))
        
        # Also get recurring holidays from previous years
        recurring_holidays = Holiday.objects.filter(
            organization=organization,
            is_recurring=True,
            date__lt=start_date
        )
        
        if location:
            recurring_holidays = recurring_holidays.filter(location=location) | recurring_holidays.filter(location__isnull=True)
        else:
            recurring_holidays = recurring_holidays.filter(location__isnull=True)
        
        for holiday in recurring_holidays:
            # Check if the holiday occurs in the current date range
            holiday_date = holiday.date.replace(year=start_date.year)
            if start_date <= holiday_date <= end_date:
                holidays.append((holiday_date, holiday.is_half_day))
    
    # Get week offs
    week_offs = {}
    if organization:
        week_off_query = WeekOff.objects.filter(organization=organization)
        
        if location:
            week_off_query = week_off_query.filter(location=location) | week_off_query.filter(location__isnull=True)
        else:
            week_off_query = week_off_query.filter(location__isnull=True)
        
        if department:
            week_off_query = week_off_query.filter(department=department) | week_off_query.filter(department__isnull=True)
        else:
            week_off_query = week_off_query.filter(department__isnull=True)
        
        for week_off in week_off_query:
            week_offs[week_off.day_of_week] = week_off.is_half_day
    
    # Calculate working days
    working_days = 0
    current_date = start_date
    
    while current_date <= end_date:
        # Check if it's a holiday
        is_holiday = False
        is_half_day_holiday = False
        
        for holiday_date, is_half_day in holidays:
            if current_date == holiday_date:
                is_holiday = True
                is_half_day_holiday = is_half_day
                break
        
        # Check if it's a week off
        is_week_off = False
        is_half_day_week_off = False
        
        day_of_week = current_date.weekday()
        if day_of_week in week_offs:
            is_week_off = True
            is_half_day_week_off = week_offs[day_of_week]
        
        # Calculate working days
        if is_holiday and not is_half_day_holiday:
            # Full holiday
            pass
        elif is_week_off and not is_half_day_week_off:
            # Full week off
            pass
        elif is_holiday and is_half_day_holiday:
            # Half-day holiday
            working_days += 0.5
        elif is_week_off and is_half_day_week_off:
            # Half-day week off
            working_days += 0.5
        else:
            # Full working day
            working_days += 1
        
        current_date += timedelta(days=1)
    
    return working_days


def calculate_leave_days(start_date, end_date, half_day=False, apply_sandwich_rule=False, employee=None, organization=None):
    """
    Calculate the number of leave days between two dates.
    
    Args:
        start_date: Start date
        end_date: End date
        half_day: Whether this is a half-day leave
        apply_sandwich_rule: Whether to apply the sandwich rule
        employee: Employee object (optional)
        organization: Organization object (optional)
    
    Returns:
        float: Number of leave days
    """
    if half_day:
        return 0.5
    
    if apply_sandwich_rule:
        # If sandwich rule is applied, count all days including holidays and week offs
        delta = end_date - start_date
        return delta.days + 1
    else:
        # Count only working days
        return get_working_days(start_date, end_date, employee, organization)
