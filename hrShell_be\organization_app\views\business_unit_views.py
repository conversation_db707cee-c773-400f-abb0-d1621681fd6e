from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from organization_app.models.business_unit import BusinessUnit
from organization_app.serializers.business_unit_serializer import BusinessUnitSerializer, BusinessUnitDetailSerializer
from organization_app.filters import BusinessUnitFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class BusinessUnitViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Organization Structure'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing BusinessUnit instances.
    
    Additional actions:
    - children: Get child business units for a specific business unit
    - departments: Get departments for a specific business unit
    - org_chart: Get organizational chart for a specific business unit
    """
    queryset = BusinessUnit.objects.all()
    serializer_class = BusinessUnitSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = BusinessUnitFilter
    search_fields = ['name', 'description', 'code']
    ordering_fields = ['name', 'created_at']

    def get_serializer_class(self):
        """
        Return different serializers based on the action
        """
        if self.action == 'retrieve':
            return BusinessUnitDetailSerializer
        return BusinessUnitSerializer
    
    @action(detail=True, methods=['get'])
    def children(self, request, pk=None):
        """
        Get child business units for a specific business unit
        """
        business_unit = self.get_object()
        children = business_unit.children.all()
        serializer = BusinessUnitSerializer(children, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def departments(self, request, pk=None):
        """
        Get departments for a specific business unit
        """
        business_unit = self.get_object()
        from employees_app.serializers.department_serializer import DepartmentSerializer
        departments = business_unit.departments.all()
        serializer = DepartmentSerializer(departments, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_organization(self, request):
        """
        Get business units filtered by organization
        """
        organization_id = request.query_params.get('organization_id')
        if not organization_id:
            return Response(
                {"error": "organization_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        business_units = BusinessUnit.objects.filter(organization_id=organization_id)
        serializer = self.get_serializer(business_units, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def root_units(self, request):
        """
        Get root business units (those without a parent)
        """
        organization_id = request.query_params.get('organization_id')
        if organization_id:
            root_units = BusinessUnit.objects.filter(parent__isnull=True, organization_id=organization_id)
        else:
            root_units = BusinessUnit.objects.filter(parent__isnull=True)
            
        serializer = self.get_serializer(root_units, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def org_chart(self, request, pk=None):
        """
        Get organizational chart for a specific business unit
        """
        business_unit = self.get_object()
        
        def build_org_chart(unit):
            result = BusinessUnitSerializer(unit, context={'request': request}).data
            children = unit.children.all()
            if children:
                result['children'] = [build_org_chart(child) for child in children]
            return result
        
        org_chart = build_org_chart(business_unit)
        return Response(org_chart)
