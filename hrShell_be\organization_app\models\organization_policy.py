from django.db import models
from enum import Enum


class PolicyType(Enum):
    LEAVE = 'leave'
    ATTENDANCE = 'attendance'
    SHIFT = 'shift'
    PAYROLL = 'payroll'
    TRAVEL = 'travel'
    EXPENSE = 'expense'
    GENERAL = 'general'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class OrganizationPolicy(models.Model):
    """
    OrganizationPolicy model for storing policies and their associations
    """
    # Basic information
    name = models.CharField(max_length=200)
    organization = models.ForeignKey(
        'organization_app.Organization',
        on_delete=models.CASCADE,
        related_name='policies'
    )
    
    # Policy details
    description = models.TextField(blank=True, null=True)
    policy_type = models.CharField(
        max_length=20,
        choices=PolicyType.choices(),
        default=PolicyType.GENERAL.value
    )
    
    # Policy content
    content = models.TextField(blank=True, null=True)
    
    # Associations
    applicable_to_all = models.BooleanField(
        default=True,
        help_text="If true, policy applies to all employees"
    )
    
    # Specific associations (only used if applicable_to_all is False)
    locations = models.ManyToManyField(
        'organization_app.Location',
        blank=True,
        related_name='policies'
    )
    departments = models.ManyToManyField(
        'employees_app.Department',
        blank=True,
        related_name='policies'
    )
    business_units = models.ManyToManyField(
        'organization_app.BusinessUnit',
        blank=True,
        related_name='policies'
    )
    
    # Effective dates
    effective_from = models.DateField(null=True, blank=True)
    effective_to = models.DateField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = "Organization Policy"
        verbose_name_plural = "Organization Policies"
        unique_together = ('organization', 'name', 'policy_type')
    
    def __str__(self):
        return f"{self.name} ({self.get_policy_type_display()})"
