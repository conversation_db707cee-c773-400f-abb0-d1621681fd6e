from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from recruitment_app.models.onboarding import Onboarding, OnboardingTask
from recruitment_app.serializers.onboarding_serializer import OnboardingSerializer, OnboardingTaskSerializer
from recruitment_app.filters.recruitment_filters import OnboardingFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class OnboardingViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for onboarding.
    """
    queryset = Onboarding.objects.all()
    serializer_class = OnboardingSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OnboardingFilter
    search_fields = ['candidate__first_name', 'candidate__last_name', 'notes']
    ordering_fields = ['start_date', 'end_date', 'status', 'created_at']
    ordering = ['-start_date']
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all onboarding records with a specific status.
        """
        onboardings = Onboarding.objects.filter(status=status_value)
        page = self.paginate_queryset(onboardings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(onboardings, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def offer(self, request, offer_pk=None):
        """
        Get the onboarding record for a specific offer.
        """
        try:
            onboarding = Onboarding.objects.get(offer_id=offer_pk)
            serializer = self.get_serializer(onboarding)
            return Response(serializer.data)
        except Onboarding.DoesNotExist:
            return Response(
                {"detail": "No onboarding record found for this offer."},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def candidate(self, request, candidate_pk=None):
        """
        Get the onboarding record for a specific candidate.
        """
        try:
            onboarding = Onboarding.objects.get(candidate_id=candidate_pk)
            serializer = self.get_serializer(onboarding)
            return Response(serializer.data)
        except Onboarding.DoesNotExist:
            return Response(
                {"detail": "No onboarding record found for this candidate."},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def hr_buddy(self, request, employee_pk=None):
        """
        Get all onboarding records assigned to a specific HR buddy.
        """
        onboardings = Onboarding.objects.filter(hr_buddy_id=employee_pk)
        page = self.paginate_queryset(onboardings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(onboardings, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def manager(self, request, employee_pk=None):
        """
        Get all onboarding records assigned to a specific manager.
        """
        onboardings = Onboarding.objects.filter(manager_id=employee_pk)
        page = self.paginate_queryset(onboardings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(onboardings, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def tasks(self, request, pk=None):
        """
        Get all tasks for a specific onboarding record.
        """
        onboarding = self.get_object()
        tasks = OnboardingTask.objects.filter(onboarding=onboarding)
        
        serializer = OnboardingTaskSerializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def add_task(self, request, pk=None):
        """
        Add a task to an onboarding record.
        """
        onboarding = self.get_object()
        
        # Validate required fields
        required_fields = ['title', 'category']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {"detail": f"Field '{field}' is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Create task
        task_data = {
            'onboarding': onboarding.id,
            'title': request.data.get('title'),
            'description': request.data.get('description'),
            'category': request.data.get('category'),
            'due_date': request.data.get('due_date'),
            'status': request.data.get('status', 'pending'),
            'assigned_to_id': request.data.get('assigned_to'),
            'notes': request.data.get('notes')
        }
        
        serializer = OnboardingTaskSerializer(data=task_data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """
        Start the onboarding process.
        """
        onboarding = self.get_object()
        
        if onboarding.status != 'pending':
            return Response(
                {"detail": "Only pending onboarding records can be started."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        onboarding.status = 'in_progress'
        onboarding.save()
        
        serializer = self.get_serializer(onboarding)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """
        Complete the onboarding process.
        """
        onboarding = self.get_object()
        
        if onboarding.status != 'in_progress':
            return Response(
                {"detail": "Only in-progress onboarding records can be completed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        onboarding.status = 'completed'
        onboarding.end_date = timezone.now().date()
        onboarding.save()
        
        serializer = self.get_serializer(onboarding)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel the onboarding process.
        """
        onboarding = self.get_object()
        
        if onboarding.status == 'completed':
            return Response(
                {"detail": "Completed onboarding records cannot be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        onboarding.status = 'cancelled'
        onboarding.save()
        
        # Cancel all pending tasks
        OnboardingTask.objects.filter(onboarding=onboarding, status='pending').update(status='cancelled')
        
        serializer = self.get_serializer(onboarding)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def assign_hr_buddy(self, request, pk=None):
        """
        Assign an HR buddy to an onboarding record.
        """
        onboarding = self.get_object()
        
        # Validate required fields
        if 'hr_buddy' not in request.data:
            return Response(
                {"detail": "Field 'hr_buddy' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        onboarding.hr_buddy_id = request.data.get('hr_buddy')
        onboarding.save()
        
        serializer = self.get_serializer(onboarding)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def assign_manager(self, request, pk=None):
        """
        Assign a manager to an onboarding record.
        """
        onboarding = self.get_object()
        
        # Validate required fields
        if 'manager' not in request.data:
            return Response(
                {"detail": "Field 'manager' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        onboarding.manager_id = request.data.get('manager')
        onboarding.save()
        
        serializer = self.get_serializer(onboarding)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def update_document_status(self, request, pk=None):
        """
        Update document status for an onboarding record.
        """
        onboarding = self.get_object()
        
        if 'documents_submitted' in request.data:
            onboarding.documents_submitted = request.data.get('documents_submitted')
        
        if 'documents_verified' in request.data:
            onboarding.documents_verified = request.data.get('documents_verified')
        
        onboarding.save()
        
        serializer = self.get_serializer(onboarding)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def update_equipment_status(self, request, pk=None):
        """
        Update equipment status for an onboarding record.
        """
        onboarding = self.get_object()
        
        if 'equipment_assigned' in request.data:
            onboarding.equipment_assigned = request.data.get('equipment_assigned')
        
        if 'system_access_provided' in request.data:
            onboarding.system_access_provided = request.data.get('system_access_provided')
        
        onboarding.save()
        
        serializer = self.get_serializer(onboarding)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def update_training_status(self, request, pk=None):
        """
        Update training status for an onboarding record.
        """
        onboarding = self.get_object()
        
        if 'orientation_completed' in request.data:
            onboarding.orientation_completed = request.data.get('orientation_completed')
        
        if 'training_completed' in request.data:
            onboarding.training_completed = request.data.get('training_completed')
        
        onboarding.save()
        
        serializer = self.get_serializer(onboarding)
        return Response(serializer.data)
