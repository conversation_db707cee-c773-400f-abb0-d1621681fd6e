# Onboarding & Offboarding Module

The Onboarding & Offboarding module helps HR teams manage the complete employee lifecycle, from joining to exit. It centralizes document collection, task assignment, orientation workflows, and exit interviews.

## Onboarding Process

Onboarding seamlessly integrates a new hire into the company by guiding them through orientation, documentation, training, and setup.

### Workflow Stages

#### 1. Pre-Onboarding
- Send welcome email, documents, and portal access
- Assign buddy/mentor
- Collect documents and tax forms (PAN, Aadhaar, etc.)

#### 2. Joining Formalities
- Submission of joining forms
- Verification of documents
- Bank, insurance, PF setup

#### 3. Orientation
- HR session about policies, culture, benefits
- IT asset allocation (laptop, email ID)

#### 4. Department Introduction
- Manager introduces new hire to the team
- Assigns training plan/tasks

#### 5. Checklist Completion
- Complete mandatory modules (e.g., code of conduct)
- Mark status for each task

### Roles Involved

| Role | Responsibilities |
|------|-----------------|
| HR Manager | Initiates onboarding, monitors task progress |
| IT/Admin | Provides systems, email, and devices |
| Manager | Assigns mentor, training, first tasks |
| Employee | Uploads documents, signs forms, completes induction |

## Offboarding Process

Offboarding ensures structured employee exit with documentation, knowledge transfer, feedback collection, and access revocation.

### Workflow Stages

#### 1. Resignation Submitted
- Employee initiates exit request
- Manager/HR reviews and approves

#### 2. Notice Period Management
- Tasks, handovers, training junior staff
- Leave encashment/final payroll adjustment

#### 3. Exit Interviews
- Feedback form or session with HR
- Collect reasons for leaving

#### 4. Asset Recovery & Access Revocation
- Laptop/email/account retrieval
- IT revokes access (VPN, tools)

#### 5. Full & Final Settlement
- Clearance from finance, admin, HR
- Salary, PF, gratuity, leave encashment

#### 6. Experience Letter / Documents
- Provide relieving letter and service certificate

### Roles Involved

| Role | Responsibilities |
|------|-----------------|
| Employee | Initiates exit, completes tasks |
| Manager | Approves resignation, ensures handover |
| IT/Admin | Handles asset return and access removal |
| HR | Handles interview, documents, final settlement |

## Models

### Onboarding
- **OnboardingPlan**: Represents onboarding plans for new employees
- **OnboardingTask**: Represents tasks to be completed during onboarding

### Offboarding
- **OffboardingRequest**: Represents offboarding requests for employees leaving the company
- **OffboardingTask**: Represents tasks to be completed during offboarding

### Documents
- **Document**: Represents documents for onboarding and offboarding
- **DocumentTemplate**: Represents document templates for onboarding and offboarding

### Feedback
- **ExitFeedback**: Represents exit feedback from employees leaving the company

## API Endpoints

### Onboarding Plans
- `GET /api/v1/onboarding-plans/`: List all onboarding plans
- `POST /api/v1/onboarding-plans/`: Create a new onboarding plan
- `GET /api/v1/onboarding-plans/{id}/`: Retrieve a specific onboarding plan
- `PUT /api/v1/onboarding-plans/{id}/`: Update a specific onboarding plan
- `DELETE /api/v1/onboarding-plans/{id}/`: Delete a specific onboarding plan
- `POST /api/v1/onboarding-plans/{id}/start/`: Start an onboarding plan
- `POST /api/v1/onboarding-plans/{id}/complete/`: Complete an onboarding plan
- `POST /api/v1/onboarding-plans/{id}/cancel/`: Cancel an onboarding plan
- `POST /api/v1/onboarding-plans/{id}/send-welcome-email/`: Send welcome email to the employee
- `POST /api/v1/onboarding-plans/{id}/assign-mentor/`: Assign a mentor to an onboarding plan

### Onboarding Tasks
- `GET /api/v1/onboarding-tasks/`: List all onboarding tasks
- `POST /api/v1/onboarding-tasks/`: Create a new onboarding task
- `GET /api/v1/onboarding-tasks/{id}/`: Retrieve a specific onboarding task
- `PUT /api/v1/onboarding-tasks/{id}/`: Update a specific onboarding task
- `DELETE /api/v1/onboarding-tasks/{id}/`: Delete a specific onboarding task
- `POST /api/v1/onboarding-tasks/{id}/start/`: Start an onboarding task
- `POST /api/v1/onboarding-tasks/{id}/complete/`: Complete an onboarding task
- `POST /api/v1/onboarding-tasks/{id}/cancel/`: Cancel an onboarding task
- `POST /api/v1/onboarding-tasks/{id}/assign/`: Assign an onboarding task to an employee

### Offboarding Requests
- `GET /api/v1/offboarding-requests/`: List all offboarding requests
- `POST /api/v1/offboarding-requests/`: Create a new offboarding request
- `GET /api/v1/offboarding-requests/{id}/`: Retrieve a specific offboarding request
- `PUT /api/v1/offboarding-requests/{id}/`: Update a specific offboarding request
- `DELETE /api/v1/offboarding-requests/{id}/`: Delete a specific offboarding request
- `POST /api/v1/offboarding-requests/{id}/approve/`: Approve an offboarding request
- `POST /api/v1/offboarding-requests/{id}/reject/`: Reject an offboarding request
- `POST /api/v1/offboarding-requests/{id}/start/`: Start an offboarding process
- `POST /api/v1/offboarding-requests/{id}/complete/`: Complete an offboarding process
- `POST /api/v1/offboarding-requests/{id}/cancel/`: Cancel an offboarding process
- `POST /api/v1/offboarding-requests/{id}/schedule-exit-interview/`: Schedule an exit interview for an employee
- `POST /api/v1/offboarding-requests/{id}/process-final-settlement/`: Process final settlement for an employee

### Offboarding Tasks
- `GET /api/v1/offboarding-tasks/`: List all offboarding tasks
- `POST /api/v1/offboarding-tasks/`: Create a new offboarding task
- `GET /api/v1/offboarding-tasks/{id}/`: Retrieve a specific offboarding task
- `PUT /api/v1/offboarding-tasks/{id}/`: Update a specific offboarding task
- `DELETE /api/v1/offboarding-tasks/{id}/`: Delete a specific offboarding task
- `POST /api/v1/offboarding-tasks/{id}/start/`: Start an offboarding task
- `POST /api/v1/offboarding-tasks/{id}/complete/`: Complete an offboarding task
- `POST /api/v1/offboarding-tasks/{id}/cancel/`: Cancel an offboarding task
- `POST /api/v1/offboarding-tasks/{id}/mark-not-applicable/`: Mark an offboarding task as not applicable
- `POST /api/v1/offboarding-tasks/{id}/assign/`: Assign an offboarding task to an employee

### Documents
- `GET /api/v1/documents/`: List all documents
- `POST /api/v1/documents/`: Create a new document
- `GET /api/v1/documents/{id}/`: Retrieve a specific document
- `PUT /api/v1/documents/{id}/`: Update a specific document
- `DELETE /api/v1/documents/{id}/`: Delete a specific document
- `POST /api/v1/documents/{id}/submit/`: Submit a document
- `POST /api/v1/documents/{id}/verify/`: Verify a document
- `POST /api/v1/documents/{id}/reject/`: Reject a document

### Document Templates
- `GET /api/v1/document-templates/`: List all document templates
- `POST /api/v1/document-templates/`: Create a new document template
- `GET /api/v1/document-templates/{id}/`: Retrieve a specific document template
- `PUT /api/v1/document-templates/{id}/`: Update a specific document template
- `DELETE /api/v1/document-templates/{id}/`: Delete a specific document template
- `POST /api/v1/document-templates/{id}/activate/`: Activate a document template
- `POST /api/v1/document-templates/{id}/deactivate/`: Deactivate a document template

### Exit Feedback
- `GET /api/v1/exit-feedback/`: List all exit feedback
- `POST /api/v1/exit-feedback/`: Create a new exit feedback
- `GET /api/v1/exit-feedback/{id}/`: Retrieve a specific exit feedback
- `PUT /api/v1/exit-feedback/{id}/`: Update a specific exit feedback
- `DELETE /api/v1/exit-feedback/{id}/`: Delete a specific exit feedback
- `GET /api/v1/exit-feedback/ratings-summary/`: Get a summary of exit feedback ratings

## Key Metrics Tracked

- Time to Productivity
- Onboarding Completion Rate
- Document Submission Rate
- Exit Interview Completion Rate
- Rehire Eligibility Rate
- Average Exit Feedback Ratings

## Integration with Other Modules

The Onboarding & Offboarding Module integrates with:

1. **Employee Module**: For employee information
2. **Organization Module**: For department and organization information
3. **Recruitment Module**: For candidate information during onboarding
4. **Payroll Module**: For final settlement during offboarding
