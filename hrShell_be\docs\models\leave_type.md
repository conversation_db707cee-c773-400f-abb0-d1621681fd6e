# LeaveType Model

The LeaveType model represents different types of leave in the organization, such as Casual Leave, Sick Leave, Annual Leave, etc.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the leave type | Yes |
| `code` | String | Unique code for the leave type | Yes |
| `description` | Text | Description of the leave type | No |
| `organization` | ForeignKey | Organization this leave type belongs to | Yes |
| `is_paid` | Boolean | Whether this is a paid leave type | Yes (default: True) |
| `max_days_per_year` | Integer | Maximum days allowed per year | Yes (default: 0, 0 = unlimited) |
| `min_days_per_request` | Integer | Minimum days per leave request | Yes (default: 1) |
| `max_days_per_request` | Integer | Maximum days per leave request | Yes (default: 0, 0 = unlimited) |
| `carry_forward_allowed` | Boolean | Whether unused leaves can be carried forward | Yes (default: False) |
| `max_carry_forward_days` | Integer | Maximum days that can be carried forward | Yes (default: 0) |
| `encashment_allowed` | Boolean | Whether leaves can be encashed | Yes (default: False) |
| `max_encashment_days` | Integer | Maximum days that can be encashed | Yes (default: 0) |
| `applicable_during_probation` | Boolean | Whether applicable during probation | Yes (default: False) |
| `probation_period_percentage` | Integer | Percentage of leave allowed during probation | Yes (default: 0) |
| `requires_documentation` | Boolean | Whether documentation is required | Yes (default: False) |
| `documentation_instructions` | Text | Instructions for required documentation | No |
| `status` | String | Status of the leave type (choices: active, inactive) | Yes (default: active) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this leave type belongs to |
| `policies` | LeavePolicy | The leave policies associated with this leave type |
| `balances` | LeaveBalance | The leave balances associated with this leave type |
| `requests` | LeaveRequest | The leave requests associated with this leave type |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name of the leave type and its organization |

## Example

```python
leave_type = LeaveType.objects.create(
    name="Casual Leave",
    code="CL",
    description="Leave for personal matters",
    organization=acme_corp,
    is_paid=True,
    max_days_per_year=12,
    min_days_per_request=1,
    max_days_per_request=3,
    carry_forward_allowed=False,
    encashment_allowed=False,
    applicable_during_probation=True,
    probation_period_percentage=50,
    requires_documentation=False,
    status="active"
)
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/leave-types/` | GET | List all leave types |
| `/api/v1/leave-types/` | POST | Create a new leave type |
| `/api/v1/leave-types/{id}/` | GET | Retrieve a specific leave type |
| `/api/v1/leave-types/{id}/` | PUT | Update a specific leave type |
| `/api/v1/leave-types/{id}/` | DELETE | Delete a specific leave type |
| `/api/v1/leave-types/active-leave-types/` | GET | List all active leave types |
| `/api/v1/leave-types/inactive-leave-types/` | GET | List all inactive leave types |
| `/api/v1/leave-types/paid-leave-types/` | GET | List all paid leave types |
| `/api/v1/leave-types/unpaid-leave-types/` | GET | List all unpaid leave types |
