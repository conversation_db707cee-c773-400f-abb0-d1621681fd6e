from django.db import models
from recruitment_app.models.offer import Offer
from recruitment_app.models.candidate import Candidate
from employees_app.models.employee import Employee


class Onboarding(models.Model):
    """
    Model to represent the onboarding process for new hires.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Basic Information
    offer = models.OneToOneField(Offer, on_delete=models.CASCADE, related_name='onboarding')
    candidate = models.OneToOneField(Candidate, on_delete=models.CASCADE, related_name='onboarding')
    
    # Onboarding Details
    start_date = models.DateField()
    end_date = models.DateField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Assigned HR and Manager
    hr_buddy = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='hr_onboardings')
    manager = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='manager_onboardings')
    
    # Documents
    documents_submitted = models.BooleanField(default=False)
    documents_verified = models.BooleanField(default=False)
    
    # Equipment and Access
    equipment_assigned = models.BooleanField(default=False)
    system_access_provided = models.BooleanField(default=False)
    
    # Training
    orientation_completed = models.BooleanField(default=False)
    training_completed = models.BooleanField(default=False)
    
    # Notes
    notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-start_date']
        verbose_name = 'Onboarding'
        verbose_name_plural = 'Onboardings'
    
    def __str__(self):
        return f"Onboarding for {self.candidate}"


class OnboardingTask(models.Model):
    """
    Model to represent tasks to be completed during onboarding.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    CATEGORY_CHOICES = [
        ('documentation', 'Documentation'),
        ('equipment', 'Equipment'),
        ('access', 'System Access'),
        ('training', 'Training'),
        ('introduction', 'Introduction'),
        ('other', 'Other'),
    ]
    
    # Basic Information
    onboarding = models.ForeignKey(Onboarding, on_delete=models.CASCADE, related_name='tasks')
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    
    # Task Details
    due_date = models.DateField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # Assignment
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_onboarding_tasks')
    
    # Completion Information
    completed_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='completed_onboarding_tasks')
    completed_date = models.DateField(blank=True, null=True)
    
    # Notes
    notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['due_date', 'created_at']
        verbose_name = 'Onboarding Task'
        verbose_name_plural = 'Onboarding Tasks'
    
    def __str__(self):
        return f"{self.title} for {self.onboarding.candidate}"
