import django_filters
from django.db.models import Q
from onboarding_offboarding_app.models.onboarding import OnboardingPlan, OnboardingTask
from onboarding_offboarding_app.models.offboarding import OffboardingRequest, OffboardingTask
from onboarding_offboarding_app.models.document import Document, DocumentTemplate
from onboarding_offboarding_app.models.feedback import ExitFeedback


class OnboardingPlanFilter(django_filters.FilterSet):
    """
    Filter for OnboardingPlan model.
    """
    employee = django_filters.NumberFilter()
    status = django_filters.ChoiceFilter(choices=OnboardingPlan.STATUS_CHOICES)
    start_date_gte = django_filters.DateFilter(field_name='start_date', lookup_expr='gte')
    start_date_lte = django_filters.DateFilter(field_name='start_date', lookup_expr='lte')
    end_date_gte = django_filters.DateFilter(field_name='end_date', lookup_expr='gte')
    end_date_lte = django_filters.DateFilter(field_name='end_date', lookup_expr='lte')
    department = django_filters.NumberFilter()
    mentor = django_filters.NumberFilter()
    assigned_by = django_filters.NumberFilter()
    welcome_email_sent = django_filters.BooleanFilter()
    orientation_completed = django_filters.BooleanFilter()
    it_setup_completed = django_filters.BooleanFilter()
    
    class Meta:
        model = OnboardingPlan
        fields = [
            'employee', 'status', 'start_date', 'end_date', 'department', 'mentor', 'assigned_by',
            'welcome_email_sent', 'orientation_completed', 'it_setup_completed'
        ]


class OnboardingTaskFilter(django_filters.FilterSet):
    """
    Filter for OnboardingTask model.
    """
    plan = django_filters.NumberFilter()
    name = django_filters.CharFilter(lookup_expr='icontains')
    category = django_filters.ChoiceFilter(choices=OnboardingTask.CATEGORY_CHOICES)
    status = django_filters.ChoiceFilter(choices=OnboardingTask.STATUS_CHOICES)
    due_date_gte = django_filters.DateFilter(field_name='due_date', lookup_expr='gte')
    due_date_lte = django_filters.DateFilter(field_name='due_date', lookup_expr='lte')
    completed_date_gte = django_filters.DateFilter(field_name='completed_date', lookup_expr='gte')
    completed_date_lte = django_filters.DateFilter(field_name='completed_date', lookup_expr='lte')
    is_mandatory = django_filters.BooleanFilter()
    assigned_to = django_filters.NumberFilter()
    assigned_to_role = django_filters.CharFilter(lookup_expr='icontains')
    completed_by = django_filters.NumberFilter()
    
    class Meta:
        model = OnboardingTask
        fields = [
            'plan', 'name', 'category', 'status', 'due_date', 'completed_date', 'is_mandatory',
            'assigned_to', 'assigned_to_role', 'completed_by'
        ]


class OffboardingRequestFilter(django_filters.FilterSet):
    """
    Filter for OffboardingRequest model.
    """
    employee = django_filters.NumberFilter()
    status = django_filters.ChoiceFilter(choices=OffboardingRequest.STATUS_CHOICES)
    exit_type = django_filters.ChoiceFilter(choices=OffboardingRequest.EXIT_TYPE_CHOICES)
    resignation_date_gte = django_filters.DateFilter(field_name='resignation_date', lookup_expr='gte')
    resignation_date_lte = django_filters.DateFilter(field_name='resignation_date', lookup_expr='lte')
    last_working_day_gte = django_filters.DateFilter(field_name='last_working_day', lookup_expr='gte')
    last_working_day_lte = django_filters.DateFilter(field_name='last_working_day', lookup_expr='lte')
    notice_period_days_gte = django_filters.NumberFilter(field_name='notice_period_days', lookup_expr='gte')
    notice_period_days_lte = django_filters.NumberFilter(field_name='notice_period_days', lookup_expr='lte')
    department = django_filters.NumberFilter()
    manager = django_filters.NumberFilter()
    approved_by = django_filters.NumberFilter()
    approval_date_gte = django_filters.DateFilter(field_name='approval_date', lookup_expr='gte')
    approval_date_lte = django_filters.DateFilter(field_name='approval_date', lookup_expr='lte')
    exit_reason_category = django_filters.CharFilter(lookup_expr='icontains')
    rehire_eligible = django_filters.BooleanFilter()
    exit_interview_scheduled = django_filters.BooleanFilter()
    final_settlement_processed = django_filters.BooleanFilter()
    
    class Meta:
        model = OffboardingRequest
        fields = [
            'employee', 'status', 'exit_type', 'resignation_date', 'last_working_day', 'notice_period_days',
            'department', 'manager', 'approved_by', 'approval_date', 'exit_reason_category', 'rehire_eligible',
            'exit_interview_scheduled', 'final_settlement_processed'
        ]


class OffboardingTaskFilter(django_filters.FilterSet):
    """
    Filter for OffboardingTask model.
    """
    request = django_filters.NumberFilter()
    name = django_filters.CharFilter(lookup_expr='icontains')
    category = django_filters.ChoiceFilter(choices=OffboardingTask.CATEGORY_CHOICES)
    status = django_filters.ChoiceFilter(choices=OffboardingTask.STATUS_CHOICES)
    due_date_gte = django_filters.DateFilter(field_name='due_date', lookup_expr='gte')
    due_date_lte = django_filters.DateFilter(field_name='due_date', lookup_expr='lte')
    completed_date_gte = django_filters.DateFilter(field_name='completed_date', lookup_expr='gte')
    completed_date_lte = django_filters.DateFilter(field_name='completed_date', lookup_expr='lte')
    is_mandatory = django_filters.BooleanFilter()
    assigned_to = django_filters.NumberFilter()
    assigned_to_role = django_filters.CharFilter(lookup_expr='icontains')
    completed_by = django_filters.NumberFilter()
    
    class Meta:
        model = OffboardingTask
        fields = [
            'request', 'name', 'category', 'status', 'due_date', 'completed_date', 'is_mandatory',
            'assigned_to', 'assigned_to_role', 'completed_by'
        ]


class DocumentFilter(django_filters.FilterSet):
    """
    Filter for Document model.
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    employee = django_filters.NumberFilter()
    document_type = django_filters.ChoiceFilter(choices=Document.TYPE_CHOICES)
    template = django_filters.NumberFilter()
    status = django_filters.ChoiceFilter(choices=Document.STATUS_CHOICES)
    verified_by = django_filters.NumberFilter()
    verified_at_gte = django_filters.DateTimeFilter(field_name='verified_at', lookup_expr='gte')
    verified_at_lte = django_filters.DateTimeFilter(field_name='verified_at', lookup_expr='lte')
    
    class Meta:
        model = Document
        fields = [
            'name', 'employee', 'document_type', 'template', 'status', 'verified_by', 'verified_at'
        ]


class DocumentTemplateFilter(django_filters.FilterSet):
    """
    Filter for DocumentTemplate model.
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    document_type = django_filters.ChoiceFilter(choices=DocumentTemplate.TYPE_CHOICES)
    is_active = django_filters.BooleanFilter()
    
    class Meta:
        model = DocumentTemplate
        fields = [
            'name', 'organization', 'document_type', 'is_active'
        ]


class ExitFeedbackFilter(django_filters.FilterSet):
    """
    Filter for ExitFeedback model.
    """
    employee = django_filters.NumberFilter()
    offboarding_request = django_filters.NumberFilter()
    overall_experience_gte = django_filters.NumberFilter(field_name='overall_experience', lookup_expr='gte')
    overall_experience_lte = django_filters.NumberFilter(field_name='overall_experience', lookup_expr='lte')
    work_environment_gte = django_filters.NumberFilter(field_name='work_environment', lookup_expr='gte')
    work_environment_lte = django_filters.NumberFilter(field_name='work_environment', lookup_expr='lte')
    work_life_balance_gte = django_filters.NumberFilter(field_name='work_life_balance', lookup_expr='gte')
    work_life_balance_lte = django_filters.NumberFilter(field_name='work_life_balance', lookup_expr='lte')
    compensation_benefits_gte = django_filters.NumberFilter(field_name='compensation_benefits', lookup_expr='gte')
    compensation_benefits_lte = django_filters.NumberFilter(field_name='compensation_benefits', lookup_expr='lte')
    career_growth_gte = django_filters.NumberFilter(field_name='career_growth', lookup_expr='gte')
    career_growth_lte = django_filters.NumberFilter(field_name='career_growth', lookup_expr='lte')
    management_gte = django_filters.NumberFilter(field_name='management', lookup_expr='gte')
    management_lte = django_filters.NumberFilter(field_name='management', lookup_expr='lte')
    would_recommend = django_filters.BooleanFilter()
    would_return = django_filters.BooleanFilter()
    conducted_by = django_filters.NumberFilter()
    interview_date_gte = django_filters.DateTimeFilter(field_name='interview_date', lookup_expr='gte')
    interview_date_lte = django_filters.DateTimeFilter(field_name='interview_date', lookup_expr='lte')
    
    class Meta:
        model = ExitFeedback
        fields = [
            'employee', 'offboarding_request', 'overall_experience', 'work_environment', 'work_life_balance',
            'compensation_benefits', 'career_growth', 'management', 'would_recommend', 'would_return',
            'conducted_by', 'interview_date'
        ]
