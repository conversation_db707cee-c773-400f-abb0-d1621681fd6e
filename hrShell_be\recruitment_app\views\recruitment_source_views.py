from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from recruitment_app.models.recruitment_source import RecruitmentSource
from recruitment_app.serializers.recruitment_source_serializer import RecruitmentSourceSerializer
from recruitment_app.filters.recruitment_filters import RecruitmentSourceFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class RecruitmentSourceViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for recruitment sources.
    """
    queryset = RecruitmentSource.objects.all()
    serializer_class = RecruitmentSourceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = RecruitmentSourceFilter
    search_fields = ['name', 'code', 'description', 'website', 'contact_person', 'contact_email']
    ordering_fields = ['name', 'source_type', 'cost_per_post', 'cost_per_hire', 'created_at']
    ordering = ['name']
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get all active recruitment sources.
        """
        sources = RecruitmentSource.objects.filter(is_active=True)
        page = self.paginate_queryset(sources)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(sources, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all recruitment sources for a specific organization.
        """
        sources = RecruitmentSource.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(sources)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(sources, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def source_type(self, request, source_type=None):
        """
        Get all recruitment sources of a specific type.
        """
        sources = RecruitmentSource.objects.filter(source_type=source_type)
        page = self.paginate_queryset(sources)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(sources, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def integrated(self, request):
        """
        Get all recruitment sources with active integration.
        """
        sources = RecruitmentSource.objects.filter(integration_active=True)
        page = self.paginate_queryset(sources)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(sources, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """
        Activate a recruitment source.
        """
        source = self.get_object()
        
        if source.is_active:
            return Response(
                {"detail": "Source is already active."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        source.is_active = True
        source.save()
        
        serializer = self.get_serializer(source)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """
        Deactivate a recruitment source.
        """
        source = self.get_object()
        
        if not source.is_active:
            return Response(
                {"detail": "Source is already inactive."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        source.is_active = False
        source.save()
        
        serializer = self.get_serializer(source)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def enable_integration(self, request, pk=None):
        """
        Enable integration for a recruitment source.
        """
        source = self.get_object()
        
        if source.integration_active:
            return Response(
                {"detail": "Integration is already active."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate required fields
        if not source.api_key or not source.api_endpoint:
            return Response(
                {"detail": "API key and endpoint are required for integration."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        source.integration_active = True
        source.save()
        
        serializer = self.get_serializer(source)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def disable_integration(self, request, pk=None):
        """
        Disable integration for a recruitment source.
        """
        source = self.get_object()
        
        if not source.integration_active:
            return Response(
                {"detail": "Integration is already inactive."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        source.integration_active = False
        source.save()
        
        serializer = self.get_serializer(source)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def update_api_key(self, request, pk=None):
        """
        Update API key for a recruitment source.
        """
        source = self.get_object()
        
        # Validate required fields
        if 'api_key' not in request.data:
            return Response(
                {"detail": "Field 'api_key' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        source.api_key = request.data.get('api_key')
        source.save()
        
        serializer = self.get_serializer(source)
        return Response(serializer.data)
