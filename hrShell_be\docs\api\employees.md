# Employees API

This section provides detailed information about the employee-related endpoints in the HR Management API.

## List Employees

Retrieves a list of employees.

```http
GET /api/v1/employees/
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `status` | string | Filter by status (`active` or `inactive`) |
| `department` | integer | Filter by department ID |
| `gender` | string | Filter by gender (`male`, `female`, or `other`) |
| `name` | string | Filter by name (searches in both first and last name) |
| `department_name` | string | Filter by department name |
| `hired_after` | date | Filter by hire date (format: YYYY-MM-DD) |
| `hired_before` | date | Filter by hire date (format: YYYY-MM-DD) |
| `search` | string | Search in first name, last name, email, and position |
| `ordering` | string | Order by field (prefix with `-` for descending order) |

### Response

```json
{
  "count": 10,
  "next": "http://example.com/api/v1/employees/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "full_name": "<PERSON>",
      "email": "<EMAIL>",
      "position": "Software Engineer",
      "department_name": "Engineering",
      "status": "active",
      "profile_picture": "http://example.com/media/employee_images/john_doe.jpg"
    },
    // More employees...
  ]
}
```

## Get Employee

Retrieves a specific employee by ID.

```http
GET /api/v1/employees/{id}/
```

### Response

```json
{
  "id": 1,
  "first_name": "John",
  "last_name": "Doe",
  "full_name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "position": "Software Engineer",
  "department": 1,
  "department_name": "Engineering",
  "hire_date": "2023-01-15",
  "gender": "male",
  "status": "active",
  "profile_picture": "http://example.com/media/employee_images/john_doe.jpg",
  "resume": "http://example.com/media/employee_documents/resumes/john_doe_resume.pdf",
  "contract": "http://example.com/media/employee_documents/contracts/john_doe_contract.pdf",
  "created_at": "2023-01-10T12:00:00Z",
  "updated_at": "2023-01-10T12:00:00Z"
}
```

## Create Employee

Creates a new employee.

```http
POST /api/v1/employees/
Content-Type: application/json
```

### Request Body

```json
{
  "first_name": "Jane",
  "last_name": "Smith",
  "email": "<EMAIL>",
  "phone": "+1987654321",
  "position": "Product Manager",
  "department": 2,
  "hire_date": "2023-02-01",
  "gender": "female",
  "status": "active"
}
```

### Response

```json
{
  "id": 2,
  "first_name": "Jane",
  "last_name": "Smith",
  "full_name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "+1987654321",
  "position": "Product Manager",
  "department": 2,
  "department_name": "Product",
  "hire_date": "2023-02-01",
  "gender": "female",
  "status": "active",
  "profile_picture": null,
  "resume": null,
  "contract": null,
  "created_at": "2023-02-01T12:00:00Z",
  "updated_at": "2023-02-01T12:00:00Z"
}
```

## Update Employee

Updates an existing employee.

```http
PUT /api/v1/employees/{id}/
Content-Type: application/json
```

### Request Body

```json
{
  "first_name": "Jane",
  "last_name": "Smith",
  "email": "<EMAIL>",
  "phone": "+1987654321",
  "position": "Senior Product Manager",
  "department": 2,
  "hire_date": "2023-02-01",
  "gender": "female",
  "status": "active"
}
```

### Response

```json
{
  "id": 2,
  "first_name": "Jane",
  "last_name": "Smith",
  "full_name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "+1987654321",
  "position": "Senior Product Manager",
  "department": 2,
  "department_name": "Product",
  "hire_date": "2023-02-01",
  "gender": "female",
  "status": "active",
  "profile_picture": null,
  "resume": null,
  "contract": null,
  "created_at": "2023-02-01T12:00:00Z",
  "updated_at": "2023-02-01T13:00:00Z"
}
```

## Partial Update Employee

Updates specific fields of an existing employee.

```http
PATCH /api/v1/employees/{id}/
Content-Type: application/json
```

### Request Body

```json
{
  "position": "Senior Product Manager",
  "status": "active"
}
```

### Response

```json
{
  "id": 2,
  "first_name": "Jane",
  "last_name": "Smith",
  "full_name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "+1987654321",
  "position": "Senior Product Manager",
  "department": 2,
  "department_name": "Product",
  "hire_date": "2023-02-01",
  "gender": "female",
  "status": "active",
  "profile_picture": null,
  "resume": null,
  "contract": null,
  "created_at": "2023-02-01T12:00:00Z",
  "updated_at": "2023-02-01T13:00:00Z"
}
```

## Delete Employee

Deletes an employee.

```http
DELETE /api/v1/employees/{id}/
```

### Response

```
204 No Content
```

## List Active Employees

Retrieves a list of active employees.

```http
GET /api/v1/employees/active/
```

### Response

```json
{
  "count": 8,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "full_name": "John Doe",
      "email": "<EMAIL>",
      "position": "Software Engineer",
      "department_name": "Engineering",
      "status": "active",
      "profile_picture": "http://example.com/media/employee_images/john_doe.jpg"
    },
    // More active employees...
  ]
}
```

## Activate Employee

Activates an inactive employee.

```http
PATCH /api/v1/employees/{id}/activate/
```

### Response

```json
{
  "id": 3,
  "first_name": "Bob",
  "last_name": "Johnson",
  "full_name": "Bob Johnson",
  "email": "<EMAIL>",
  "phone": "+1122334455",
  "position": "Designer",
  "department": 3,
  "department_name": "Design",
  "hire_date": "2023-03-01",
  "gender": "male",
  "status": "active",
  "profile_picture": null,
  "resume": null,
  "contract": null,
  "created_at": "2023-03-01T12:00:00Z",
  "updated_at": "2023-03-10T14:00:00Z"
}
```

## Deactivate Employee

Deactivates an active employee.

```http
PATCH /api/v1/employees/{id}/deactivate/
```

### Response

```json
{
  "id": 3,
  "first_name": "Bob",
  "last_name": "Johnson",
  "full_name": "Bob Johnson",
  "email": "<EMAIL>",
  "phone": "+1122334455",
  "position": "Designer",
  "department": 3,
  "department_name": "Design",
  "hire_date": "2023-03-01",
  "gender": "male",
  "status": "inactive",
  "profile_picture": null,
  "resume": null,
  "contract": null,
  "created_at": "2023-03-01T12:00:00Z",
  "updated_at": "2023-03-10T14:00:00Z"
}
```
