from django.test import TestCase
from django.utils import timezone
from organization_app.models.organization import Organization
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from leave_app.models.leave_type import LeaveType
from leave_app.models.leave_policy import LeavePolicy
from leave_app.models.leave_balance import LeaveBalance
from leave_app.models.leave_request import LeaveRequest
from leave_app.models.leave_approval import LeaveApproval
from leave_app.serializers.leave_type_serializer import LeaveTypeSerializer
from leave_app.serializers.leave_policy_serializer import LeavePolicySerializer
from leave_app.serializers.leave_balance_serializer import LeaveBalanceSerializer
from leave_app.serializers.leave_request_serializer import LeaveRequestSerializer, LeaveRequestDetailSerializer
from leave_app.serializers.leave_approval_serializer import LeaveApprovalSerializer
from datetime import date, timedelta


class LeaveTypeSerializerTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test organization
        cls.organization = Organization.objects.create(
            name="Test Organization",
            registration_number="TEST123",
            business_type="private_limited"
        )
        
        # Create test leave type
        cls.leave_type = LeaveType.objects.create(
            name="Casual Leave",
            code="CL",
            description="Leave for personal matters",
            organization=cls.organization,
            is_paid=True,
            max_days_per_year=12,
            min_days_per_request=1,
            max_days_per_request=3,
            carry_forward_allowed=False,
            encashment_allowed=False,
            applicable_during_probation=True,
            probation_period_percentage=50,
            requires_documentation=False,
            status="active"
        )
    
    def test_leave_type_serializer(self):
        serializer = LeaveTypeSerializer(instance=self.leave_type)
        data = serializer.data
        
        self.assertEqual(data['name'], "Casual Leave")
        self.assertEqual(data['code'], "CL")
        self.assertEqual(data['organization'], self.organization.id)
        self.assertEqual(data['organization_name'], self.organization.name)
        self.assertEqual(data['is_paid'], True)
        self.assertEqual(data['max_days_per_year'], 12)
        self.assertEqual(data['status'], "active")
    
    def test_leave_type_serializer_validation(self):
        # Test validation for probation_period_percentage
        data = {
            'name': 'Test Leave',
            'code': 'TL',
            'organization': self.organization.id,
            'probation_period_percentage': 150  # Invalid: should be between 0 and 100
        }
        serializer = LeaveTypeSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('probation_period_percentage', serializer.errors)
        
        # Test validation for max_days_per_request vs max_days_per_year
        data = {
            'name': 'Test Leave',
            'code': 'TL',
            'organization': self.organization.id,
            'max_days_per_year': 10,
            'max_days_per_request': 15  # Invalid: should not be greater than max_days_per_year
        }
        serializer = LeaveTypeSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('max_days_per_request', serializer.errors)


class LeaveRequestSerializerTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test organization
        cls.organization = Organization.objects.create(
            name="Test Organization",
            registration_number="TEST123",
            business_type="private_limited"
        )
        
        # Create test department
        cls.department = Department.objects.create(
            name="Test Department",
            description="Test Department Description"
        )
        
        # Create test employees
        cls.employee = Employee.objects.create(
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="1234567890",
            department=cls.department,
            hire_date=date.today() - timedelta(days=365)
        )
        
        cls.manager = Employee.objects.create(
            first_name="Jane",
            last_name="Smith",
            email="<EMAIL>",
            phone="0987654321",
            department=cls.department,
            hire_date=date.today() - timedelta(days=730)
        )
        
        # Create test leave type
        cls.leave_type = LeaveType.objects.create(
            name="Casual Leave",
            code="CL",
            description="Leave for personal matters",
            organization=cls.organization,
            is_paid=True,
            max_days_per_year=12,
            min_days_per_request=1,
            max_days_per_request=3,
            carry_forward_allowed=False,
            encashment_allowed=False,
            applicable_during_probation=True,
            probation_period_percentage=50,
            requires_documentation=False,
            status="active"
        )
        
        # Create test leave balance
        cls.leave_balance = LeaveBalance.objects.create(
            employee=cls.employee,
            leave_type=cls.leave_type,
            year=timezone.now().year,
            opening_balance=12.0
        )
        
        # Create test leave request
        cls.leave_request = LeaveRequest.objects.create(
            employee=cls.employee,
            leave_type=cls.leave_type,
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=12),
            reason="Personal matters",
            half_day=False,
            status="pending"
        )
    
    def test_leave_request_serializer(self):
        serializer = LeaveRequestSerializer(instance=self.leave_request)
        data = serializer.data
        
        self.assertEqual(data['employee'], self.employee.id)
        self.assertEqual(data['employee_name'], f"{self.employee.first_name} {self.employee.last_name}")
        self.assertEqual(data['leave_type'], self.leave_type.id)
        self.assertEqual(data['leave_type_name'], self.leave_type.name)
        self.assertEqual(data['total_days'], '3.00')
        self.assertEqual(data['reason'], "Personal matters")
        self.assertEqual(data['status'], "pending")
    
    def test_leave_request_detail_serializer(self):
        # Create test approval
        approval = LeaveApproval.objects.create(
            leave_request=self.leave_request,
            approver=self.manager,
            level=1,
            status="pending"
        )
        
        serializer = LeaveRequestDetailSerializer(instance=self.leave_request)
        data = serializer.data
        
        self.assertEqual(data['employee'], self.employee.id)
        self.assertEqual(data['leave_type'], self.leave_type.id)
        self.assertEqual(len(data['approvals']), 1)
        self.assertEqual(data['approvals'][0]['approver'], self.manager.id)
        self.assertEqual(data['approvals'][0]['status'], "pending")


# Add more test classes for other serializers as needed
