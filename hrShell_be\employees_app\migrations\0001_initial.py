# Generated by Django 5.1.7 on 2025-03-29 08:08

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='sn_employees',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('name', models.<PERSON>r<PERSON><PERSON>(db_column='name', max_length=100)),
                ('job_title', models.CharField(db_column='job_title', max_length=100)),
                ('image', models.ImageField(blank=True, db_column='image', null=True, upload_to='employee_images/')),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other')], db_column='gender', max_length=10)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], db_column='status', max_length=10)),
                ('work_location', models.<PERSON><PERSON><PERSON><PERSON>(db_column='work_location', max_length=100)),
            ],
            options={
                'db_table': 'sn_employees',
            },
        ),
    ]
