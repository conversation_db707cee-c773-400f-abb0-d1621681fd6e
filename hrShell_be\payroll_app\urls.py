from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from payroll_app.views.salary_structure_views import SalaryStructureViewSet, SalaryComponentViewSet
from payroll_app.views.employee_salary_views import EmployeeSalaryViewSet
from payroll_app.views.payroll_views import PayrollViewSet, PayrollItemViewSet
from payroll_app.views.payslip_views import PayslipViewSet
from payroll_app.views.loan_views import LoanViewSet, LoanInstallmentViewSet
from payroll_app.views.bonus_views import BonusViewSet, BonusBatchViewSet
from payroll_app.views.tax_views import TaxSlabViewSet, EmployeeTaxDeclarationViewSet
from payroll_app.views.bank_transfer_views import BankTransferViewSet

app_name = 'payroll'

router = DefaultRouter()
router.register(r'salary-components', SalaryComponentViewSet, basename='salarycomponent')
router.register(r'salary-structures', SalaryStructureViewSet, basename='salarystructure')
router.register(r'employee-salaries', EmployeeSalaryViewSet, basename='employeesalary')
router.register(r'payrolls', PayrollViewSet, basename='payroll')
router.register(r'payroll-items', PayrollItemViewSet, basename='payrollitem')
router.register(r'payslips', PayslipViewSet, basename='payslip')
router.register(r'loans', LoanViewSet, basename='loan')
router.register(r'loan-installments', LoanInstallmentViewSet, basename='loaninstallment')
router.register(r'bonuses', BonusViewSet, basename='bonus')
router.register(r'bonus-batches', BonusBatchViewSet, basename='bonusbatch')
router.register(r'tax-slabs', TaxSlabViewSet, basename='taxslab')
router.register(r'tax-declarations', EmployeeTaxDeclarationViewSet, basename='taxdeclaration')
router.register(r'bank-transfers', BankTransferViewSet, basename='banktransfer')

# Custom URLs for nested resources
salary_structure_urls = [
    path('organization/<int:organization_pk>/', SalaryStructureViewSet.as_view({'get': 'organization'}), name='organization-salary-structures'),
]

salary_component_urls = [
    path('organization/<int:organization_pk>/', SalaryComponentViewSet.as_view({'get': 'organization'}), name='organization-salary-components'),
]

employee_salary_urls = [
    path('employee/<int:employee_pk>/', EmployeeSalaryViewSet.as_view({'get': 'employee'}), name='employee-salaries'),
    path('employee/<int:employee_pk>/current/', EmployeeSalaryViewSet.as_view({'get': 'current'}), name='employee-current-salary'),
]

payroll_urls = [
    path('organization/<int:organization_pk>/', PayrollViewSet.as_view({'get': 'organization'}), name='organization-payrolls'),
    path('status/<str:status_value>/', PayrollViewSet.as_view({'get': 'status'}), name='status-payrolls'),
]

payroll_item_urls = [
    path('employee/<int:employee_pk>/', PayrollItemViewSet.as_view({'get': 'employee'}), name='employee-payroll-items'),
    path('status/<str:status_value>/', PayrollItemViewSet.as_view({'get': 'status'}), name='status-payroll-items'),
]

payslip_urls = [
    path('employee/<int:employee_pk>/', PayslipViewSet.as_view({'get': 'employee'}), name='employee-payslips'),
    path('status/<str:status_value>/', PayslipViewSet.as_view({'get': 'status'}), name='status-payslips'),
]

loan_urls = [
    path('employee/<int:employee_pk>/', LoanViewSet.as_view({'get': 'employee'}), name='employee-loans'),
    path('status/<str:status_value>/', LoanViewSet.as_view({'get': 'status'}), name='status-loans'),
]

loan_installment_urls = [
    path('loan/<int:loan_pk>/', LoanInstallmentViewSet.as_view({'get': 'loan'}), name='loan-installments'),
    path('employee/<int:employee_pk>/', LoanInstallmentViewSet.as_view({'get': 'employee'}), name='employee-loan-installments'),
    path('status/<str:status_value>/', LoanInstallmentViewSet.as_view({'get': 'status'}), name='status-loan-installments'),
]

bonus_urls = [
    path('employee/<int:employee_pk>/', BonusViewSet.as_view({'get': 'employee'}), name='employee-bonuses'),
    path('status/<str:status_value>/', BonusViewSet.as_view({'get': 'status'}), name='status-bonuses'),
    path('type/<str:bonus_type>/', BonusViewSet.as_view({'get': 'type'}), name='type-bonuses'),
]

bonus_batch_urls = [
    path('organization/<int:organization_pk>/', BonusBatchViewSet.as_view({'get': 'organization'}), name='organization-bonus-batches'),
    path('status/<str:status_value>/', BonusBatchViewSet.as_view({'get': 'status'}), name='status-bonus-batches'),
]

tax_slab_urls = [
    path('organization/<int:organization_pk>/', TaxSlabViewSet.as_view({'get': 'organization'}), name='organization-tax-slabs'),
    path('gender/<str:gender>/', TaxSlabViewSet.as_view({'get': 'gender'}), name='gender-tax-slabs'),
    path('age-group/<str:age_group>/', TaxSlabViewSet.as_view({'get': 'age_group'}), name='age-group-tax-slabs'),
]

tax_declaration_urls = [
    path('employee/<int:employee_pk>/', EmployeeTaxDeclarationViewSet.as_view({'get': 'employee'}), name='employee-tax-declarations'),
    path('status/<str:status_value>/', EmployeeTaxDeclarationViewSet.as_view({'get': 'status'}), name='status-tax-declarations'),
    path('declaration-type/<str:declaration_type>/', EmployeeTaxDeclarationViewSet.as_view({'get': 'declaration_type'}), name='declaration-type-tax-declarations'),
]

bank_transfer_urls = [
    path('organization/<int:organization_pk>/', BankTransferViewSet.as_view({'get': 'organization'}), name='organization-bank-transfers'),
    path('payroll/<int:payroll_pk>/', BankTransferViewSet.as_view({'get': 'payroll'}), name='payroll-bank-transfers'),
    path('status/<str:status_value>/', BankTransferViewSet.as_view({'get': 'status'}), name='status-bank-transfers'),
]

urlpatterns = [
    path('', include(router.urls)),
    path('salary-structures/', include(salary_structure_urls)),
    path('salary-components/', include(salary_component_urls)),
    path('employee-salaries/', include(employee_salary_urls)),
    path('payrolls/', include(payroll_urls)),
    path('payroll-items/', include(payroll_item_urls)),
    path('payslips/', include(payslip_urls)),
    path('loans/', include(loan_urls)),
    path('loan-installments/', include(loan_installment_urls)),
    path('bonuses/', include(bonus_urls)),
    path('bonus-batches/', include(bonus_batch_urls)),
    path('tax-slabs/', include(tax_slab_urls)),
    path('tax-declarations/', include(tax_declaration_urls)),
    path('bank-transfers/', include(bank_transfer_urls)),
]
