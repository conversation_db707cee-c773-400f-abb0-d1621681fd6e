from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from recruitment_app.views.job_requisition_views import JobRequisitionViewSet
from recruitment_app.views.job_posting_views import JobPostingViewSet
from recruitment_app.views.candidate_views import CandidateViewSet
from recruitment_app.views.application_views import ApplicationViewSet
from recruitment_app.views.interview_views import InterviewViewSet, InterviewFeedbackViewSet
from recruitment_app.views.offer_views import OfferViewSet
from recruitment_app.views.onboarding_views import OnboardingViewSet
from recruitment_app.views.recruitment_source_views import RecruitmentSourceViewSet
from recruitment_app.views.skill_views import SkillViewSet

app_name = 'recruitment'

router = DefaultRouter()
router.register(r'job-requisitions', JobRequisitionViewSet, basename='jobrequisition')
router.register(r'job-postings', JobPostingViewSet, basename='jobposting')
router.register(r'candidates', CandidateViewSet, basename='candidate')
router.register(r'applications', ApplicationViewSet, basename='application')
router.register(r'interviews', InterviewViewSet, basename='interview')
router.register(r'interview-feedback', InterviewFeedbackViewSet, basename='interviewfeedback')
router.register(r'offers', OfferViewSet, basename='offer')
router.register(r'onboarding', OnboardingViewSet, basename='onboarding')
router.register(r'recruitment-sources', RecruitmentSourceViewSet, basename='recruitmentsource')
router.register(r'skills', SkillViewSet, basename='skill')

# Custom URLs for nested resources
job_requisition_urls = [
    path('status/<str:status_value>/', JobRequisitionViewSet.as_view({'get': 'status'}), name='status-job-requisitions'),
    path('organization/<int:organization_pk>/', JobRequisitionViewSet.as_view({'get': 'organization'}), name='organization-job-requisitions'),
    path('department/<int:department_pk>/', JobRequisitionViewSet.as_view({'get': 'department'}), name='department-job-requisitions'),
    path('location/<int:location_pk>/', JobRequisitionViewSet.as_view({'get': 'location'}), name='location-job-requisitions'),
    path('requested-by/<int:employee_pk>/', JobRequisitionViewSet.as_view({'get': 'requested_by'}), name='requested-by-job-requisitions'),
]

job_posting_urls = [
    path('status/<str:status_value>/', JobPostingViewSet.as_view({'get': 'status'}), name='status-job-postings'),
    path('requisition/<int:requisition_pk>/', JobPostingViewSet.as_view({'get': 'requisition'}), name='requisition-job-postings'),
    path('source/<int:source_pk>/', JobPostingViewSet.as_view({'get': 'source'}), name='source-job-postings'),
    path('active/', JobPostingViewSet.as_view({'get': 'active'}), name='active-job-postings'),
    path('featured/', JobPostingViewSet.as_view({'get': 'featured'}), name='featured-job-postings'),
]

candidate_urls = [
    path('status/<str:status_value>/', CandidateViewSet.as_view({'get': 'status'}), name='status-candidates'),
    path('organization/<int:organization_pk>/', CandidateViewSet.as_view({'get': 'organization'}), name='organization-candidates'),
    path('source/<int:source_pk>/', CandidateViewSet.as_view({'get': 'source'}), name='source-candidates'),
    path('skill/<int:skill_pk>/', CandidateViewSet.as_view({'get': 'skill'}), name='skill-candidates'),
]

application_urls = [
    path('status/<str:status_value>/', ApplicationViewSet.as_view({'get': 'status'}), name='status-applications'),
    path('stage/<str:stage_value>/', ApplicationViewSet.as_view({'get': 'stage'}), name='stage-applications'),
    path('job-posting/<int:job_posting_pk>/', ApplicationViewSet.as_view({'get': 'job_posting'}), name='job-posting-applications'),
    path('candidate/<int:candidate_pk>/', ApplicationViewSet.as_view({'get': 'candidate'}), name='candidate-applications'),
    path('assigned-to/<int:employee_pk>/', ApplicationViewSet.as_view({'get': 'assigned_to'}), name='assigned-to-applications'),
]

interview_urls = [
    path('status/<str:status_value>/', InterviewViewSet.as_view({'get': 'status'}), name='status-interviews'),
    path('application/<int:application_pk>/', InterviewViewSet.as_view({'get': 'application'}), name='application-interviews'),
    path('interviewer/<int:interviewer_pk>/', InterviewViewSet.as_view({'get': 'interviewer'}), name='interviewer-interviews'),
    path('upcoming/', InterviewViewSet.as_view({'get': 'upcoming'}), name='upcoming-interviews'),
]

offer_urls = [
    path('status/<str:status_value>/', OfferViewSet.as_view({'get': 'status'}), name='status-offers'),
    path('application/<int:application_pk>/', OfferViewSet.as_view({'get': 'application'}), name='application-offer'),
    path('candidate/<int:candidate_pk>/', OfferViewSet.as_view({'get': 'candidate'}), name='candidate-offers'),
    path('approved-by/<int:employee_pk>/', OfferViewSet.as_view({'get': 'approved_by'}), name='approved-by-offers'),
]

onboarding_urls = [
    path('status/<str:status_value>/', OnboardingViewSet.as_view({'get': 'status'}), name='status-onboarding'),
    path('offer/<int:offer_pk>/', OnboardingViewSet.as_view({'get': 'offer'}), name='offer-onboarding'),
    path('candidate/<int:candidate_pk>/', OnboardingViewSet.as_view({'get': 'candidate'}), name='candidate-onboarding'),
    path('hr-buddy/<int:employee_pk>/', OnboardingViewSet.as_view({'get': 'hr_buddy'}), name='hr-buddy-onboarding'),
    path('manager/<int:employee_pk>/', OnboardingViewSet.as_view({'get': 'manager'}), name='manager-onboarding'),
]

recruitment_source_urls = [
    path('active/', RecruitmentSourceViewSet.as_view({'get': 'active'}), name='active-recruitment-sources'),
    path('organization/<int:organization_pk>/', RecruitmentSourceViewSet.as_view({'get': 'organization'}), name='organization-recruitment-sources'),
    path('source-type/<str:source_type>/', RecruitmentSourceViewSet.as_view({'get': 'source_type'}), name='source-type-recruitment-sources'),
    path('integrated/', RecruitmentSourceViewSet.as_view({'get': 'integrated'}), name='integrated-recruitment-sources'),
]

skill_urls = [
    path('active/', SkillViewSet.as_view({'get': 'active'}), name='active-skills'),
    path('organization/<int:organization_pk>/', SkillViewSet.as_view({'get': 'organization'}), name='organization-skills'),
    path('skill-type/<str:skill_type>/', SkillViewSet.as_view({'get': 'skill_type'}), name='skill-type-skills'),
    path('parent/<int:parent_pk>/', SkillViewSet.as_view({'get': 'parent'}), name='parent-skills'),
]

urlpatterns = [
    path('', include(router.urls)),
    path('job-requisitions/', include(job_requisition_urls)),
    path('job-postings/', include(job_posting_urls)),
    path('candidates/', include(candidate_urls)),
    path('applications/', include(application_urls)),
    path('interviews/', include(interview_urls)),
    path('offers/', include(offer_urls)),
    path('onboarding/', include(onboarding_urls)),
    path('recruitment-sources/', include(recruitment_source_urls)),
    path('skills/', include(skill_urls)),
]
