from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from employees_app.serializers.user_serializer import UserRegistrationSerializer, UserSerializer
from employees_app.serializers.token_serializer import LoginSerializer
from django.contrib.auth.models import User
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import logging

logger = logging.getLogger(__name__)


@swagger_auto_schema(
    tags=['Authentication'],
    operation_description="login management endpoints"
)
class LoginView(TokenObtainPairView):
    """
    Enhanced login endpoint for HR Shell application.

    This endpoint authenticates users and returns JWT tokens along with
    comprehensive user information. It follows Augment guidelines for
    proper documentation, error handling, and security logging.
    """
    serializer_class = LoginSerializer

    @swagger_auto_schema(
        operation_summary="User Login",
        operation_description="""
        Authenticate a user with username and password credentials.

        This endpoint:
        - Validates user credentials
        - Returns JWT access and refresh tokens
        - Provides comprehensive user information
        - Logs authentication attempts for security
        - Updates user's last login timestamp

        **Security Features:**
        - Failed login attempts are logged
        - Inactive accounts are rejected
        - Comprehensive error messages for troubleshooting
        """,
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['username', 'password'],
            properties={
                'username': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Username for authentication',
                    example='john_doe'
                ),
                'password': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Password for authentication',
                    format='password',
                    example='secure_password123'
                ),
            }
        ),
        responses={
            200: openapi.Response(
                description="Login successful",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'access': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='JWT access token (expires in 1 hour)',
                            example='eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
                        ),
                        'refresh': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='JWT refresh token (expires in 1 day)',
                            example='eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
                        ),
                        'user': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                                'username': openapi.Schema(type=openapi.TYPE_STRING, example='john_doe'),
                                'email': openapi.Schema(type=openapi.TYPE_STRING, example='<EMAIL>'),
                                'first_name': openapi.Schema(type=openapi.TYPE_STRING, example='John'),
                                'last_name': openapi.Schema(type=openapi.TYPE_STRING, example='Doe'),
                                'full_name': openapi.Schema(type=openapi.TYPE_STRING, example='John Doe'),
                                'is_staff': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
                                'is_superuser': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
                                'last_login': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
                                'date_joined': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
                            }
                        ),
                        'message': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example='Login successful'
                        ),
                    }
                )
            ),
            400: openapi.Response(
                description="Bad request - Invalid credentials or missing fields",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'detail': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example='Invalid username or password. Please check your credentials and try again.'
                        )
                    }
                )
            ),
            401: openapi.Response(
                description="Unauthorized - Account deactivated",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'detail': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example='This account has been deactivated. Please contact your administrator.'
                        )
                    }
                )
            ),
        },
        tags=['Authentication']
    )
    def post(self, request, *args, **kwargs):
        """
        Authenticate user and return JWT tokens with user information.
        """
        return super().post(request, *args, **kwargs)


@swagger_auto_schema(
    tags=['Authentication'],
    operation_description="userinfo management endpoints"
)
class UserInfoView(APIView):
    """
    View to get information about the authenticated user.
    """

    def get(self, request):
        """
        Return information about the authenticated user.
        """
        user = request.user
        data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
        }
        return Response(data)


@swagger_auto_schema(
    tags=['Authentication'],
    operation_description="logout management endpoints"
)
class LogoutView(APIView):
    """
    View to blacklist the refresh token, effectively logging out the user.
    """

    def post(self, request):
        """
        Blacklist the refresh token.
        """
        try:
            refresh_token = request.data.get('refresh')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
                return Response({"detail": "Successfully logged out."}, status=status.HTTP_200_OK)
            else:
                return Response({"detail": "Refresh token is required."}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


@swagger_auto_schema(
    tags=['Authentication'],
    operation_description="register management endpoints"
)
class RegisterView(generics.CreateAPIView):
    """
    View for user registration.
    """
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer

    @swagger_auto_schema(
        operation_summary="User Registration",
        operation_description="Register a new user account",
        request_body=UserRegistrationSerializer,
        responses={
            201: openapi.Response(
                description="User registered successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'user': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'username': openapi.Schema(type=openapi.TYPE_STRING),
                                'email': openapi.Schema(type=openapi.TYPE_STRING),
                                'first_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'last_name': openapi.Schema(type=openapi.TYPE_STRING),
                            }
                        ),
                        'access': openapi.Schema(type=openapi.TYPE_STRING),
                        'refresh': openapi.Schema(type=openapi.TYPE_STRING),
                        'detail': openapi.Schema(type=openapi.TYPE_STRING),
                    }
                )
            ),
            400: openapi.Response(description="Bad request - validation errors")
        },
        tags=['Authentication']
    )
    def post(self, request, *args, **kwargs):
        """
        Create a new user and return a response with user details and tokens.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Generate tokens for the new user
        refresh = RefreshToken.for_user(user)

        # Return user details and tokens
        return Response({
            "user": UserSerializer(user).data,
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            "detail": "User registered successfully."
        }, status=status.HTTP_201_CREATED)
