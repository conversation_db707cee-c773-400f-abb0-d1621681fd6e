from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from organization_app.models.organization import Organization
from organization_app.serializers.organization_serializer import OrganizationSerializer, OrganizationDetailSerializer
from organization_app.filters import OrganizationFilter


class OrganizationViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Organization Structure'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing Organization instances.

    Additional actions:
    - locations: Get locations for a specific organization
    - business_units: Get business units for a specific organization
    - designations: Get designations for a specific organization
    - policies: Get policies for a specific organization
    - documents: Get documents for a specific organization
    """
    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OrganizationFilter
    search_fields = ['name', 'industry_sector']
    ordering_fields = ['name', 'establishment_date', 'created_at']

    def get_serializer_class(self):
        """
        Return different serializers based on the action
        """
        if self.action == 'retrieve':
            return OrganizationDetailSerializer
        return OrganizationSerializer

    @action(detail=True, methods=['get'])
    def locations(self, request, pk=None):
        """
        Get locations for a specific organization
        """
        organization = self.get_object()
        from organization_app.serializers.location_serializer import LocationSerializer
        locations = organization.locations.all()
        serializer = LocationSerializer(locations, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def business_units(self, request, pk=None):
        """
        Get business units for a specific organization
        """
        organization = self.get_object()
        from organization_app.serializers.business_unit_serializer import BusinessUnitSerializer
        business_units = organization.business_units.all()
        serializer = BusinessUnitSerializer(business_units, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def designations(self, request, pk=None):
        """
        Get designations for a specific organization
        """
        organization = self.get_object()
        from organization_app.serializers.designation_serializer import DesignationSerializer
        designations = organization.designations.all()
        serializer = DesignationSerializer(designations, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def policies(self, request, pk=None):
        """
        Get policies for a specific organization
        """
        organization = self.get_object()
        from organization_app.serializers.organization_policy_serializer import OrganizationPolicySerializer
        policies = organization.policies.all()
        serializer = OrganizationPolicySerializer(policies, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def documents(self, request, pk=None):
        """
        Get documents for a specific organization
        """
        organization = self.get_object()
        from organization_app.serializers.organization_document_serializer import OrganizationDocumentSerializer
        documents = organization.documents.all()
        serializer = OrganizationDocumentSerializer(documents, many=True, context={'request': request})
        return Response(serializer.data)
