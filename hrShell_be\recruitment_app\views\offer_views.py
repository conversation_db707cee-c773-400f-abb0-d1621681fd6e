from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from recruitment_app.models.offer import Offer
from recruitment_app.models.application import Application, ApplicationStageHistory
from recruitment_app.serializers.offer_serializer import OfferSerializer
from recruitment_app.filters.recruitment_filters import OfferFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class OfferViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for offers.
    """
    queryset = Offer.objects.all()
    serializer_class = OfferSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OfferFilter
    search_fields = ['application__candidate__first_name', 'application__candidate__last_name', 'position', 'department', 'location']
    ordering_fields = ['offer_date', 'expiry_date', 'joining_date', 'status', 'created_at']
    ordering = ['-offer_date']
    
    def perform_create(self, serializer):
        """
        Create a new offer and update the application status.
        """
        offer = serializer.save()
        
        # Update application status
        application = offer.application
        application.status = 'offer'
        application.current_stage = 'offer_stage'
        application.last_status_change = timezone.now()
        application.save()
        
        # Create stage history
        ApplicationStageHistory.objects.create(
            application=application,
            from_stage=application.current_stage,
            to_stage='offer_stage',
            from_status=application.status,
            to_status='offer',
            changed_by=self.request.user.employee if hasattr(self.request.user, 'employee') else None,
            notes=f"Offer created with joining date {offer.joining_date}"
        )
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all offers with a specific status.
        """
        offers = Offer.objects.filter(status=status_value)
        page = self.paginate_queryset(offers)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(offers, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def application(self, request, application_pk=None):
        """
        Get the offer for a specific application.
        """
        try:
            offer = Offer.objects.get(application_id=application_pk)
            serializer = self.get_serializer(offer)
            return Response(serializer.data)
        except Offer.DoesNotExist:
            return Response(
                {"detail": "No offer found for this application."},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def candidate(self, request, candidate_pk=None):
        """
        Get all offers for a specific candidate.
        """
        offers = Offer.objects.filter(application__candidate_id=candidate_pk)
        page = self.paginate_queryset(offers)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(offers, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def approved_by(self, request, employee_pk=None):
        """
        Get all offers approved by a specific employee.
        """
        offers = Offer.objects.filter(approved_by_id=employee_pk)
        page = self.paginate_queryset(offers)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(offers, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def submit_for_approval(self, request, pk=None):
        """
        Submit an offer for approval.
        """
        offer = self.get_object()
        
        if offer.status != 'draft':
            return Response(
                {"detail": "Only draft offers can be submitted for approval."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        offer.status = 'pending_approval'
        offer.save()
        
        serializer = self.get_serializer(offer)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """
        Approve an offer.
        """
        offer = self.get_object()
        
        if offer.status != 'pending_approval':
            return Response(
                {"detail": "Only pending offers can be approved."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update offer status
        offer.status = 'approved'
        offer.approval_date = timezone.now().date()
        
        # Set the approver if provided
        approver_id = request.data.get('approved_by')
        if approver_id:
            offer.approved_by_id = approver_id
        
        offer.save()
        
        serializer = self.get_serializer(offer)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def send(self, request, pk=None):
        """
        Send an offer to the candidate.
        """
        offer = self.get_object()
        
        if offer.status != 'approved':
            return Response(
                {"detail": "Only approved offers can be sent."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update offer status
        offer.status = 'sent'
        
        # Set the offer letter if provided
        offer_letter = request.data.get('offer_letter')
        if offer_letter:
            offer.offer_letter = offer_letter
        
        offer.save()
        
        serializer = self.get_serializer(offer)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def accept(self, request, pk=None):
        """
        Mark an offer as accepted by the candidate.
        """
        offer = self.get_object()
        
        if offer.status not in ['sent', 'negotiating']:
            return Response(
                {"detail": "Only sent or negotiating offers can be accepted."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update offer status
        offer.status = 'accepted'
        offer.response_date = timezone.now().date()
        offer.save()
        
        # Update application status
        application = offer.application
        application.status = 'hired'
        application.current_stage = 'onboarding'
        application.last_status_change = timezone.now()
        application.save()
        
        # Create stage history
        ApplicationStageHistory.objects.create(
            application=application,
            from_stage='offer_stage',
            to_stage='onboarding',
            from_status='offer',
            to_status='hired',
            changed_by=self.request.user.employee if hasattr(self.request.user, 'employee') else None,
            notes="Offer accepted by candidate"
        )
        
        serializer = self.get_serializer(offer)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def negotiate(self, request, pk=None):
        """
        Mark an offer as being negotiated by the candidate.
        """
        offer = self.get_object()
        
        if offer.status != 'sent':
            return Response(
                {"detail": "Only sent offers can be negotiated."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update offer status
        offer.status = 'negotiating'
        offer.negotiation_details = request.data.get('negotiation_details')
        offer.save()
        
        serializer = self.get_serializer(offer)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def decline(self, request, pk=None):
        """
        Mark an offer as declined by the candidate.
        """
        offer = self.get_object()
        
        if offer.status not in ['sent', 'negotiating']:
            return Response(
                {"detail": "Only sent or negotiating offers can be declined."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update offer status
        offer.status = 'declined'
        offer.response_date = timezone.now().date()
        offer.decline_reason = request.data.get('decline_reason')
        offer.save()
        
        # Update application status
        application = offer.application
        application.status = 'rejected'
        application.rejection_reason = f"Offer declined: {offer.decline_reason}"
        application.last_status_change = timezone.now()
        application.save()
        
        # Create stage history
        ApplicationStageHistory.objects.create(
            application=application,
            from_stage='offer_stage',
            to_stage='offer_stage',
            from_status='offer',
            to_status='rejected',
            changed_by=self.request.user.employee if hasattr(self.request.user, 'employee') else None,
            notes=f"Offer declined by candidate: {offer.decline_reason}"
        )
        
        serializer = self.get_serializer(offer)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def withdraw(self, request, pk=None):
        """
        Withdraw an offer.
        """
        offer = self.get_object()
        
        if offer.status in ['accepted', 'declined', 'expired', 'withdrawn']:
            return Response(
                {"detail": "Offer cannot be withdrawn in its current state."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update offer status
        offer.status = 'withdrawn'
        offer.withdrawal_reason = request.data.get('withdrawal_reason')
        offer.withdrawal_date = timezone.now().date()
        
        # Set the withdrawer if provided
        withdrawer_id = request.data.get('withdrawn_by')
        if withdrawer_id:
            offer.withdrawn_by_id = withdrawer_id
        
        offer.save()
        
        # Update application status
        application = offer.application
        application.status = 'rejected'
        application.rejection_reason = f"Offer withdrawn: {offer.withdrawal_reason}"
        application.last_status_change = timezone.now()
        application.save()
        
        # Create stage history
        ApplicationStageHistory.objects.create(
            application=application,
            from_stage='offer_stage',
            to_stage='offer_stage',
            from_status='offer',
            to_status='rejected',
            changed_by=offer.withdrawn_by,
            notes=f"Offer withdrawn: {offer.withdrawal_reason}"
        )
        
        serializer = self.get_serializer(offer)
        return Response(serializer.data)
