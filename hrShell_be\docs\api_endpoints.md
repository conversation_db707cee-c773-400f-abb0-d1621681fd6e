# HR Shell API Endpoints

This document provides a comprehensive list of all API endpoints available in the HR Shell application. You can also access the interactive API documentation at:

- Swagger UI: [/swagger-ui/](http://127.0.0.1:8000/swagger-ui/)
- OpenAPI Schema: [/api/schema/](http://127.0.0.1:8000/api/schema/)

## Authentication

The API uses JWT (JSON Web Token) authentication. To access protected endpoints, you need to include the JW<PERSON> token in the Authorization header of your requests.

### User Registration

```
POST /api/register/
```

**Request Body:**
```json
{
  "username": "your_username",
  "email": "<EMAIL>",
  "password": "your_password",
  "password2": "your_password",
  "first_name": "Your",
  "last_name": "Name"
}
```

**Response:**
```json
{
  "user": {
    "id": 1,
    "username": "your_username",
    "email": "<EMAIL>",
    "first_name": "Your",
    "last_name": "Name",
    "is_staff": false,
    "is_superuser": false
  },
  "refresh": "refresh_token_here",
  "access": "access_token_here",
  "detail": "User registered successfully."
}
```

### Getting a Token

```
POST /api/token/
```

**Request Body:**
```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "access": "access_token_here",
  "refresh": "refresh_token_here"
}
```

### Refreshing a Token

```
POST /api/token/refresh/
```

**Request Body:**
```json
{
  "refresh": "refresh_token_here"
}
```

**Response:**
```json
{
  "access": "new_access_token_here"
}
```

### Verifying a Token

```
POST /api/token/verify/
```

**Request Body:**
```json
{
  "token": "token_to_verify_here"
}
```

## System Endpoints

### Health Check

- `GET /health/` - Check if the API is up and running

**Response:**
```json
{
  "status": "ok"
}
```

## AI Assistant Endpoints

### Vector Chat

- `POST /api/v1/ai/chat/` - Chat with AI Assistant

**Request Body:**
```json
{
  "question": "What are the best practices for employee onboarding?"
}
```

**Response:**
```json
{
  "question": "What are the best practices for employee onboarding?",
  "answer": "Based on the information available, here are the best practices for employee onboarding: 1. Structured Onboarding Program, 2. Pre-boarding Communication, 3. First Day Experience, 4. Documentation and Access, 5. Role Clarity, 6. Buddy System, 7. Regular Check-ins, 8. Cultural Integration, 9. Training and Development, 10. Measure Effectiveness.",
  "sources": ["HR Shell Onboarding Module Documentation", "Best Practices for Employee Onboarding"]
}
```

## Employees App Endpoints

### Employees

- `GET /api/v1/employees/` - List all employees
- `POST /api/v1/employees/` - Create a new employee
- `GET /api/v1/employees/{id}/` - Retrieve a specific employee
- `PUT /api/v1/employees/{id}/` - Update a specific employee
- `PATCH /api/v1/employees/{id}/` - Partially update a specific employee
- `DELETE /api/v1/employees/{id}/` - Delete a specific employee

### Departments

- `GET /api/v1/departments/` - List all departments
- `POST /api/v1/departments/` - Create a new department
- `GET /api/v1/departments/{id}/` - Retrieve a specific department
- `PUT /api/v1/departments/{id}/` - Update a specific department
- `PATCH /api/v1/departments/{id}/` - Partially update a specific department
- `DELETE /api/v1/departments/{id}/` - Delete a specific department

### Employee Hikes

- `GET /api/v1/employee-hikes/` - List all employee hikes
- `POST /api/v1/employee-hikes/` - Create a new employee hike
- `GET /api/v1/employee-hikes/{id}/` - Retrieve a specific employee hike
- `PUT /api/v1/employee-hikes/{id}/` - Update a specific employee hike
- `PATCH /api/v1/employee-hikes/{id}/` - Partially update a specific employee hike
- `DELETE /api/v1/employee-hikes/{id}/` - Delete a specific employee hike

### Employee Details

- `GET /api/v1/employee-details/` - List all employee details
- `POST /api/v1/employee-details/` - Create new employee details
- `GET /api/v1/employee-details/{id}/` - Retrieve specific employee details
- `PUT /api/v1/employee-details/{id}/` - Update specific employee details
- `PATCH /api/v1/employee-details/{id}/` - Partially update specific employee details
- `DELETE /api/v1/employee-details/{id}/` - Delete specific employee details

### Employee Attendance

- `GET /api/v1/employee-attendance/` - List all employee attendance records
- `POST /api/v1/employee-attendance/` - Create a new attendance record
- `GET /api/v1/employee-attendance/{id}/` - Retrieve a specific attendance record
- `PUT /api/v1/employee-attendance/{id}/` - Update a specific attendance record
- `PATCH /api/v1/employee-attendance/{id}/` - Partially update a specific attendance record
- `DELETE /api/v1/employee-attendance/{id}/` - Delete a specific attendance record

### Employee Leaves

- `GET /api/v1/employee-leaves/` - List all employee leaves
- `POST /api/v1/employee-leaves/` - Create a new employee leave
- `GET /api/v1/employee-leaves/{id}/` - Retrieve a specific employee leave
- `PUT /api/v1/employee-leaves/{id}/` - Update a specific employee leave
- `PATCH /api/v1/employee-leaves/{id}/` - Partially update a specific employee leave
- `DELETE /api/v1/employee-leaves/{id}/` - Delete a specific employee leave

### Job History

- `GET /api/v1/job-history/` - List all job history records
- `POST /api/v1/job-history/` - Create a new job history record
- `GET /api/v1/job-history/{id}/` - Retrieve a specific job history record
- `PUT /api/v1/job-history/{id}/` - Update a specific job history record
- `PATCH /api/v1/job-history/{id}/` - Partially update a specific job history record
- `DELETE /api/v1/job-history/{id}/` - Delete a specific job history record

### Salaries

- `GET /api/v1/salaries/` - List all salary records
- `POST /api/v1/salaries/` - Create a new salary record
- `GET /api/v1/salaries/{id}/` - Retrieve a specific salary record
- `PUT /api/v1/salaries/{id}/` - Update a specific salary record
- `PATCH /api/v1/salaries/{id}/` - Partially update a specific salary record
- `DELETE /api/v1/salaries/{id}/` - Delete a specific salary record

### Employee Documents

- `GET /api/v1/employee-documents/` - List all employee documents
- `POST /api/v1/employee-documents/` - Create a new employee document
- `GET /api/v1/employee-documents/{id}/` - Retrieve a specific employee document
- `PUT /api/v1/employee-documents/{id}/` - Update a specific employee document
- `PATCH /api/v1/employee-documents/{id}/` - Partially update a specific employee document
- `DELETE /api/v1/employee-documents/{id}/` - Delete a specific employee document

### Skill Offerings

- `GET /api/v1/skill-offerings/` - List all skill offerings
- `POST /api/v1/skill-offerings/` - Create a new skill offering
- `GET /api/v1/skill-offerings/{id}/` - Retrieve a specific skill offering
- `PUT /api/v1/skill-offerings/{id}/` - Update a specific skill offering
- `PATCH /api/v1/skill-offerings/{id}/` - Partially update a specific skill offering
- `DELETE /api/v1/skill-offerings/{id}/` - Delete a specific skill offering

### Authentication

- `GET /api/v1/user-info/` - Get current user information
- `POST /api/v1/logout/` - Logout current user

## Organization App Endpoints

### Organizations

- `GET /api/v1/organizations/` - List all organizations
- `POST /api/v1/organizations/` - Create a new organization
- `GET /api/v1/organizations/{id}/` - Retrieve a specific organization
- `PUT /api/v1/organizations/{id}/` - Update a specific organization
- `PATCH /api/v1/organizations/{id}/` - Partially update a specific organization
- `DELETE /api/v1/organizations/{id}/` - Delete a specific organization

### Locations

- `GET /api/v1/locations/` - List all locations
- `POST /api/v1/locations/` - Create a new location
- `GET /api/v1/locations/{id}/` - Retrieve a specific location
- `PUT /api/v1/locations/{id}/` - Update a specific location
- `PATCH /api/v1/locations/{id}/` - Partially update a specific location
- `DELETE /api/v1/locations/{id}/` - Delete a specific location

### Designations

- `GET /api/v1/designations/` - List all designations
- `POST /api/v1/designations/` - Create a new designation
- `GET /api/v1/designations/{id}/` - Retrieve a specific designation
- `PUT /api/v1/designations/{id}/` - Update a specific designation
- `PATCH /api/v1/designations/{id}/` - Partially update a specific designation
- `DELETE /api/v1/designations/{id}/` - Delete a specific designation

### Business Units

- `GET /api/v1/business-units/` - List all business units
- `POST /api/v1/business-units/` - Create a new business unit
- `GET /api/v1/business-units/{id}/` - Retrieve a specific business unit
- `PUT /api/v1/business-units/{id}/` - Update a specific business unit
- `PATCH /api/v1/business-units/{id}/` - Partially update a specific business unit
- `DELETE /api/v1/business-units/{id}/` - Delete a specific business unit

### Organization Policies

- `GET /api/v1/policies/` - List all organization policies
- `POST /api/v1/policies/` - Create a new organization policy
- `GET /api/v1/policies/{id}/` - Retrieve a specific organization policy
- `PUT /api/v1/policies/{id}/` - Update a specific organization policy
- `PATCH /api/v1/policies/{id}/` - Partially update a specific organization policy
- `DELETE /api/v1/policies/{id}/` - Delete a specific organization policy

### Organization Documents

- `GET /api/v1/documents/` - List all organization documents
- `POST /api/v1/documents/` - Create a new organization document
- `GET /api/v1/documents/{id}/` - Retrieve a specific organization document
- `PUT /api/v1/documents/{id}/` - Update a specific organization document
- `PATCH /api/v1/documents/{id}/` - Partially update a specific organization document
- `DELETE /api/v1/documents/{id}/` - Delete a specific organization document

## Leave App Endpoints

### Leave Types

- `GET /api/v1/leave-types/` - List all leave types
- `POST /api/v1/leave-types/` - Create a new leave type
- `GET /api/v1/leave-types/{id}/` - Retrieve a specific leave type
- `PUT /api/v1/leave-types/{id}/` - Update a specific leave type
- `PATCH /api/v1/leave-types/{id}/` - Partially update a specific leave type
- `DELETE /api/v1/leave-types/{id}/` - Delete a specific leave type

### Leave Policies

- `GET /api/v1/leave-policies/` - List all leave policies
- `POST /api/v1/leave-policies/` - Create a new leave policy
- `GET /api/v1/leave-policies/{id}/` - Retrieve a specific leave policy
- `PUT /api/v1/leave-policies/{id}/` - Update a specific leave policy
- `PATCH /api/v1/leave-policies/{id}/` - Partially update a specific leave policy
- `DELETE /api/v1/leave-policies/{id}/` - Delete a specific leave policy

### Leave Balances

- `GET /api/v1/leave-balances/` - List all leave balances
- `POST /api/v1/leave-balances/` - Create a new leave balance
- `GET /api/v1/leave-balances/{id}/` - Retrieve a specific leave balance
- `PUT /api/v1/leave-balances/{id}/` - Update a specific leave balance
- `PATCH /api/v1/leave-balances/{id}/` - Partially update a specific leave balance
- `DELETE /api/v1/leave-balances/{id}/` - Delete a specific leave balance

### Leave Requests

- `GET /api/v1/leave-requests/` - List all leave requests
- `POST /api/v1/leave-requests/` - Create a new leave request
- `GET /api/v1/leave-requests/{id}/` - Retrieve a specific leave request
- `PUT /api/v1/leave-requests/{id}/` - Update a specific leave request
- `PATCH /api/v1/leave-requests/{id}/` - Partially update a specific leave request
- `DELETE /api/v1/leave-requests/{id}/` - Delete a specific leave request

### Leave Approvals

- `GET /api/v1/leave-approvals/` - List all leave approvals
- `POST /api/v1/leave-approvals/` - Create a new leave approval
- `GET /api/v1/leave-approvals/{id}/` - Retrieve a specific leave approval
- `PUT /api/v1/leave-approvals/{id}/` - Update a specific leave approval
- `PATCH /api/v1/leave-approvals/{id}/` - Partially update a specific leave approval
- `DELETE /api/v1/leave-approvals/{id}/` - Delete a specific leave approval

### Holidays

- `GET /api/v1/holidays/` - List all holidays
- `POST /api/v1/holidays/` - Create a new holiday
- `GET /api/v1/holidays/{id}/` - Retrieve a specific holiday
- `PUT /api/v1/holidays/{id}/` - Update a specific holiday
- `PATCH /api/v1/holidays/{id}/` - Partially update a specific holiday
- `DELETE /api/v1/holidays/{id}/` - Delete a specific holiday

### Week Offs

- `GET /api/v1/week-offs/` - List all week offs
- `POST /api/v1/week-offs/` - Create a new week off
- `GET /api/v1/week-offs/{id}/` - Retrieve a specific week off
- `PUT /api/v1/week-offs/{id}/` - Update a specific week off
- `PATCH /api/v1/week-offs/{id}/` - Partially update a specific week off
- `DELETE /api/v1/week-offs/{id}/` - Delete a specific week off

## Payroll App Endpoints

### Salary Components

- `GET /api/v1/salary-components/` - List all salary components
- `POST /api/v1/salary-components/` - Create a new salary component
- `GET /api/v1/salary-components/{id}/` - Retrieve a specific salary component
- `PUT /api/v1/salary-components/{id}/` - Update a specific salary component
- `PATCH /api/v1/salary-components/{id}/` - Partially update a specific salary component
- `DELETE /api/v1/salary-components/{id}/` - Delete a specific salary component
- `GET /api/v1/salary-components/organization/{organization_pk}/` - List salary components for a specific organization

### Salary Structures

- `GET /api/v1/salary-structures/` - List all salary structures
- `POST /api/v1/salary-structures/` - Create a new salary structure
- `GET /api/v1/salary-structures/{id}/` - Retrieve a specific salary structure
- `PUT /api/v1/salary-structures/{id}/` - Update a specific salary structure
- `PATCH /api/v1/salary-structures/{id}/` - Partially update a specific salary structure
- `DELETE /api/v1/salary-structures/{id}/` - Delete a specific salary structure
- `GET /api/v1/salary-structures/organization/{organization_pk}/` - List salary structures for a specific organization

### Employee Salaries

- `GET /api/v1/employee-salaries/` - List all employee salaries
- `POST /api/v1/employee-salaries/` - Create a new employee salary
- `GET /api/v1/employee-salaries/{id}/` - Retrieve a specific employee salary
- `PUT /api/v1/employee-salaries/{id}/` - Update a specific employee salary
- `PATCH /api/v1/employee-salaries/{id}/` - Partially update a specific employee salary
- `DELETE /api/v1/employee-salaries/{id}/` - Delete a specific employee salary
- `GET /api/v1/employee-salaries/employee/{employee_pk}/` - List salaries for a specific employee
- `GET /api/v1/employee-salaries/employee/{employee_pk}/current/` - Get current salary for a specific employee

### Payrolls

- `GET /api/v1/payrolls/` - List all payrolls
- `POST /api/v1/payrolls/` - Create a new payroll
- `GET /api/v1/payrolls/{id}/` - Retrieve a specific payroll
- `PUT /api/v1/payrolls/{id}/` - Update a specific payroll
- `PATCH /api/v1/payrolls/{id}/` - Partially update a specific payroll
- `DELETE /api/v1/payrolls/{id}/` - Delete a specific payroll
- `GET /api/v1/payrolls/organization/{organization_pk}/` - List payrolls for a specific organization
- `GET /api/v1/payrolls/status/{status_value}/` - List payrolls with a specific status

### Payroll Items

- `GET /api/v1/payroll-items/` - List all payroll items
- `POST /api/v1/payroll-items/` - Create a new payroll item
- `GET /api/v1/payroll-items/{id}/` - Retrieve a specific payroll item
- `PUT /api/v1/payroll-items/{id}/` - Update a specific payroll item
- `PATCH /api/v1/payroll-items/{id}/` - Partially update a specific payroll item
- `DELETE /api/v1/payroll-items/{id}/` - Delete a specific payroll item
- `GET /api/v1/payroll-items/employee/{employee_pk}/` - List payroll items for a specific employee
- `GET /api/v1/payroll-items/status/{status_value}/` - List payroll items with a specific status

### Payslips

- `GET /api/v1/payslips/` - List all payslips
- `POST /api/v1/payslips/` - Create a new payslip
- `GET /api/v1/payslips/{id}/` - Retrieve a specific payslip
- `PUT /api/v1/payslips/{id}/` - Update a specific payslip
- `PATCH /api/v1/payslips/{id}/` - Partially update a specific payslip
- `DELETE /api/v1/payslips/{id}/` - Delete a specific payslip
- `GET /api/v1/payslips/employee/{employee_pk}/` - List payslips for a specific employee
- `GET /api/v1/payslips/status/{status_value}/` - List payslips with a specific status

### Loans

- `GET /api/v1/loans/` - List all loans
- `POST /api/v1/loans/` - Create a new loan
- `GET /api/v1/loans/{id}/` - Retrieve a specific loan
- `PUT /api/v1/loans/{id}/` - Update a specific loan
- `PATCH /api/v1/loans/{id}/` - Partially update a specific loan
- `DELETE /api/v1/loans/{id}/` - Delete a specific loan
- `GET /api/v1/loans/employee/{employee_pk}/` - List loans for a specific employee
- `GET /api/v1/loans/status/{status_value}/` - List loans with a specific status

### Loan Installments

- `GET /api/v1/loan-installments/` - List all loan installments
- `POST /api/v1/loan-installments/` - Create a new loan installment
- `GET /api/v1/loan-installments/{id}/` - Retrieve a specific loan installment
- `PUT /api/v1/loan-installments/{id}/` - Update a specific loan installment
- `PATCH /api/v1/loan-installments/{id}/` - Partially update a specific loan installment
- `DELETE /api/v1/loan-installments/{id}/` - Delete a specific loan installment

### Bonuses

- `GET /api/v1/bonuses/` - List all bonuses
- `POST /api/v1/bonuses/` - Create a new bonus
- `GET /api/v1/bonuses/{id}/` - Retrieve a specific bonus
- `PUT /api/v1/bonuses/{id}/` - Update a specific bonus
- `PATCH /api/v1/bonuses/{id}/` - Partially update a specific bonus
- `DELETE /api/v1/bonuses/{id}/` - Delete a specific bonus

### Bonus Batches

- `GET /api/v1/bonus-batches/` - List all bonus batches
- `POST /api/v1/bonus-batches/` - Create a new bonus batch
- `GET /api/v1/bonus-batches/{id}/` - Retrieve a specific bonus batch
- `PUT /api/v1/bonus-batches/{id}/` - Update a specific bonus batch
- `PATCH /api/v1/bonus-batches/{id}/` - Partially update a specific bonus batch
- `DELETE /api/v1/bonus-batches/{id}/` - Delete a specific bonus batch
- `GET /api/v1/bonus-batches/organization/{organization_pk}/` - List bonus batches for a specific organization
- `GET /api/v1/bonus-batches/status/{status_value}/` - List bonus batches with a specific status

### Tax Slabs

- `GET /api/v1/tax-slabs/` - List all tax slabs
- `POST /api/v1/tax-slabs/` - Create a new tax slab
- `GET /api/v1/tax-slabs/{id}/` - Retrieve a specific tax slab
- `PUT /api/v1/tax-slabs/{id}/` - Update a specific tax slab
- `PATCH /api/v1/tax-slabs/{id}/` - Partially update a specific tax slab
- `DELETE /api/v1/tax-slabs/{id}/` - Delete a specific tax slab
- `GET /api/v1/tax-slabs/organization/{organization_pk}/` - List tax slabs for a specific organization
- `GET /api/v1/tax-slabs/gender/{gender}/` - List tax slabs for a specific gender
- `GET /api/v1/tax-slabs/age-group/{age_group}/` - List tax slabs for a specific age group

### Tax Declarations

- `GET /api/v1/tax-declarations/` - List all tax declarations
- `POST /api/v1/tax-declarations/` - Create a new tax declaration
- `GET /api/v1/tax-declarations/{id}/` - Retrieve a specific tax declaration
- `PUT /api/v1/tax-declarations/{id}/` - Update a specific tax declaration
- `PATCH /api/v1/tax-declarations/{id}/` - Partially update a specific tax declaration
- `DELETE /api/v1/tax-declarations/{id}/` - Delete a specific tax declaration

### Bank Transfers

- `GET /api/v1/bank-transfers/` - List all bank transfers
- `POST /api/v1/bank-transfers/` - Create a new bank transfer
- `GET /api/v1/bank-transfers/{id}/` - Retrieve a specific bank transfer
- `PUT /api/v1/bank-transfers/{id}/` - Update a specific bank transfer
- `PATCH /api/v1/bank-transfers/{id}/` - Partially update a specific bank transfer
- `DELETE /api/v1/bank-transfers/{id}/` - Delete a specific bank transfer

## Recruitment App Endpoints

### Job Requisitions

- `GET /api/v1/job-requisitions/` - List all job requisitions
- `POST /api/v1/job-requisitions/` - Create a new job requisition
- `GET /api/v1/job-requisitions/{id}/` - Retrieve a specific job requisition
- `PUT /api/v1/job-requisitions/{id}/` - Update a specific job requisition
- `PATCH /api/v1/job-requisitions/{id}/` - Partially update a specific job requisition
- `DELETE /api/v1/job-requisitions/{id}/` - Delete a specific job requisition

### Job Postings

- `GET /api/v1/job-postings/` - List all job postings
- `POST /api/v1/job-postings/` - Create a new job posting
- `GET /api/v1/job-postings/{id}/` - Retrieve a specific job posting
- `PUT /api/v1/job-postings/{id}/` - Update a specific job posting
- `PATCH /api/v1/job-postings/{id}/` - Partially update a specific job posting
- `DELETE /api/v1/job-postings/{id}/` - Delete a specific job posting

### Candidates

- `GET /api/v1/candidates/` - List all candidates
- `POST /api/v1/candidates/` - Create a new candidate
- `GET /api/v1/candidates/{id}/` - Retrieve a specific candidate
- `PUT /api/v1/candidates/{id}/` - Update a specific candidate
- `PATCH /api/v1/candidates/{id}/` - Partially update a specific candidate
- `DELETE /api/v1/candidates/{id}/` - Delete a specific candidate

### Applications

- `GET /api/v1/applications/` - List all applications
- `POST /api/v1/applications/` - Create a new application
- `GET /api/v1/applications/{id}/` - Retrieve a specific application
- `PUT /api/v1/applications/{id}/` - Update a specific application
- `PATCH /api/v1/applications/{id}/` - Partially update a specific application
- `DELETE /api/v1/applications/{id}/` - Delete a specific application
- `GET /api/v1/applications/status/{status_value}/` - List applications with a specific status
- `GET /api/v1/applications/stage/{stage_value}/` - List applications in a specific stage
- `GET /api/v1/applications/job-posting/{job_posting_pk}/` - List applications for a specific job posting
- `GET /api/v1/applications/candidate/{candidate_pk}/` - List applications for a specific candidate
- `GET /api/v1/applications/assigned-to/{employee_pk}/` - List applications assigned to a specific employee

### Interviews

- `GET /api/v1/interviews/` - List all interviews
- `POST /api/v1/interviews/` - Create a new interview
- `GET /api/v1/interviews/{id}/` - Retrieve a specific interview
- `PUT /api/v1/interviews/{id}/` - Update a specific interview
- `PATCH /api/v1/interviews/{id}/` - Partially update a specific interview
- `DELETE /api/v1/interviews/{id}/` - Delete a specific interview

### Interview Feedback

- `GET /api/v1/interview-feedback/` - List all interview feedback
- `POST /api/v1/interview-feedback/` - Create new interview feedback
- `GET /api/v1/interview-feedback/{id}/` - Retrieve specific interview feedback
- `PUT /api/v1/interview-feedback/{id}/` - Update specific interview feedback
- `PATCH /api/v1/interview-feedback/{id}/` - Partially update specific interview feedback
- `DELETE /api/v1/interview-feedback/{id}/` - Delete specific interview feedback

### Offers

- `GET /api/v1/offers/` - List all offers
- `POST /api/v1/offers/` - Create a new offer
- `GET /api/v1/offers/{id}/` - Retrieve a specific offer
- `PUT /api/v1/offers/{id}/` - Update a specific offer
- `PATCH /api/v1/offers/{id}/` - Partially update a specific offer
- `DELETE /api/v1/offers/{id}/` - Delete a specific offer

### Onboarding

- `GET /api/v1/onboarding/` - List all onboarding records
- `POST /api/v1/onboarding/` - Create a new onboarding record
- `GET /api/v1/onboarding/{id}/` - Retrieve a specific onboarding record
- `PUT /api/v1/onboarding/{id}/` - Update a specific onboarding record
- `PATCH /api/v1/onboarding/{id}/` - Partially update a specific onboarding record
- `DELETE /api/v1/onboarding/{id}/` - Delete a specific onboarding record

### Recruitment Sources

- `GET /api/v1/recruitment-sources/` - List all recruitment sources
- `POST /api/v1/recruitment-sources/` - Create a new recruitment source
- `GET /api/v1/recruitment-sources/{id}/` - Retrieve a specific recruitment source
- `PUT /api/v1/recruitment-sources/{id}/` - Update a specific recruitment source
- `PATCH /api/v1/recruitment-sources/{id}/` - Partially update a specific recruitment source
- `DELETE /api/v1/recruitment-sources/{id}/` - Delete a specific recruitment source
- `GET /api/v1/recruitment-sources/active/` - List active recruitment sources
- `GET /api/v1/recruitment-sources/organization/{organization_pk}/` - List recruitment sources for a specific organization
- `GET /api/v1/recruitment-sources/source-type/{source_type}/` - List recruitment sources of a specific type
- `GET /api/v1/recruitment-sources/integrated/` - List integrated recruitment sources

### Skills

- `GET /api/v1/skills/` - List all skills
- `POST /api/v1/skills/` - Create a new skill
- `GET /api/v1/skills/{id}/` - Retrieve a specific skill
- `PUT /api/v1/skills/{id}/` - Update a specific skill
- `PATCH /api/v1/skills/{id}/` - Partially update a specific skill
- `DELETE /api/v1/skills/{id}/` - Delete a specific skill

## Onboarding & Offboarding App Endpoints

### Onboarding Plans

- `GET /api/v1/onboarding-plans/` - List all onboarding plans
- `POST /api/v1/onboarding-plans/` - Create a new onboarding plan
- `GET /api/v1/onboarding-plans/{id}/` - Retrieve a specific onboarding plan
- `PUT /api/v1/onboarding-plans/{id}/` - Update a specific onboarding plan
- `PATCH /api/v1/onboarding-plans/{id}/` - Partially update a specific onboarding plan
- `DELETE /api/v1/onboarding-plans/{id}/` - Delete a specific onboarding plan

### Onboarding Tasks

- `GET /api/v1/onboarding-tasks/` - List all onboarding tasks
- `POST /api/v1/onboarding-tasks/` - Create a new onboarding task
- `GET /api/v1/onboarding-tasks/{id}/` - Retrieve a specific onboarding task
- `PUT /api/v1/onboarding-tasks/{id}/` - Update a specific onboarding task
- `PATCH /api/v1/onboarding-tasks/{id}/` - Partially update a specific onboarding task
- `DELETE /api/v1/onboarding-tasks/{id}/` - Delete a specific onboarding task

### Offboarding Requests

- `GET /api/v1/offboarding-requests/` - List all offboarding requests
- `POST /api/v1/offboarding-requests/` - Create a new offboarding request
- `GET /api/v1/offboarding-requests/{id}/` - Retrieve a specific offboarding request
- `PUT /api/v1/offboarding-requests/{id}/` - Update a specific offboarding request
- `PATCH /api/v1/offboarding-requests/{id}/` - Partially update a specific offboarding request
- `DELETE /api/v1/offboarding-requests/{id}/` - Delete a specific offboarding request

### Offboarding Tasks

- `GET /api/v1/offboarding-tasks/` - List all offboarding tasks
- `POST /api/v1/offboarding-tasks/` - Create a new offboarding task
- `GET /api/v1/offboarding-tasks/{id}/` - Retrieve a specific offboarding task
- `PUT /api/v1/offboarding-tasks/{id}/` - Update a specific offboarding task
- `PATCH /api/v1/offboarding-tasks/{id}/` - Partially update a specific offboarding task
- `DELETE /api/v1/offboarding-tasks/{id}/` - Delete a specific offboarding task

### Documents

- `GET /api/v1/documents/` - List all documents
- `POST /api/v1/documents/` - Create a new document
- `GET /api/v1/documents/{id}/` - Retrieve a specific document
- `PUT /api/v1/documents/{id}/` - Update a specific document
- `PATCH /api/v1/documents/{id}/` - Partially update a specific document
- `DELETE /api/v1/documents/{id}/` - Delete a specific document

### Document Templates

- `GET /api/v1/document-templates/` - List all document templates
- `POST /api/v1/document-templates/` - Create a new document template
- `GET /api/v1/document-templates/{id}/` - Retrieve a specific document template
- `PUT /api/v1/document-templates/{id}/` - Update a specific document template
- `PATCH /api/v1/document-templates/{id}/` - Partially update a specific document template
- `DELETE /api/v1/document-templates/{id}/` - Delete a specific document template

### Exit Feedback

- `GET /api/v1/exit-feedback/` - List all exit feedback
- `POST /api/v1/exit-feedback/` - Create new exit feedback
- `GET /api/v1/exit-feedback/{id}/` - Retrieve specific exit feedback
- `PUT /api/v1/exit-feedback/{id}/` - Update specific exit feedback
- `PATCH /api/v1/exit-feedback/{id}/` - Partially update specific exit feedback
- `DELETE /api/v1/exit-feedback/{id}/` - Delete specific exit feedback

## Other Endpoints

### System

- `GET /health/` - Check the health of the API

### Authentication

- `POST /api/register/` - Register a new user
- `POST /api/token/` - Obtain JWT token
- `POST /api/token/refresh/` - Refresh JWT token
- `POST /api/token/verify/` - Verify JWT token

### AI Assistant

- `POST /api/v1/ai/chat/` - Chat with AI Assistant using vector search and Gemini AI
