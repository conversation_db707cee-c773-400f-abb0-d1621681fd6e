from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from leave_app.models.leave_balance import LeaveBalance
from leave_app.serializers.leave_balance_serializer import LeaveBalanceSerializer
from leave_app.filters.leave_filters import LeaveBalanceFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class LeaveBalanceViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Leave Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for managing leave balances
    
    list:
    Return a list of all leave balances
    
    create:
    Create a new leave balance
    
    retrieve:
    Return the given leave balance
    
    update:
    Update the given leave balance
    
    partial_update:
    Partially update the given leave balance
    
    destroy:
    Delete the given leave balance
    
    Additional actions:
    - employee_balances: Get leave balances for a specific employee
    - employee_year_balances: Get leave balances for a specific employee and year
    - current_year_balances: Get leave balances for the current year
    - adjust_balance: Adjust a leave balance
    """
    queryset = LeaveBalance.objects.all()
    serializer_class = LeaveBalanceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LeaveBalanceFilter
    ordering_fields = ['year', 'employee__first_name', 'leave_type__name']
    ordering = ['-year', 'employee__first_name']
    
    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_balances(self, request, employee_id=None):
        """
        Get leave balances for a specific employee
        """
        balances = self.queryset.filter(employee_id=employee_id)
        page = self.paginate_queryset(balances)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(balances, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)/year/(?P<year>[^/.]+)')
    def employee_year_balances(self, request, employee_id=None, year=None):
        """
        Get leave balances for a specific employee and year
        """
        balances = self.queryset.filter(employee_id=employee_id, year=year)
        page = self.paginate_queryset(balances)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(balances, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def current_year_balances(self, request):
        """
        Get leave balances for the current year
        """
        current_year = timezone.now().year
        balances = self.queryset.filter(year=current_year)
        page = self.paginate_queryset(balances)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(balances, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def adjust_balance(self, request, pk=None):
        """
        Adjust a leave balance
        """
        balance = self.get_object()
        
        # Get adjustment amount from request data
        adjustment = request.data.get('adjustment', 0)
        reason = request.data.get('reason', '')
        
        try:
            adjustment = float(adjustment)
        except ValueError:
            return Response(
                {"detail": "Invalid adjustment value"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update the adjusted field
        balance.adjusted += adjustment
        balance.save()
        
        # Return the updated balance
        serializer = self.get_serializer(balance)
        return Response(serializer.data)
