from django.contrib import admin
from leave_app.models.leave_type import LeaveType
from leave_app.models.leave_policy import LeavePolicy
from leave_app.models.leave_balance import LeaveBalance
from leave_app.models.leave_request import LeaveRequest
from leave_app.models.leave_approval import LeaveApproval
from leave_app.models.holiday import Holiday
from leave_app.models.week_off import WeekOff


@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'organization', 'is_paid', 'max_days_per_year', 'status')
    list_filter = ('is_paid', 'carry_forward_allowed', 'encashment_allowed', 'status', 'organization')
    search_fields = ('name', 'code', 'description')


@admin.register(LeavePolicy)
class LeavePolicyAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'leave_type', 'department', 'location', 'designation')
    list_filter = ('organization', 'leave_type', 'department', 'location', 'designation', 'accrual_method')
    search_fields = ('name', 'description')


@admin.register(LeaveBalance)
class LeaveBalanceAdmin(admin.ModelAdmin):
    list_display = ('employee', 'leave_type', 'year', 'opening_balance', 'accrued', 'used', 'adjusted', 'encashed')
    list_filter = ('year', 'leave_type', 'employee__department')
    search_fields = ('employee__first_name', 'employee__last_name', 'leave_type__name')

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('employee', 'leave_type')
        return queryset


@admin.register(LeaveRequest)
class LeaveRequestAdmin(admin.ModelAdmin):
    list_display = ('employee', 'leave_type', 'start_date', 'end_date', 'total_days', 'status')
    list_filter = ('status', 'leave_type', 'employee__department', 'half_day')
    search_fields = ('employee__first_name', 'employee__last_name', 'reason')
    date_hierarchy = 'start_date'

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('employee', 'leave_type', 'approved_by')
        return queryset


@admin.register(LeaveApproval)
class LeaveApprovalAdmin(admin.ModelAdmin):
    list_display = ('leave_request', 'approver', 'level', 'status', 'approval_date')
    list_filter = ('status', 'level')
    search_fields = ('leave_request__employee__first_name', 'leave_request__employee__last_name', 'approver__first_name', 'approver__last_name')

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('leave_request', 'approver')
        return queryset


@admin.register(Holiday)
class HolidayAdmin(admin.ModelAdmin):
    list_display = ('name', 'date', 'organization', 'location', 'holiday_type', 'is_recurring')
    list_filter = ('holiday_type', 'is_recurring', 'is_half_day', 'organization', 'location')
    search_fields = ('name', 'description')
    date_hierarchy = 'date'


@admin.register(WeekOff)
class WeekOffAdmin(admin.ModelAdmin):
    list_display = ('organization', 'location', 'department', 'day_of_week', 'is_half_day')
    list_filter = ('day_of_week', 'is_half_day', 'organization', 'location', 'department')
