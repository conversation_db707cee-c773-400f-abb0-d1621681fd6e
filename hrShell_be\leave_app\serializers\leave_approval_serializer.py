from rest_framework import serializers
from leave_app.models.leave_approval import LeaveApproval, ApprovalStatus


class LeaveApprovalSerializer(serializers.ModelSerializer):
    """
    Serializer for the LeaveApproval model
    """
    approver_name = serializers.ReadOnlyField(source='approver.full_name')
    status_display = serializers.ReadOnlyField(source='get_status_display')
    
    class Meta:
        model = LeaveApproval
        fields = [
            'id', 'leave_request', 'approver', 'approver_name', 'level',
            'status', 'status_display', 'comments', 'approval_date',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate that the approver is not the employee who requested the leave
        """
        leave_request = data.get('leave_request')
        approver = data.get('approver')
        
        if leave_request.employee == approver:
            raise serializers.ValidationError("An employee cannot approve their own leave request")
        
        return data
