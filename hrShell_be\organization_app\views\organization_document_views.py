from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from organization_app.models.organization_document import OrganizationDocument
from organization_app.serializers.organization_document_serializer import OrganizationDocumentSerializer
from organization_app.filters import OrganizationDocumentFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class OrganizationDocumentViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Document Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing OrganizationDocument instances.

    Additional actions:
    - by_type: Get documents filtered by type
    - by_business_unit: Get documents for a specific business unit
    - expired: Get expired documents
    - expiring_soon: Get documents expiring soon
    """
    queryset = OrganizationDocument.objects.all()
    serializer_class = OrganizationDocumentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OrganizationDocumentFilter
    search_fields = ['title', 'description']
    ordering_fields = ['title', 'document_type', 'effective_date', 'expiry_date', 'created_at']

    @action(detail=False, methods=['get'])
    def by_organization(self, request):
        """
        Get documents filtered by organization
        """
        organization_id = request.query_params.get('organization_id')
        if not organization_id:
            return Response(
                {"error": "organization_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        documents = OrganizationDocument.objects.filter(organization_id=organization_id)
        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """
        Get documents filtered by type
        """
        document_type = request.query_params.get('document_type')
        if not document_type:
            return Response(
                {"error": "document_type query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        documents = OrganizationDocument.objects.filter(document_type=document_type)
        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_business_unit(self, request):
        """
        Get documents for a specific business unit
        """
        business_unit_id = request.query_params.get('business_unit_id')
        if not business_unit_id:
            return Response(
                {"error": "business_unit_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        documents = OrganizationDocument.objects.filter(business_unit_id=business_unit_id)
        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def expired(self, request):
        """
        Get expired documents
        """
        from django.utils import timezone
        today = timezone.now().date()

        documents = OrganizationDocument.objects.filter(expiry_date__lt=today)
        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def expiring_soon(self, request):
        """
        Get documents expiring soon (within 30 days)
        """
        from django.utils import timezone
        import datetime

        today = timezone.now().date()
        thirty_days_later = today + datetime.timedelta(days=30)

        documents = OrganizationDocument.objects.filter(
            expiry_date__gte=today,
            expiry_date__lte=thirty_days_later
        )
        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)
