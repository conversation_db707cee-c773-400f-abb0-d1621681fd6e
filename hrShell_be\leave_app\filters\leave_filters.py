import django_filters
from leave_app.models.leave_type import LeaveType
from leave_app.models.leave_policy import LeavePolicy
from leave_app.models.leave_balance import LeaveBalance
from leave_app.models.leave_request import LeaveRequest
from leave_app.models.leave_approval import LeaveApproval
from leave_app.models.holiday import Holiday
from leave_app.models.week_off import WeekOff


class LeaveTypeFilter(django_filters.FilterSet):
    """
    Filter for LeaveType model
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    code = django_filters.CharFilter(lookup_expr='icontains')
    is_paid = django_filters.BooleanFilter()
    carry_forward_allowed = django_filters.BooleanFilter()
    encashment_allowed = django_filters.BooleanFilter()
    applicable_during_probation = django_filters.BooleanFilter()
    requires_documentation = django_filters.BooleanFilter()
    status = django_filters.CharFilter()
    
    class Meta:
        model = LeaveType
        fields = [
            'name', 'code', 'organization', 'is_paid', 'carry_forward_allowed',
            'encashment_allowed', 'applicable_during_probation', 'requires_documentation',
            'status'
        ]


class LeavePolicyFilter(django_filters.FilterSet):
    """
    Filter for LeavePolicy model
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    leave_type = django_filters.NumberFilter()
    department = django_filters.NumberFilter()
    location = django_filters.NumberFilter()
    designation = django_filters.NumberFilter()
    business_unit = django_filters.NumberFilter()
    employee_type = django_filters.CharFilter(lookup_expr='icontains')
    accrual_method = django_filters.CharFilter()
    apply_sandwich_rule = django_filters.BooleanFilter()
    requires_approval = django_filters.BooleanFilter()
    
    class Meta:
        model = LeavePolicy
        fields = [
            'name', 'organization', 'leave_type', 'department', 'location',
            'designation', 'business_unit', 'employee_type', 'accrual_method',
            'apply_sandwich_rule', 'requires_approval'
        ]


class LeaveBalanceFilter(django_filters.FilterSet):
    """
    Filter for LeaveBalance model
    """
    employee = django_filters.NumberFilter()
    leave_type = django_filters.NumberFilter()
    year = django_filters.NumberFilter()
    
    class Meta:
        model = LeaveBalance
        fields = ['employee', 'leave_type', 'year']


class LeaveRequestFilter(django_filters.FilterSet):
    """
    Filter for LeaveRequest model
    """
    employee = django_filters.NumberFilter()
    leave_type = django_filters.NumberFilter()
    start_date = django_filters.DateFilter()
    start_date_gte = django_filters.DateFilter(field_name='start_date', lookup_expr='gte')
    start_date_lte = django_filters.DateFilter(field_name='start_date', lookup_expr='lte')
    end_date = django_filters.DateFilter()
    end_date_gte = django_filters.DateFilter(field_name='end_date', lookup_expr='gte')
    end_date_lte = django_filters.DateFilter(field_name='end_date', lookup_expr='lte')
    status = django_filters.CharFilter()
    approved_by = django_filters.NumberFilter()
    half_day = django_filters.BooleanFilter()
    
    class Meta:
        model = LeaveRequest
        fields = [
            'employee', 'leave_type', 'start_date', 'start_date_gte', 'start_date_lte',
            'end_date', 'end_date_gte', 'end_date_lte', 'status', 'approved_by', 'half_day'
        ]


class LeaveApprovalFilter(django_filters.FilterSet):
    """
    Filter for LeaveApproval model
    """
    leave_request = django_filters.NumberFilter()
    approver = django_filters.NumberFilter()
    level = django_filters.NumberFilter()
    status = django_filters.CharFilter()
    
    class Meta:
        model = LeaveApproval
        fields = ['leave_request', 'approver', 'level', 'status']


class HolidayFilter(django_filters.FilterSet):
    """
    Filter for Holiday model
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    location = django_filters.NumberFilter()
    date = django_filters.DateFilter()
    date_gte = django_filters.DateFilter(field_name='date', lookup_expr='gte')
    date_lte = django_filters.DateFilter(field_name='date', lookup_expr='lte')
    holiday_type = django_filters.CharFilter()
    is_recurring = django_filters.BooleanFilter()
    is_half_day = django_filters.BooleanFilter()
    
    class Meta:
        model = Holiday
        fields = [
            'name', 'organization', 'location', 'date', 'date_gte', 'date_lte',
            'holiday_type', 'is_recurring', 'is_half_day'
        ]


class WeekOffFilter(django_filters.FilterSet):
    """
    Filter for WeekOff model
    """
    organization = django_filters.NumberFilter()
    location = django_filters.NumberFilter()
    department = django_filters.NumberFilter()
    day_of_week = django_filters.NumberFilter()
    is_half_day = django_filters.BooleanFilter()
    
    class Meta:
        model = WeekOff
        fields = [
            'organization', 'location', 'department', 'day_of_week', 'is_half_day'
        ]
