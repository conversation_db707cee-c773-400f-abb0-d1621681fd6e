# Designation Model

The Designation model represents a job title or role in an organization.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `title` | String | Title of the designation | Yes |
| `organization` | ForeignKey | Organization this designation belongs to | Yes |
| `description` | Text | Description of the designation | No |
| `level` | String | Level of the designation (choices: junior, mid, senior, lead, manager, director, executive) | Yes (default: mid) |
| `department` | ForeignKey | Department this designation belongs to | No |
| `pay_grade` | String | Pay grade or band | No |
| `min_salary` | Decimal | Minimum salary for this designation | No |
| `max_salary` | Decimal | Maximum salary for this designation | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this designation belongs to |
| `department` | Department | The department this designation belongs to |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the title of the designation and department |

## Example

```python
designation = Designation.objects.create(
    title="Software Engineer",
    organization=acme_corp,
    description="Develops software applications",
    level="mid",
    department=engineering_dept,
    pay_grade="B2",
    min_salary=60000,
    max_salary=90000
)
```
