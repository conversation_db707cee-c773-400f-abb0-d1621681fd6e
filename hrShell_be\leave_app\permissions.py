from rest_framework import permissions


class IsLeaveAdmin(permissions.BasePermission):
    """
    Custom permission to only allow leave administrators to perform certain actions.
    """
    
    def has_permission(self, request, view):
        # Read permissions are allowed to any authenticated user
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated
        
        # Write permissions are only allowed to leave administrators
        return request.user.is_authenticated and (
            request.user.is_staff or 
            hasattr(request.user, 'employee') and request.user.employee.is_leave_admin
        )


class IsOwnLeaveOrAdmin(permissions.BasePermission):
    """
    Custom permission to only allow employees to view/edit their own leave requests,
    or administrators to view/edit any leave request.
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # Check if the user is a staff member or leave administrator
        is_admin = request.user.is_staff or (
            hasattr(request.user, 'employee') and request.user.employee.is_leave_admin
        )
        
        # Allow if user is admin or the leave request belongs to the user
        if hasattr(obj, 'employee'):
            return is_admin or obj.employee.user == request.user
        
        # For leave balances
        if hasattr(obj, 'employee') and hasattr(obj.employee, 'user'):
            return is_admin or obj.employee.user == request.user
        
        return is_admin


class IsLeaveApprover(permissions.BasePermission):
    """
    Custom permission to only allow designated approvers to approve/reject leave requests.
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # Check if the user is a staff member or leave administrator
        is_admin = request.user.is_staff or (
            hasattr(request.user, 'employee') and request.user.employee.is_leave_admin
        )
        
        # Allow if user is admin
        if is_admin:
            return True
        
        # For leave approvals, check if the user is the designated approver
        if hasattr(obj, 'approver') and hasattr(request.user, 'employee'):
            return obj.approver == request.user.employee
        
        # For leave requests, check if the user is in the approval chain
        if hasattr(obj, 'approvals') and hasattr(request.user, 'employee'):
            return obj.approvals.filter(approver=request.user.employee).exists()
        
        return False
