from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from payroll_app.models.payslip import Payslip
from payroll_app.models.payroll import PayrollItem
from payroll_app.serializers.payslip_serializer import PayslipSerializer
from payroll_app.filters.payroll_filters import PayslipFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class PayslipViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Payroll Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for payslips.
    """
    queryset = Payslip.objects.all()
    serializer_class = PayslipSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = PayslipFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email', 'payslip_number']
    ordering_fields = ['employee__first_name', 'month', 'year', 'generation_date', 'status', 'created_at']
    ordering = ['-year', '-month', 'employee__first_name', 'employee__last_name']
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all payslips with a specific status.
        """
        payslips = Payslip.objects.filter(status=status_value)
        page = self.paginate_queryset(payslips)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(payslips, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all payslips for a specific employee.
        """
        payslips = Payslip.objects.filter(employee_id=employee_pk)
        page = self.paginate_queryset(payslips)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(payslips, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def period(self, request):
        """
        Get all payslips for a specific month and year.
        """
        month = request.query_params.get('month')
        year = request.query_params.get('year')
        
        if not month or not year:
            return Response(
                {"detail": "Month and year parameters are required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        payslips = Payslip.objects.filter(month=month, year=year)
        page = self.paginate_queryset(payslips)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(payslips, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def generate_pdf(self, request, pk=None):
        """
        Generate PDF for a specific payslip.
        """
        payslip = self.get_object()
        
        # In a real implementation, you would generate a PDF here
        # For now, we'll just update the status
        
        payslip.status = 'generated'
        payslip.save()
        
        serializer = self.get_serializer(payslip)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def email(self, request, pk=None):
        """
        Email a specific payslip to the employee.
        """
        payslip = self.get_object()
        
        # In a real implementation, you would send an email here
        # For now, we'll just update the status
        
        payslip.status = 'emailed'
        payslip.email_sent = True
        payslip.email_sent_at = timezone.now()
        payslip.save()
        
        serializer = self.get_serializer(payslip)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def generate_for_payroll(self, request):
        """
        Generate payslips for all approved payroll items in a specific payroll.
        """
        payroll_id = request.data.get('payroll_id')
        
        if not payroll_id:
            return Response(
                {"detail": "Payroll ID is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get all approved payroll items for this payroll
        payroll_items = PayrollItem.objects.filter(
            payroll_id=payroll_id,
            status='approved'
        )
        
        payslips_created = 0
        
        for item in payroll_items:
            # Check if a payslip already exists for this payroll item
            if Payslip.objects.filter(payroll_item=item).exists():
                continue
            
            # Get the month and year from the payroll's payment date
            payment_date = item.payroll.payment_date
            month = payment_date.month
            year = payment_date.year
            
            # Generate a unique payslip number
            payslip_number = f"PS-{year}{month:02d}-{item.employee.id:04d}"
            
            # Create the payslip
            Payslip.objects.create(
                payroll_item=item,
                employee=item.employee,
                payslip_number=payslip_number,
                month=month,
                year=year,
                generation_date=timezone.now().date(),
                basic_salary=item.basic_salary,
                gross_earnings=item.gross_earnings,
                total_deductions=item.total_deductions,
                net_payable=item.net_payable,
                status='draft'
            )
            
            payslips_created += 1
        
        return Response({
            "detail": f"Generated {payslips_created} payslips.",
            "payslips_created": payslips_created
        })
