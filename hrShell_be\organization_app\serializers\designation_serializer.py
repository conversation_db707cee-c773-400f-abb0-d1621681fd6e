from rest_framework import serializers
from organization_app.models.designation import Designation, DesignationLevel


class DesignationSerializer(serializers.ModelSerializer):
    """
    Serializer for the Designation model
    """
    organization_name = serializers.ReadOnlyField(source='organization.name')
    department_name = serializers.ReadOnlyField(source='department.name')
    level_display = serializers.ReadOnlyField(source='get_level_display')
    
    class Meta:
        model = Designation
        fields = [
            'id', 'title', 'organization', 'organization_name', 'description',
            'level', 'level_display', 'department', 'department_name',
            'pay_grade', 'min_salary', 'max_salary', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_level(self, value):
        """
        Validate that the level is one of the allowed choices
        """
        try:
            DesignationLevel(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError(
                f"Level must be one of: {', '.join([l.value for l in DesignationLevel])}"
            )
    
    def validate(self, data):
        """
        Validate that the designation title is unique within the organization and department
        """
        organization = data.get('organization')
        title = data.get('title')
        department = data.get('department')
        
        # Skip validation if organization or title is not provided
        if not organization or not title:
            return data
        
        # Check if we're updating an existing instance
        instance = getattr(self, 'instance', None)
        if instance and instance.organization == organization and instance.title == title and instance.department == department:
            return data
        
        # Check if a designation with the same title already exists in the organization and department
        existing_query = Designation.objects.filter(organization=organization, title=title)
        if department:
            existing_query = existing_query.filter(department=department)
        elif 'department' in data:  # department is None in the new data
            existing_query = existing_query.filter(department__isnull=True)
            
        if existing_query.exists():
            raise serializers.ValidationError(
                {"title": "A designation with this title already exists in the organization and department."}
            )
        
        return data
