# LeaveApproval Model

The LeaveApproval model represents multi-level approval records for leave requests.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `leave_request` | ForeignKey | Leave request being approved | Yes |
| `approver` | ForeignKey | Employee approving the request | Yes |
| `level` | Integer | Approval level (1, 2, 3, etc.) | Yes (default: 1) |
| `status` | String | Status of the approval (choices: pending, approved, rejected, skipped) | Yes (default: pending) |
| `comments` | Text | Comments from the approver | No |
| `approval_date` | DateTime | Date and time of approval/rejection | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `leave_request` | LeaveRequest | The leave request being approved |
| `approver` | Employee | The employee approving the request |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the leave request, level, and status |

## Example

```python
leave_approval = LeaveApproval.objects.create(
    leave_request=leave_request,
    approver=jane_smith,
    level=1,
    status="pending"
)
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/leave-approvals/` | GET | List all leave approvals |
| `/api/v1/leave-approvals/` | POST | Create a new leave approval |
| `/api/v1/leave-approvals/{id}/` | GET | Retrieve a specific leave approval |
| `/api/v1/leave-approvals/{id}/` | PUT | Update a specific leave approval |
| `/api/v1/leave-approvals/{id}/` | DELETE | Delete a specific leave approval |
| `/api/v1/leave-approvals/approver/{approver_id}/` | GET | List leave approvals for a specific approver |
| `/api/v1/leave-approvals/pending-approvals/` | GET | List all pending leave approvals |
| `/api/v1/leave-approvals/{id}/approve/` | POST | Approve a leave approval |
| `/api/v1/leave-approvals/{id}/reject/` | POST | Reject a leave approval |
| `/api/v1/leave-approvals/{id}/skip/` | POST | Skip a leave approval |

## Approve/Reject/Skip API

### Approve

To approve a leave approval, send a POST request to `/api/v1/leave-approvals/{id}/approve/` with the following payload:

```json
{
  "comments": "Approved. Team coverage is sufficient."
}
```

### Reject

To reject a leave approval, send a POST request to `/api/v1/leave-approvals/{id}/reject/` with the following payload:

```json
{
  "comments": "Rejected. Insufficient team coverage during this period."
}
```

### Skip

To skip a leave approval, send a POST request to `/api/v1/leave-approvals/{id}/skip/` with the following payload:

```json
{
  "comments": "Skipped as the approver is on leave."
}
```
