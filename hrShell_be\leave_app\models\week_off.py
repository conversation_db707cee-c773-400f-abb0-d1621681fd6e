from django.db import models
from enum import Enum


class DayOfWeek(Enum):
    MONDAY = 0
    TUESDAY = 1
    WEDNESDAY = 2
    THURSDAY = 3
    FRIDAY = 4
    SATURDAY = 5
    SUNDAY = 6
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class WeekOff(models.Model):
    """
    WeekOff model for tracking weekly off days
    """
    # Associations
    organization = models.ForeignKey(
        'organization_app.Organization',
        on_delete=models.CASCADE,
        related_name='week_offs'
    )
    
    # Specific location (optional)
    location = models.ForeignKey(
        'organization_app.Location',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='week_offs'
    )
    
    # Specific department (optional)
    department = models.ForeignKey(
        'employees_app.Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='week_offs'
    )
    
    # Day of week
    day_of_week = models.IntegerField(
        choices=DayOfWeek.choices(),
        default=DayOfWeek.SUNDAY.value
    )
    
    # Half day
    is_half_day = models.BooleanField(
        default=False,
        help_text="Whether this is a half-day off"
    )
    
    # First half or second half (if half day)
    first_half = models.BooleanField(
        default=True,
        help_text="If is_half_day is True, whether it's the first half or second half"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['day_of_week']
        verbose_name = "Week Off"
        verbose_name_plural = "Week Offs"
        unique_together = ('organization', 'location', 'department', 'day_of_week')
    
    def __str__(self):
        location_name = self.location.name if self.location else "All Locations"
        department_name = self.department.name if self.department else "All Departments"
        return f"{self.get_day_of_week_display()} - {location_name} - {department_name}"
