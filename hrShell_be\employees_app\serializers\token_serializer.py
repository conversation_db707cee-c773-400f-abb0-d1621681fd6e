from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import logging

logger = logging.getLogger(__name__)


class LoginSerializer(TokenObtainPairSerializer):
    """
    Enhanced login serializer with better validation, error handling, and user information.

    This serializer handles user authentication and returns JWT tokens along with
    comprehensive user information for the HR Shell application.
    """

    # Override field names for better API documentation
    username = serializers.Char<PERSON>ield(
        max_length=150,
        help_text="Username for authentication",
        style={'placeholder': 'Enter your username'}
    )
    password = serializers.CharField(
        max_length=128,
        write_only=True,
        help_text="Password for authentication",
        style={'input_type': 'password', 'placeholder': 'Enter your password'}
    )

    class Meta:
        ref_name = 'LoginRequest'

    @classmethod
    def get_token(cls, user):
        """
        Generate JWT token with custom claims for the authenticated user.

        Args:
            user: Django User instance

        Returns:
            JWT token with custom claims
        """
        token = super().get_token(user)

        # Add custom claims to the token
        token['username'] = user.username
        token['email'] = user.email
        token['full_name'] = f"{user.first_name} {user.last_name}".strip()
        token['is_staff'] = user.is_staff
        token['is_superuser'] = user.is_superuser
        token['last_login'] = user.last_login.isoformat() if user.last_login else None

        return token

    def validate(self, attrs):
        """
        Validate user credentials and return authentication data.

        Args:
            attrs: Dictionary containing username and password

        Returns:
            Dictionary containing tokens and user information

        Raises:
            serializers.ValidationError: If credentials are invalid
        """
        username = attrs.get('username')
        password = attrs.get('password')

        if not username or not password:
            raise serializers.ValidationError({
                'detail': 'Both username and password are required.'
            })

        # Authenticate user
        user = authenticate(username=username, password=password)

        if not user:
            # Log failed login attempt
            logger.warning(f"Failed login attempt for username: {username}")
            raise serializers.ValidationError({
                'detail': 'Invalid username or password. Please check your credentials and try again.'
            })

        if not user.is_active:
            logger.warning(f"Login attempt for inactive user: {username}")
            raise serializers.ValidationError({
                'detail': 'This account has been deactivated. Please contact your administrator.'
            })

        # Update last login
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])

        # Log successful login
        logger.info(f"Successful login for user: {username}")

        # Get tokens
        refresh = self.get_token(user)
        access = refresh.access_token

        # Prepare response data
        data = {
            'access': str(access),
            'refresh': str(refresh),
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': f"{user.first_name} {user.last_name}".strip(),
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'date_joined': user.date_joined.isoformat()
            },
            'message': 'Login successful'
        }

        return data


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Legacy custom token serializer for backward compatibility.

    Note: This is maintained for backward compatibility.
    New implementations should use LoginSerializer.
    """
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)

        # Add custom claims
        token['username'] = user.username
        token['email'] = user.email
        token['is_staff'] = user.is_staff
        token['is_superuser'] = user.is_superuser

        return token

    def validate(self, attrs):
        data = super().validate(attrs)

        # Add extra response data
        user = self.user
        data['user_id'] = user.id
        data['username'] = user.username
        data['email'] = user.email
        data['is_staff'] = user.is_staff
        data['is_superuser'] = user.is_superuser

        return data


class LoginView(TokenObtainPairView):
    """
    Enhanced login view with comprehensive documentation and error handling.

    This view handles user authentication for the HR Shell application,
    providing JWT tokens and user information upon successful login.
    """
    serializer_class = LoginSerializer

    def get_serializer_class(self):
        """Return the appropriate serializer class."""
        return LoginSerializer


@swagger_auto_schema(
    tags=['Authentication'],
    operation_description="Legacy token obtain pair view for backward compatibility"
)
class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Legacy custom token view for backward compatibility.

    Note: This is maintained for backward compatibility.
    New implementations should use LoginView.
    """
    serializer_class = CustomTokenObtainPairSerializer
