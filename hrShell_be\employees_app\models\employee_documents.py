from django.db import models


class EmployeeDocument(models.Model):
    """
    Employee document model for storing employee documents
    """

    # Document type choices
    DOCUMENT_TYPE_CHOICES = [
        ('resume', 'Resume'),
        ('contract', 'Contract'),
        ('offer_letter', 'Offer Letter'),
        ('experience_certificate', 'Experience Certificate'),
        ('qualification_certificate', 'Qualification Certificate'),
        ('id_proof', 'ID Proof'),
        ('address_proof', 'Address Proof'),
        ('bank_details', 'Bank Details'),
        ('other', 'Other'),
    ]

    employee = models.ForeignKey(
        'Employee',
        on_delete=models.CASCADE,
        related_name='documents'
    )

    # Generic fields for flexible document management
    document_type = models.CharField(
        max_length=50,
        choices=DOCUMENT_TYPE_CHOICES,
        default='other',
        help_text="Type of document being uploaded"
    )
    file = models.FileField(
        upload_to='employee_documents/%Y/%m/',
        null=True,
        blank=True,
        help_text="Document file"
    )
    document_name = models.Char<PERSON>ield(
        max_length=255,
        blank=True,
        null=True,
        help_text="Name or title of the document"
    )
    description = models.TextField(
        blank=True,
        null=True,
        help_text="Description of the document"
    )
    upload_date = models.DateField(
        auto_now_add=True,
        help_text="Date when document was uploaded"
    )
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether the document has been verified"
    )
    resume = models.FileField(
        upload_to='employee_documents/resumes/',
        null=True,
        blank=True
    )
    contract = models.FileField(
        upload_to='employee_documents/contracts/',
        null=True,
        blank=True
    )
    signed_offer_letter = models.FileField(
        upload_to='employee_documents/offer_letters/',
        null=True,
        blank=True
    )
    experience_certificates = models.FileField(
        upload_to='employee_documents/experience_certificates/',
        null=True,
        blank=True
    )
    qualification_certificates = models.FileField(
        upload_to='employee_documents/qualification_certificates/',
        null=True,
        blank=True
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Employee Document"
        verbose_name_plural = "Employee Documents"
        ordering = ['-created_at']

    def __str__(self):
        if self.document_name:
            return f"{self.document_name} - {self.employee}"
        return f"{self.get_document_type_display()} for {self.employee}"
