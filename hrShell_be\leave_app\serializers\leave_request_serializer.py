from rest_framework import serializers
from leave_app.models.leave_request import LeaveRequest, LeaveRequestStatus
from leave_app.models.leave_balance import LeaveBalance
from django.utils import timezone


class LeaveRequestSerializer(serializers.ModelSerializer):
    """
    Serializer for the LeaveRequest model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    leave_type_name = serializers.ReadOnlyField(source='leave_type.name')
    approved_by_name = serializers.ReadOnlyField(source='approved_by.full_name')
    status_display = serializers.ReadOnlyField(source='get_status_display')
    
    class Meta:
        model = LeaveRequest
        fields = [
            'id', 'employee', 'employee_name', 'leave_type', 'leave_type_name',
            'start_date', 'end_date', 'total_days', 'half_day', 'first_half',
            'reason', 'attachment', 'status', 'status_display', 'approved_by',
            'approved_by_name', 'approval_date', 'rejection_reason',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['total_days', 'approved_by', 'approval_date', 'created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate leave request data
        """
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        half_day = data.get('half_day', False)
        employee = data.get('employee')
        leave_type = data.get('leave_type')
        
        # Validate start and end dates
        if not half_day and end_date < start_date:
            raise serializers.ValidationError("End date must be after or equal to start date")
        
        # If half day, start and end date must be the same
        if half_day and start_date != end_date:
            raise serializers.ValidationError("For half-day leave, start and end date must be the same")
        
        # Check for overlapping leave requests
        instance = self.instance
        overlapping_requests = LeaveRequest.objects.filter(
            employee=employee,
            status=LeaveRequestStatus.APPROVED.value
        ).filter(
            # Leave starts within the range or before the range and ends within or after the range
            (
                (serializers.Q(start_date__lte=start_date) & serializers.Q(end_date__gte=start_date)) |
                (serializers.Q(start_date__lte=end_date) & serializers.Q(end_date__gte=end_date)) |
                (serializers.Q(start_date__gte=start_date) & serializers.Q(end_date__lte=end_date))
            )
        )
        
        # Exclude current instance if updating
        if instance:
            overlapping_requests = overlapping_requests.exclude(id=instance.id)
        
        if overlapping_requests.exists():
            raise serializers.ValidationError("There is already an approved leave request for this period")
        
        # Check leave balance
        current_year = timezone.now().year
        try:
            leave_balance = LeaveBalance.objects.get(
                employee=employee,
                leave_type=leave_type,
                year=current_year
            )
            
            # Calculate total days
            if half_day:
                total_days = 0.5
            else:
                delta = end_date - start_date
                total_days = delta.days + 1
            
            # Check if employee has enough balance
            if leave_balance.closing_balance < total_days:
                raise serializers.ValidationError(
                    f"Insufficient leave balance. Available: {leave_balance.closing_balance}, Requested: {total_days}"
                )
                
        except LeaveBalance.DoesNotExist:
            # If no balance exists, create one with zero balance
            leave_balance = LeaveBalance.objects.create(
                employee=employee,
                leave_type=leave_type,
                year=current_year
            )
            
            # Only allow if leave type allows negative balance or if it's unpaid leave
            if not leave_type.is_paid:
                pass  # Allow unpaid leave even with zero balance
            else:
                raise serializers.ValidationError("No leave balance found for this leave type")
        
        return data


class LeaveRequestDetailSerializer(LeaveRequestSerializer):
    """
    Detailed serializer for the LeaveRequest model including approvals
    """
    from leave_app.serializers.leave_approval_serializer import LeaveApprovalSerializer
    
    approvals = LeaveApprovalSerializer(many=True, read_only=True)
    
    class Meta(LeaveRequestSerializer.Meta):
        fields = LeaveRequestSerializer.Meta.fields + ['approvals']
