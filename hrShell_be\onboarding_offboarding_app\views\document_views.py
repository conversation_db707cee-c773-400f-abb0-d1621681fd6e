from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from onboarding_offboarding_app.models.document import Document, DocumentTemplate
from onboarding_offboarding_app.serializers.document_serializer import DocumentSerializer, DocumentTemplateSerializer
from onboarding_offboarding_app.filters.onboarding_offboarding_filters import DocumentFilter, DocumentTemplateFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class DocumentTemplateViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Document Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for document templates.
    """
    queryset = DocumentTemplate.objects.all()
    serializer_class = DocumentTemplateSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = DocumentTemplateFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'document_type', 'created_at']
    ordering = ['name']

    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all document templates for a specific organization.
        """
        templates = DocumentTemplate.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(templates)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(templates, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def document_type(self, request, document_type=None):
        """
        Get all document templates with a specific document type.
        """
        templates = DocumentTemplate.objects.filter(document_type=document_type)
        page = self.paginate_queryset(templates)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(templates, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get all active document templates.
        """
        templates = DocumentTemplate.objects.filter(is_active=True)
        page = self.paginate_queryset(templates)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(templates, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """
        Activate a document template.
        """
        template = self.get_object()

        if template.is_active:
            return Response(
                {"detail": "Template is already active."},
                status=status.HTTP_400_BAD_REQUEST
            )

        template.is_active = True
        template.save()

        serializer = self.get_serializer(template)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """
        Deactivate a document template.
        """
        template = self.get_object()

        if not template.is_active:
            return Response(
                {"detail": "Template is already inactive."},
                status=status.HTTP_400_BAD_REQUEST
            )

        template.is_active = False
        template.save()

        serializer = self.get_serializer(template)
        return Response(serializer.data)


class DocumentViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Document Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for documents.
    """
    queryset = Document.objects.all()
    serializer_class = DocumentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = DocumentFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'document_type', 'status', 'created_at']
    ordering = ['-created_at']

    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all documents for a specific employee.
        """
        documents = Document.objects.filter(employee_id=employee_pk)
        page = self.paginate_queryset(documents)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def document_type(self, request, document_type=None):
        """
        Get all documents with a specific document type.
        """
        documents = Document.objects.filter(document_type=document_type)
        page = self.paginate_queryset(documents)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all documents with a specific status.
        """
        documents = Document.objects.filter(status=status_value)
        page = self.paginate_queryset(documents)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def template(self, request, template_pk=None):
        """
        Get all documents for a specific template.
        """
        documents = Document.objects.filter(template_id=template_pk)
        page = self.paginate_queryset(documents)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def submit(self, request, pk=None):
        """
        Submit a document.
        """
        document = self.get_object()

        if document.status != 'pending':
            return Response(
                {"detail": "Only pending documents can be submitted."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate document file is provided
        if not document.document_file and 'document_file' not in request.data:
            return Response(
                {"detail": "Document file is required for submission."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update document file if provided
        if 'document_file' in request.data:
            document.document_file = request.data.get('document_file')

        document.status = 'submitted'
        document.save()

        serializer = self.get_serializer(document)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """
        Verify a document.
        """
        document = self.get_object()

        if document.status != 'submitted':
            return Response(
                {"detail": "Only submitted documents can be verified."},
                status=status.HTTP_400_BAD_REQUEST
            )

        document.status = 'verified'
        document.verified_at = timezone.now()

        # Set verified_by if provided
        verified_by_id = request.data.get('verified_by')
        if verified_by_id:
            document.verified_by_id = verified_by_id

        document.save()

        serializer = self.get_serializer(document)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """
        Reject a document.
        """
        document = self.get_object()

        if document.status != 'submitted':
            return Response(
                {"detail": "Only submitted documents can be rejected."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate rejection reason is provided
        if 'rejection_reason' not in request.data:
            return Response(
                {"detail": "Rejection reason is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        document.status = 'rejected'
        document.rejection_reason = request.data.get('rejection_reason')

        # Set verified_by if provided
        verified_by_id = request.data.get('verified_by')
        if verified_by_id:
            document.verified_by_id = verified_by_id

        document.save()

        serializer = self.get_serializer(document)
        return Response(serializer.data)
