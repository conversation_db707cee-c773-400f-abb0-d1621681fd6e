#!/usr/bin/env python3
"""
Test script to verify file size functionality in employee documents API.
"""

import os
import sys
import django
import requests
from io import BytesIO

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hrShell_be.settings')
django.setup()

from employees_app.models.employee_documents import EmployeeDocument
from employees_app.models.employee import Employee
from django.core.files.uploadedfile import SimpleUploadedFile

def test_file_size_functionality():
    """Test file size functionality"""
    print("🧪 Testing File Size Functionality...")
    
    # Get an employee
    employee = Employee.objects.first()
    if not employee:
        print("❌ No employees found")
        return
    
    print(f"✅ Using employee: {employee}")
    
    # Test 1: Create documents with different file sizes
    print("\n📝 Test 1: Creating documents with different file sizes...")
    
    test_files = [
        {
            'name': 'small_file.txt',
            'content': b'Small file content',
            'expected_size': len(b'Small file content')
        },
        {
            'name': 'medium_file.txt', 
            'content': b'Medium file content ' * 100,  # ~2KB
            'expected_size': len(b'Medium file content ' * 100)
        },
        {
            'name': 'large_file.txt',
            'content': b'Large file content ' * 10000,  # ~190KB
            'expected_size': len(b'Large file content ' * 10000)
        }
    ]
    
    created_docs = []
    
    for i, file_info in enumerate(test_files):
        try:
            # Create file
            test_file = SimpleUploadedFile(
                name=file_info['name'],
                content=file_info['content'],
                content_type="text/plain"
            )
            
            # Create document via API
            data = {
                'employee': employee.id,
                'document_type': 'resume',
                'document_name': f'Test Document {i+1}',
                'description': f'Test file with size {file_info["expected_size"]} bytes'
            }
            
            files = {
                'file': (file_info['name'], BytesIO(file_info['content']), 'text/plain')
            }
            
            response = requests.post(
                'http://127.0.0.1:8000/api/v1/employee-documents/',
                data=data,
                files=files
            )
            
            if response.status_code == 201:
                doc_data = response.json()
                created_docs.append(doc_data)
                
                print(f"✅ Created {file_info['name']}:")
                print(f"   - ID: {doc_data['id']}")
                print(f"   - File Size (bytes): {doc_data.get('file_size')}")
                print(f"   - File Size (formatted): {doc_data.get('file_size_formatted')}")
                print(f"   - Expected Size: {file_info['expected_size']} bytes")
                
                # Verify size matches
                if doc_data.get('file_size') == file_info['expected_size']:
                    print(f"   ✅ Size matches expected value")
                else:
                    print(f"   ❌ Size mismatch: got {doc_data.get('file_size')}, expected {file_info['expected_size']}")
            else:
                print(f"❌ Failed to create {file_info['name']}: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error creating {file_info['name']}: {e}")
    
    # Test 2: Test list API with file sizes
    print(f"\n📋 Test 2: Testing list API with file sizes...")
    try:
        response = requests.get(f'http://127.0.0.1:8000/api/v1/employee-documents/?employee={employee.id}')
        
        if response.status_code == 200:
            data = response.json()
            documents = data.get('results', [])
            
            print(f"✅ Retrieved {len(documents)} documents")
            
            for doc in documents:
                if doc.get('file_size') is not None:
                    print(f"  - Document {doc['id']}: {doc.get('document_name', 'Unnamed')}")
                    print(f"    File: {doc.get('file', 'No file')}")
                    print(f"    Size: {doc.get('file_size')} bytes ({doc.get('file_size_formatted')})")
                else:
                    print(f"  - Document {doc['id']}: No file attached")
        else:
            print(f"❌ Failed to retrieve documents: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing list API: {e}")
    
    # Test 3: Test file size formatting
    print(f"\n🔢 Test 3: Testing file size formatting...")
    
    from employees_app.serializers.employee_document_serializer import EmployeeDocumentSerializer
    
    serializer = EmployeeDocumentSerializer()
    
    test_sizes = [
        (0, "0 B"),
        (512, "512 B"),
        (1024, "1 KB"),
        (1536, "1.5 KB"),
        (1048576, "1 MB"),
        (1572864, "1.5 MB"),
        (1073741824, "1 GB"),
        (1099511627776, "1 TB")
    ]
    
    for size_bytes, expected in test_sizes:
        formatted = serializer._format_file_size(size_bytes)
        if formatted == expected:
            print(f"✅ {size_bytes} bytes -> {formatted}")
        else:
            print(f"❌ {size_bytes} bytes -> {formatted} (expected {expected})")
    
    print(f"\n🎉 File size functionality test completed!")

if __name__ == "__main__":
    test_file_size_functionality()
