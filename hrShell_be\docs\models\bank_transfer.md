# Bank Transfer Model

The BankTransfer model represents bank transfers for salary disbursement.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `payroll` | ForeignKey | Payroll this transfer is for | No |
| `organization` | ForeignKey | Organization this transfer belongs to | Yes |
| `reference_number` | String | Unique reference number | Yes |
| `transfer_date` | Date | Date of transfer | Yes |
| `bank_name` | String | Name of the bank | Yes |
| `account_number` | String | Account number | Yes |
| `transfer_type` | String | Type of transfer | Yes (default: salary) |
| `total_amount` | Decimal | Total amount of the transfer | Yes |
| `total_employees` | Integer | Total number of employees | Yes (default: 0) |
| `file_format` | String | Format of the transfer file | Yes (default: csv) |
| `transfer_file` | FileField | Transfer file | No |
| `status` | String | Status of the transfer | Yes (default: draft) |
| `remarks` | Text | Any remarks about this transfer | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Choices

### Status Choices
- `draft`: Draft
- `generated`: Generated
- `sent`: Sent to Bank
- `processed`: Processed
- `completed`: Completed
- `failed`: Failed

### Transfer Type Choices
- `salary`: Salary
- `bonus`: Bonus
- `reimbursement`: Reimbursement
- `other`: Other

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `payroll` | Payroll | The payroll this transfer is for |
| `organization` | Organization | The organization this transfer belongs to |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the reference number, transfer date, and total amount |

## Example

```python
bank_transfer = BankTransfer.objects.create(
    payroll=payroll,
    organization=acme_corp,
    reference_number="BT-202305-0001",
    transfer_date=date(2023, 5, 1),
    bank_name="ACME Bank",
    account_number="**********",
    transfer_type="salary",
    total_amount=1000000.00,
    total_employees=50,
    file_format="csv",
    status="draft"
)
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/bank-transfers/` | GET | List all bank transfers |
| `/api/v1/bank-transfers/` | POST | Create a new bank transfer |
| `/api/v1/bank-transfers/{id}/` | GET | Retrieve a specific bank transfer |
| `/api/v1/bank-transfers/{id}/` | PUT | Update a specific bank transfer |
| `/api/v1/bank-transfers/{id}/` | DELETE | Delete a specific bank transfer |
| `/api/v1/bank-transfers/status/{status}/` | GET | List all bank transfers with a specific status |
| `/api/v1/bank-transfers/organization/{organization_id}/` | GET | List all bank transfers for a specific organization |
| `/api/v1/bank-transfers/payroll/{payroll_id}/` | GET | List all bank transfers for a specific payroll |
| `/api/v1/bank-transfers/{id}/generate-file/` | POST | Generate a bank transfer file for a specific bank transfer |
| `/api/v1/bank-transfers/{id}/mark-sent/` | POST | Mark a bank transfer as sent to the bank |
| `/api/v1/bank-transfers/{id}/mark-processed/` | POST | Mark a bank transfer as processed by the bank |
| `/api/v1/bank-transfers/{id}/mark-completed/` | POST | Mark a bank transfer as completed |
| `/api/v1/bank-transfers/{id}/mark-failed/` | POST | Mark a bank transfer as failed |

## Bank Transfer Process

1. **Create Bank Transfer**: Create a bank transfer for a specific payroll
2. **Generate File**: Generate a bank transfer file in the required format (CSV, NEFT, etc.)
3. **Send to Bank**: Send the file to the bank for processing
4. **Process Transfer**: Bank processes the transfer
5. **Complete Transfer**: Mark the transfer as completed once the bank confirms the transfer

## Bank Transfer File Formats

### CSV Format

The CSV format includes the following columns:

```
Employee ID, Employee Name, Account Number, Bank Name, IFSC Code, Amount, Reference Number
```

Example:
```
1001, John Doe, **********, ACME Bank, ACME0001234, 77000.00, SAL-202304-1001
1002, Jane Smith, **********, XYZ Bank, XYZ0009876, 88000.00, SAL-202304-1002
```

### NEFT Format

The NEFT format includes the following columns:

```
Beneficiary Account Number, Beneficiary Name, Beneficiary Bank, Beneficiary Branch, IFSC Code, Amount, Remarks
```

Example:
```
**********, John Doe, ACME Bank, Main Branch, ACME0001234, 77000.00, Salary for April 2023
**********, Jane Smith, XYZ Bank, City Branch, XYZ0009876, 88000.00, Salary for April 2023
```

## Integration with Accounting Software

The bank transfer module can be integrated with accounting software like Tally, QuickBooks, etc. This integration allows for:

1. **Automatic Journal Entry**: Create journal entries in the accounting software for salary disbursements
2. **Reconciliation**: Reconcile bank statements with salary disbursements
3. **Financial Reporting**: Generate financial reports for salary expenses

## Security Measures

The bank transfer module includes the following security measures:

1. **Encryption**: All bank account details are encrypted in the database
2. **Two-Factor Authentication**: Two-factor authentication is required for generating and sending bank transfer files
3. **Audit Trail**: All actions related to bank transfers are logged for audit purposes
4. **Role-Based Access Control**: Only authorized users can access and manage bank transfers
5. **IP Restriction**: Bank transfer files can only be generated from authorized IP addresses

## Error Handling

The bank transfer module includes the following error handling mechanisms:

1. **Validation**: Validate all bank account details before generating the transfer file
2. **Retry Mechanism**: Retry failed transfers automatically
3. **Notification**: Notify administrators of failed transfers
4. **Manual Intervention**: Allow manual intervention for failed transfers
5. **Rollback**: Roll back changes if a transfer fails
