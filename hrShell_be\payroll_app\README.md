# Payroll Management Module

The Payroll Management Module automates the salary calculation, deductions, statutory compliance, payslip generation, and disbursement processes. It ensures timely and accurate salary processing based on attendance, leaves, taxes, and benefits.

## Core Features

### 1. Salary Structure Definition
- Set up components:
  - Earnings: Basic, HRA, Allowances, Bonus, Overtime
  - Deductions: Provident Fund, ESI, TDS, Loan EMI, Professional Tax
- Support for:
  - Fixed salary
  - Variable pay (performance-based)
  - Hourly wage

### 2. Employee Salary Assignment
- Link salary structure per employee
- Effective from date
- Revision history (salary hikes)

### 3. Attendance & Leave Integration
- Calculate pay based on:
  - Working days
  - LOP (Leave Without Pay)
  - Overtime hours
- Pull data from Attendance/Leave module

### 4. Payroll Processing
- Monthly payroll run
- Preview salary sheet before finalization
- Auto-calculation:
  - Gross Pay
  - Total Deductions
  - Net Pay

### 5. Payslip Generation
- Auto-generate PDF payslips for all employees
- Includes salary breakup, deductions, and net payable
- Download or email payslips to employees

### 6. Salary Disbursement
- Generate bank transfer statements (CSV, NEFT format)
- Integration with accounting software (like Tally, QuickBooks)

### 7. Loans & Advances
- Track company-issued loans
- EMI deduction schedules
- Interest calculation (if applicable)

### 8. Bonus & Incentive Management
- Handle one-time payments, bonuses, performance incentives

### 9. Full & Final Settlement (FnF)
- On resignation/termination:
  - Pro-rata salary
  - Leave encashment
  - Gratuity, notice period pay
  - Deductions

### 10. Reports & Analytics
- Payroll summary report (monthly/annually)
- Payslip register
- Deduction breakdown (PF, ESI, TDS)
- Bank statement for salary disbursement
- Audit trail of salary changes

## Models

### Salary Structure
- **SalaryComponent**: Defines individual salary components (Basic, HRA, etc.)
- **SalaryStructure**: Defines salary structures that can be assigned to employees

### Employee Salary
- **EmployeeSalary**: Assigns salary structure to employees with specific component values
- **EmployeeSalaryComponent**: Stores specific component values for an employee's salary
- **SalaryRevision**: Tracks salary revisions (hikes, promotions, etc.)

### Payroll Processing
- **Payroll**: Represents a payroll run for a specific period
- **PayrollItem**: Represents an individual employee's payroll calculation
- **PayrollItemComponent**: Stores the breakdown of earnings and deductions for a payroll item

### Payslip
- **Payslip**: Represents a payslip generated for an employee

### Loans
- **Loan**: Represents loans given to employees
- **LoanInstallment**: Tracks loan installment payments

### Bonus
- **Bonus**: Represents bonuses and incentives given to employees
- **BonusBatch**: Represents a batch of bonuses for multiple employees

### Tax
- **TaxSlab**: Defines tax slabs for income tax calculation
- **EmployeeTaxDeclaration**: Stores employee tax declarations and exemptions

### Bank Transfer
- **BankTransfer**: Represents bank transfers for salary disbursement

## API Endpoints

### Salary Structure
- `GET /api/v1/salary-components/`: List all salary components
- `POST /api/v1/salary-components/`: Create a new salary component
- `GET /api/v1/salary-structures/`: List all salary structures
- `POST /api/v1/salary-structures/`: Create a new salary structure

### Employee Salary
- `GET /api/v1/employee-salaries/`: List all employee salaries
- `POST /api/v1/employee-salaries/`: Create a new employee salary
- `GET /api/v1/employee-salaries/employee/{employee_id}/`: List salaries for a specific employee
- `GET /api/v1/employee-salaries/employee/{employee_id}/current/`: Get current salary for a specific employee

### Payroll Processing
- `GET /api/v1/payrolls/`: List all payrolls
- `POST /api/v1/payrolls/`: Create a new payroll
- `GET /api/v1/payroll-items/`: List all payroll items
- `POST /api/v1/payroll-items/`: Create a new payroll item
- `POST /api/v1/payrolls/{id}/generate-items/`: Generate payroll items for all active employees
- `POST /api/v1/payrolls/{id}/calculate/`: Calculate all payroll items for this payroll
- `POST /api/v1/payrolls/{id}/finalize/`: Finalize the payroll and mark it as completed

### Payslip
- `GET /api/v1/payslips/`: List all payslips
- `POST /api/v1/payslips/`: Create a new payslip
- `GET /api/v1/payslips/employee/{employee_id}/`: List payslips for a specific employee
- `POST /api/v1/payslips/{id}/generate-pdf/`: Generate PDF for a specific payslip
- `POST /api/v1/payslips/{id}/email/`: Email a specific payslip to the employee
- `POST /api/v1/payslips/generate-for-payroll/`: Generate payslips for all approved payroll items in a specific payroll

### Loans
- `GET /api/v1/loans/`: List all loans
- `POST /api/v1/loans/`: Create a new loan
- `GET /api/v1/loan-installments/`: List all loan installments
- `POST /api/v1/loan-installments/`: Create a new loan installment
- `GET /api/v1/loans/employee/{employee_id}/`: List loans for a specific employee
- `POST /api/v1/loans/{id}/approve/`: Approve a specific loan
- `POST /api/v1/loans/{id}/reject/`: Reject a specific loan
- `POST /api/v1/loans/{id}/activate/`: Activate a specific loan and generate installments
- `POST /api/v1/loan-installments/{id}/pay/`: Mark an installment as paid

### Bonus
- `GET /api/v1/bonuses/`: List all bonuses
- `POST /api/v1/bonuses/`: Create a new bonus
- `GET /api/v1/bonus-batches/`: List all bonus batches
- `POST /api/v1/bonus-batches/`: Create a new bonus batch
- `GET /api/v1/bonuses/employee/{employee_id}/`: List bonuses for a specific employee
- `POST /api/v1/bonuses/{id}/approve/`: Approve a specific bonus
- `POST /api/v1/bonuses/{id}/reject/`: Reject a specific bonus
- `POST /api/v1/bonuses/{id}/mark-paid/`: Mark a specific bonus as paid
- `POST /api/v1/bonuses/create-batch/`: Create bonuses for multiple employees in a batch

### Tax
- `GET /api/v1/tax-slabs/`: List all tax slabs
- `POST /api/v1/tax-slabs/`: Create a new tax slab
- `GET /api/v1/tax-declarations/`: List all tax declarations
- `POST /api/v1/tax-declarations/`: Create a new tax declaration
- `GET /api/v1/tax-declarations/employee/{employee_id}/`: List tax declarations for a specific employee
- `POST /api/v1/tax-declarations/{id}/verify/`: Verify a specific tax declaration
- `POST /api/v1/tax-declarations/{id}/reject/`: Reject a specific tax declaration

### Bank Transfer
- `GET /api/v1/bank-transfers/`: List all bank transfers
- `POST /api/v1/bank-transfers/`: Create a new bank transfer
- `POST /api/v1/bank-transfers/{id}/generate-file/`: Generate a bank transfer file for a specific bank transfer
- `POST /api/v1/bank-transfers/{id}/mark-sent/`: Mark a bank transfer as sent to the bank
- `POST /api/v1/bank-transfers/{id}/mark-processed/`: Mark a bank transfer as processed by the bank
- `POST /api/v1/bank-transfers/{id}/mark-completed/`: Mark a bank transfer as completed
- `POST /api/v1/bank-transfers/{id}/mark-failed/`: Mark a bank transfer as failed

## Integration with Other Modules

The Payroll Management Module integrates with:

1. **Employee Module**: For employee information
2. **Organization Module**: For organization, department, and location information
3. **Leave Module**: For leave information to calculate LOP days
4. **Attendance Module**: For attendance information to calculate working days and overtime
