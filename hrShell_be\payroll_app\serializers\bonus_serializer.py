from rest_framework import serializers
from payroll_app.models.bonus import Bonus, BonusBatch


class BonusSerializer(serializers.ModelSerializer):
    employee_name = serializers.SerializerMethodField()
    bonus_type_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    approved_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Bonus
        fields = [
            'id', 'employee', 'employee_name', 'bonus_type', 'bonus_type_display',
            'amount', 'payment_date', 'reference_period_start', 'reference_period_end',
            'status', 'status_display', 'description', 'is_taxable', 'is_prorated',
            'approved_by', 'approved_by_name', 'approval_date', 'rejection_reason',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_employee_name(self, obj):
        return f"{obj.employee.first_name} {obj.employee.last_name}" if obj.employee else None
    
    def get_bonus_type_display(self, obj):
        return obj.get_bonus_type_display()
    
    def get_status_display(self, obj):
        return obj.get_status_display()
    
    def get_approved_by_name(self, obj):
        if not obj.approved_by:
            return None
        return f"{obj.approved_by.first_name} {obj.approved_by.last_name}"
    
    def validate(self, data):
        # Validate amount
        if data.get('amount', 0) <= 0:
            raise serializers.ValidationError({"amount": "Bonus amount must be greater than 0"})
        
        # Validate reference period
        if 'reference_period_start' in data and 'reference_period_end' in data:
            if data['reference_period_start'] and data['reference_period_end']:
                if data['reference_period_start'] >= data['reference_period_end']:
                    raise serializers.ValidationError({"reference_period_end": "End date must be after start date"})
        
        return data


class BonusBatchSerializer(serializers.ModelSerializer):
    organization_name = serializers.SerializerMethodField()
    bonus_type_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    approved_by_name = serializers.SerializerMethodField()
    bonuses_count = serializers.SerializerMethodField()
    
    class Meta:
        model = BonusBatch
        fields = [
            'id', 'name', 'organization', 'organization_name', 'bonus_type',
            'bonus_type_display', 'payment_date', 'reference_period_start',
            'reference_period_end', 'status', 'status_display', 'description',
            'total_amount', 'created_by', 'created_by_name', 'approved_by',
            'approved_by_name', 'bonuses_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'bonuses_count']
    
    def get_organization_name(self, obj):
        return obj.organization.name if obj.organization else None
    
    def get_bonus_type_display(self, obj):
        return obj.get_bonus_type_display()
    
    def get_status_display(self, obj):
        return obj.get_status_display()
    
    def get_created_by_name(self, obj):
        if not obj.created_by:
            return None
        return f"{obj.created_by.first_name} {obj.created_by.last_name}"
    
    def get_approved_by_name(self, obj):
        if not obj.approved_by:
            return None
        return f"{obj.approved_by.first_name} {obj.approved_by.last_name}"
    
    def get_bonuses_count(self, obj):
        return Bonus.objects.filter(
            bonus_type=obj.bonus_type,
            payment_date=obj.payment_date,
            reference_period_start=obj.reference_period_start,
            reference_period_end=obj.reference_period_end
        ).count()
