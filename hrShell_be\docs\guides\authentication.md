# Authentication and Authorization

This guide explains how authentication and authorization work in the HR Management API.

## Authentication

The HR Management API uses JWT (JSON Web Token) authentication. JWT is a compact, URL-safe means of representing claims to be transferred between two parties.

### How JWT Authentication Works

1. The client sends a request to the server with their credentials (username and password).
2. The server verifies the credentials and, if valid, generates a JWT token.
3. The server sends the token back to the client.
4. The client includes the token in the Authorization header of subsequent requests.
5. The server verifies the token and, if valid, processes the request.

### Token Types

The HR Management API uses two types of tokens:

- **Access Token**: Used to authenticate requests. Has a short lifespan (typically 1 hour).
- **Refresh Token**: Used to obtain a new access token when the current one expires. Has a longer lifespan (typically 1 day).

### Obtaining Tokens

To obtain a token, send a POST request to the `/api/token/` endpoint with your credentials:

```http
POST /api/token/
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

Response:

```json
{
  "access": "your_access_token",
  "refresh": "your_refresh_token"
}
```

### Using Tokens

Include the access token in the Authorization header of your requests:

```http
GET /api/v1/employees/
Authorization: Bearer your_access_token
```

### Refreshing Tokens

When your access token expires, you can obtain a new one by sending a POST request to the `/api/token/refresh/` endpoint with your refresh token:

```http
POST /api/token/refresh/
Content-Type: application/json

{
  "refresh": "your_refresh_token"
}
```

Response:

```json
{
  "access": "new_access_token"
}
```

### Verifying Tokens

You can verify that a token is valid by sending a POST request to the `/api/token/verify/` endpoint:

```http
POST /api/token/verify/
Content-Type: application/json

{
  "token": "your_token"
}
```

If the token is valid, the server will respond with a 200 OK status code. If the token is invalid, the server will respond with a 401 Unauthorized status code.

## Authorization

The HR Management API uses a permission-based authorization system. Each endpoint has a set of permissions that determine who can access it.

### Permission Classes

The API uses the following permission classes:

- **IsAuthenticated**: Only authenticated users can access the endpoint.
- **IsAdminOrReadOnly**: Only admin users can modify data, but anyone can read it.
- **IsOwnerOrAdmin**: Only the owner of a resource or admin users can modify it.

### Default Permissions

By default, most endpoints require authentication:

```python
REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
}
```

### Custom Permissions

Some endpoints have custom permissions:

```python
class EmployeeViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated, IsAdminOrReadOnly]
    # ...
```

### User Roles

The API recognizes the following user roles:

- **Anonymous**: Unauthenticated users.
- **Authenticated**: Users who have authenticated with the API.
- **Staff**: Users with the `is_staff` flag set to `True`.
- **Superuser**: Users with the `is_superuser` flag set to `True`.

### Role-Based Access Control

The API implements role-based access control as follows:

- **Anonymous users**: Can only access public endpoints (if any).
- **Authenticated users**: Can read data from most endpoints.
- **Staff users**: Can create, update, and delete data.
- **Superusers**: Have full access to all endpoints.

## Examples

### Accessing a Protected Endpoint

```http
GET /api/v1/employees/
Authorization: Bearer your_access_token
```

### Creating a Resource (Staff Only)

```http
POST /api/v1/employees/
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "position": "Software Engineer",
  "department": 1
}
```

### Updating a Resource (Staff or Owner Only)

```http
PATCH /api/v1/employees/1/
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "position": "Senior Software Engineer"
}
```

## Next Steps

- [Learn about working with employees](working-with-employees.md)
- [Learn about working with departments](working-with-departments.md)
- [Learn about filtering and searching](filtering-searching.md)
