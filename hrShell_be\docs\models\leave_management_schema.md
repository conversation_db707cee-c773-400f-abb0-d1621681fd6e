# Leave Management Module Schema

This document provides a visual representation of the leave management module's database schema.

## Database Schema Diagram

```mermaid
erDiagram
    %% Define entities with their fields
    
    "LeaveType" {
        bigint id PK
        varchar name
        varchar code
        text description
        bigint organization_id FK
        boolean is_paid
        int max_days_per_year
        int min_days_per_request
        int max_days_per_request
        boolean carry_forward_allowed
        int max_carry_forward_days
        boolean encashment_allowed
        int max_encashment_days
        boolean applicable_during_probation
        int probation_period_percentage
        boolean requires_documentation
        text documentation_instructions
        varchar status
        timestamp created_at
        timestamp updated_at
    }
    
    "LeavePolicy" {
        bigint id PK
        varchar name
        text description
        bigint organization_id FK
        bigint leave_type_id FK
        bigint department_id FK
        bigint location_id FK
        bigint designation_id FK
        bigint business_unit_id FK
        varchar employee_type
        varchar accrual_method
        varchar accrual_frequency
        int max_accrual
        int carry_forward_limit
        int carry_forward_expiry_months
        int encashment_limit
        int probation_period_days
        boolean apply_sandwich_rule
        boolean requires_approval
        int auto_approve_after_days
        int min_days_before_application
        int max_days_before_application
        timestamp created_at
        timestamp updated_at
    }
    
    "LeaveBalance" {
        bigint id PK
        bigint employee_id FK
        bigint leave_type_id FK
        int year
        decimal opening_balance
        decimal accrued
        decimal used
        decimal adjusted
        decimal encashed
        timestamp created_at
        timestamp updated_at
    }
    
    "LeaveRequest" {
        bigint id PK
        bigint employee_id FK
        bigint leave_type_id FK
        date start_date
        date end_date
        decimal total_days
        boolean half_day
        boolean first_half
        text reason
        file attachment
        varchar status
        bigint approved_by_id FK
        timestamp approval_date
        text rejection_reason
        timestamp created_at
        timestamp updated_at
    }
    
    "LeaveApproval" {
        bigint id PK
        bigint leave_request_id FK
        bigint approver_id FK
        int level
        varchar status
        text comments
        timestamp approval_date
        timestamp created_at
        timestamp updated_at
    }
    
    "Holiday" {
        bigint id PK
        varchar name
        date date
        text description
        bigint organization_id FK
        bigint location_id FK
        varchar holiday_type
        boolean is_recurring
        boolean is_half_day
        timestamp created_at
        timestamp updated_at
    }
    
    "WeekOff" {
        bigint id PK
        bigint organization_id FK
        bigint location_id FK
        bigint department_id FK
        int day_of_week
        boolean is_half_day
        boolean first_half
        timestamp created_at
        timestamp updated_at
    }
    
    %% Define relationships
    "LeaveType" ||--o{ "LeavePolicy" : "has"
    "LeaveType" ||--o{ "LeaveBalance" : "has"
    "LeaveType" ||--o{ "LeaveRequest" : "has"
    
    "LeavePolicy" }o--|| "Organization" : "belongs to"
    "LeavePolicy" }o--|| "Department" : "applies to"
    "LeavePolicy" }o--|| "Location" : "applies to"
    "LeavePolicy" }o--|| "Designation" : "applies to"
    "LeavePolicy" }o--|| "BusinessUnit" : "applies to"
    
    "LeaveBalance" }o--|| "Employee" : "belongs to"
    
    "LeaveRequest" }o--|| "Employee" : "belongs to"
    "LeaveRequest" }o--|| "Employee" : "approved by"
    "LeaveRequest" ||--o{ "LeaveApproval" : "has"
    
    "LeaveApproval" }o--|| "Employee" : "approved by"
    
    "Holiday" }o--|| "Organization" : "belongs to"
    "Holiday" }o--|| "Location" : "applies to"
    
    "WeekOff" }o--|| "Organization" : "belongs to"
    "WeekOff" }o--|| "Location" : "applies to"
    "WeekOff" }o--|| "Department" : "applies to"
```

## Table Descriptions

### Leave Management Tables

1. **LeaveType**: Stores different types of leave (e.g., Casual, Sick, Annual, etc.)
2. **LeavePolicy**: Stores leave policies with rules for different departments, locations, etc.
3. **LeaveBalance**: Stores employee leave balances for each leave type and year
4. **LeaveRequest**: Stores leave applications submitted by employees
5. **LeaveApproval**: Stores multi-level approval records for leave requests
6. **Holiday**: Stores holiday information for different locations
7. **WeekOff**: Stores weekly off days for different departments and locations

## Key Relationships

- A leave type can have multiple leave policies, balances, and requests
- Leave policies can be associated with specific departments, locations, designations, or business units
- Leave balances are specific to an employee, leave type, and year
- Leave requests are submitted by employees and can be approved by managers
- Leave requests can have multiple approval records for multi-level approval
- Holidays and week offs can be organization-wide or specific to locations/departments

This schema provides a comprehensive structure for managing leave types, policies, balances, requests, approvals, holidays, and week offs.
