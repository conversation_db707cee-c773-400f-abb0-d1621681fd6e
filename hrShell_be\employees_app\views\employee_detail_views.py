from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Q
from django_filters.rest_framework import DjangoFilterBackend
from employees_app.models.employee_detail import EmployeeDetail, MaritalStatus
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from employees_app.serializers.employee_detail_serializer import EmployeeDetailSerializer
from employees_app.filters import EmployeeDetailFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class EmployeeDetailViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Employee Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing EmployeeDetail instances.

    Additional actions:
    - employee_detail: Get details for a specific employee
    - department_details: Get details for employees in a specific department
    - marital_status_statistics: Get statistics about marital status
    - blood_group_statistics: Get statistics about blood groups
    """
    queryset = EmployeeDetail.objects.all()
    serializer_class = EmployeeDetailSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = EmployeeDetailFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'address', 'technical_skills', 'qualifications']
    ordering_fields = ['employee__first_name', 'employee__last_name', 'created_at', 'updated_at']

    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_detail(self, request, employee_id=None):
        """
        Get details for a specific employee
        """
        try:
            employee = Employee.objects.get(pk=employee_id)
        except Employee.DoesNotExist:
            return Response(
                {"detail": "Employee not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            employee_detail = self.queryset.get(employee=employee)
            serializer = self.get_serializer(employee_detail)
            return Response(serializer.data)
        except EmployeeDetail.DoesNotExist:
            return Response(
                {"detail": "Employee details not found"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_details(self, request, department_id=None):
        """
        Get details for employees in a specific department
        """
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return Response(
                {"detail": "Department not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all employees in the department
        employees = Employee.objects.filter(department=department)

        # Get details for these employees
        employee_details = self.queryset.filter(employee__in=employees)
        page = self.paginate_queryset(employee_details)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(employee_details, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def marital_status_statistics(self, request):
        """
        Get statistics about marital status
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Calculate statistics
        marital_status_counts = queryset.values('marital_status').annotate(count=Count('id'))

        # Convert to a more readable format
        stats = {
            'total_employees': queryset.count(),
            'marital_status': []
        }

        for status_count in marital_status_counts:
            status_value = status_count['marital_status']
            status_display = dict(MaritalStatus.choices()).get(status_value, status_value)
            stats['marital_status'].append({
                'status': status_value,
                'status_display': status_display,
                'count': status_count['count'],
                'percentage': (status_count['count'] / stats['total_employees'] * 100) if stats['total_employees'] > 0 else 0
            })

        return Response(stats)

    @action(detail=False, methods=['get'])
    def blood_group_statistics(self, request):
        """
        Get statistics about blood groups
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Calculate statistics
        blood_group_counts = queryset.values('blood_group').annotate(count=Count('id'))

        # Convert to a more readable format
        stats = {
            'total_employees': queryset.count(),
            'blood_groups': []
        }

        for group_count in blood_group_counts:
            blood_group = group_count['blood_group']
            if blood_group:  # Skip None values
                stats['blood_groups'].append({
                    'blood_group': blood_group,
                    'count': group_count['count'],
                    'percentage': (group_count['count'] / stats['total_employees'] * 100) if stats['total_employees'] > 0 else 0
                })

        return Response(stats)
