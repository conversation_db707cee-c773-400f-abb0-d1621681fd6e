# Leave Management API Reference

This document provides a comprehensive reference for the Leave Management API endpoints.

## Base URL

All API endpoints are relative to the base URL: `/api/v1/`

## Authentication

All API endpoints require authentication using JWT (JSON Web Token). Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

## Leave Types API

### List Leave Types

```
GET /leave-types/
```

**Query Parameters:**
- `name`: Filter by name (case-insensitive)
- `code`: Filter by code (case-insensitive)
- `is_paid`: Filter by paid status (true/false)
- `organization`: Filter by organization ID
- `status`: Filter by status (active/inactive)

**Response:**
```json
{
  "count": 3,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Casual Leave",
      "code": "CL",
      "description": "Leave for personal matters",
      "organization": 1,
      "organization_name": "Acme Corp",
      "is_paid": true,
      "max_days_per_year": 12,
      "min_days_per_request": 1,
      "max_days_per_request": 3,
      "carry_forward_allowed": false,
      "max_carry_forward_days": 0,
      "encashment_allowed": false,
      "max_encashment_days": 0,
      "applicable_during_probation": true,
      "probation_period_percentage": 50,
      "requires_documentation": false,
      "documentation_instructions": null,
      "status": "active",
      "status_display": "Active",
      "created_at": "2023-05-10T12:00:00Z",
      "updated_at": "2023-05-10T12:00:00Z"
    },
    // More leave types...
  ]
}
```

### Create Leave Type

```
POST /leave-types/
```

**Request Body:**
```json
{
  "name": "Casual Leave",
  "code": "CL",
  "description": "Leave for personal matters",
  "organization": 1,
  "is_paid": true,
  "max_days_per_year": 12,
  "min_days_per_request": 1,
  "max_days_per_request": 3,
  "carry_forward_allowed": false,
  "max_carry_forward_days": 0,
  "encashment_allowed": false,
  "max_encashment_days": 0,
  "applicable_during_probation": true,
  "probation_period_percentage": 50,
  "requires_documentation": false,
  "documentation_instructions": null,
  "status": "active"
}
```

### Get Leave Type

```
GET /leave-types/{id}/
```

### Update Leave Type

```
PUT /leave-types/{id}/
```

### Partial Update Leave Type

```
PATCH /leave-types/{id}/
```

### Delete Leave Type

```
DELETE /leave-types/{id}/
```

### List Active Leave Types

```
GET /leave-types/active-leave-types/
```

### List Inactive Leave Types

```
GET /leave-types/inactive-leave-types/
```

### List Paid Leave Types

```
GET /leave-types/paid-leave-types/
```

### List Unpaid Leave Types

```
GET /leave-types/unpaid-leave-types/
```

## Leave Policies API

### List Leave Policies

```
GET /leave-policies/
```

**Query Parameters:**
- `name`: Filter by name (case-insensitive)
- `organization`: Filter by organization ID
- `leave_type`: Filter by leave type ID
- `department`: Filter by department ID
- `location`: Filter by location ID
- `designation`: Filter by designation ID
- `business_unit`: Filter by business unit ID
- `employee_type`: Filter by employee type

### Create Leave Policy

```
POST /leave-policies/
```

**Request Body:**
```json
{
  "name": "Engineering Casual Leave Policy",
  "description": "Casual leave policy for engineering department",
  "organization": 1,
  "leave_type": 1,
  "department": 2,
  "accrual_method": "monthly",
  "accrual_frequency": "prorated",
  "max_accrual": 12,
  "carry_forward_limit": 6,
  "carry_forward_expiry_months": 3,
  "encashment_limit": 0,
  "probation_period_days": 90,
  "apply_sandwich_rule": true,
  "requires_approval": true,
  "auto_approve_after_days": 0,
  "min_days_before_application": 1,
  "max_days_before_application": 30
}
```

### Get Leave Policy

```
GET /leave-policies/{id}/
```

### Update Leave Policy

```
PUT /leave-policies/{id}/
```

### Partial Update Leave Policy

```
PATCH /leave-policies/{id}/
```

### Delete Leave Policy

```
DELETE /leave-policies/{id}/
```

### List Organization Policies

```
GET /leave-policies/organization/{organization_id}/
```

### List Department Policies

```
GET /leave-policies/department/{department_id}/
```

### List Location Policies

```
GET /leave-policies/location/{location_id}/
```

### List Designation Policies

```
GET /leave-policies/designation/{designation_id}/
```

### List Business Unit Policies

```
GET /leave-policies/business-unit/{business_unit_id}/
```

### List Employee Type Policies

```
GET /leave-policies/employee-type/{employee_type}/
```

## Leave Balances API

### List Leave Balances

```
GET /leave-balances/
```

**Query Parameters:**
- `employee`: Filter by employee ID
- `leave_type`: Filter by leave type ID
- `year`: Filter by year

### Create Leave Balance

```
POST /leave-balances/
```

**Request Body:**
```json
{
  "employee": 1,
  "leave_type": 1,
  "year": 2023,
  "opening_balance": 12.0,
  "accrued": 0.0,
  "used": 0.0,
  "adjusted": 0.0,
  "encashed": 0.0
}
```

### Get Leave Balance

```
GET /leave-balances/{id}/
```

### Update Leave Balance

```
PUT /leave-balances/{id}/
```

### Partial Update Leave Balance

```
PATCH /leave-balances/{id}/
```

### Delete Leave Balance

```
DELETE /leave-balances/{id}/
```

### List Employee Balances

```
GET /leave-balances/employee/{employee_id}/
```

### List Employee Year Balances

```
GET /leave-balances/employee/{employee_id}/year/{year}/
```

### List Current Year Balances

```
GET /leave-balances/current-year-balances/
```

### Adjust Balance

```
POST /leave-balances/{id}/adjust-balance/
```

**Request Body:**
```json
{
  "adjustment": 2.0,
  "reason": "Additional leave granted for exceptional performance"
}
```

## Leave Requests API

### List Leave Requests

```
GET /leave-requests/
```

**Query Parameters:**
- `employee`: Filter by employee ID
- `leave_type`: Filter by leave type ID
- `start_date`: Filter by exact start date
- `start_date_gte`: Filter by start date greater than or equal to
- `start_date_lte`: Filter by start date less than or equal to
- `end_date`: Filter by exact end date
- `end_date_gte`: Filter by end date greater than or equal to
- `end_date_lte`: Filter by end date less than or equal to
- `status`: Filter by status (pending/approved/rejected/cancelled)
- `approved_by`: Filter by approver ID
- `half_day`: Filter by half day status (true/false)

### Create Leave Request

```
POST /leave-requests/
```

**Request Body:**
```json
{
  "employee": 1,
  "leave_type": 1,
  "start_date": "2023-05-15",
  "end_date": "2023-05-17",
  "half_day": false,
  "first_half": true,
  "reason": "Personal matters",
  "attachment": null
}
```

### Get Leave Request

```
GET /leave-requests/{id}/
```

### Update Leave Request

```
PUT /leave-requests/{id}/
```

### Partial Update Leave Request

```
PATCH /leave-requests/{id}/
```

### Delete Leave Request

```
DELETE /leave-requests/{id}/
```

### List Employee Requests

```
GET /leave-requests/employee/{employee_id}/
```

### List Pending Requests

```
GET /leave-requests/pending-requests/
```

### List Approved Requests

```
GET /leave-requests/approved-requests/
```

### List Rejected Requests

```
GET /leave-requests/rejected-requests/
```

### List Cancelled Requests

```
GET /leave-requests/cancelled-requests/
```

### Approve Request

```
POST /leave-requests/{id}/approve-request/
```

### Reject Request

```
POST /leave-requests/{id}/reject-request/
```

**Request Body:**
```json
{
  "rejection_reason": "Insufficient team coverage during this period"
}
```

### Cancel Request

```
POST /leave-requests/{id}/cancel-request/
```

### List Date Range Requests

```
GET /leave-requests/date-range-requests/?start_date=2023-05-01&end_date=2023-05-31
```

## For more detailed information on other endpoints (Leave Approvals, Holidays, Week Offs), please refer to the individual model documentation:

- [LeaveType](../models/leave_type.md)
- [LeavePolicy](../models/leave_policy.md)
- [LeaveBalance](../models/leave_balance.md)
- [LeaveRequest](../models/leave_request.md)
- [LeaveApproval](../models/leave_approval.md)
- [Holiday](../models/holiday.md)
- [WeekOff](../models/week_off.md)
