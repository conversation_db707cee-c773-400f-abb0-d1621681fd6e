from django.db import models
from enum import Enum


class ApprovalStatus(Enum):
    PENDING = 'pending'
    APPROVED = 'approved'
    REJECTED = 'rejected'
    SKIPPED = 'skipped'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class LeaveApproval(models.Model):
    """
    Leave Approval model for tracking multi-level approvals
    """
    # Associations
    leave_request = models.ForeignKey(
        'leave_app.LeaveRequest',
        on_delete=models.CASCADE,
        related_name='approvals'
    )
    approver = models.ForeignKey(
        'employees_app.Employee',
        on_delete=models.CASCADE,
        related_name='leave_approvals'
    )
    
    # Approval details
    level = models.PositiveIntegerField(
        default=1,
        help_text="Approval level (1, 2, 3, etc.)"
    )
    status = models.CharField(
        max_length=10,
        choices=ApprovalStatus.choices(),
        default=ApprovalStatus.PENDING.value
    )
    comments = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    approval_date = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['leave_request', 'level']
        verbose_name = "Leave Approval"
        verbose_name_plural = "Leave Approvals"
        unique_together = ('leave_request', 'approver', 'level')
    
    def __str__(self):
        return f"{self.leave_request} - Level {self.level} - {self.get_status_display()}"
