from django.db import models
from enum import Enum


class DocumentType(Enum):
    POLICY = 'policy'
    PROCEDURE = 'procedure'
    CERTIFICATE = 'certificate'
    LEGAL = 'legal'
    REGISTRATION = 'registration'
    COMPLIANCE = 'compliance'
    OTHER = 'other'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class OrganizationDocument(models.Model):
    """
    OrganizationDocument model for storing company documents
    """
    # Basic information
    title = models.CharField(max_length=200)
    organization = models.ForeignKey(
        'organization_app.Organization',
        on_delete=models.CASCADE,
        related_name='documents'
    )
    
    # Document details
    description = models.TextField(blank=True, null=True)
    document_type = models.CharField(
        max_length=20,
        choices=DocumentType.choices(),
        default=DocumentType.OTHER.value
    )
    
    # File
    file = models.FileField(upload_to='organization_documents/')
    
    # Metadata
    version = models.CharField(max_length=50, blank=True, null=True)
    effective_date = models.DateField(null=True, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    
    # Associations
    business_unit = models.ForeignKey(
        'organization_app.BusinessUnit',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='documents'
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = "Organization Document"
        verbose_name_plural = "Organization Documents"
    
    def __str__(self):
        return f"{self.title} ({self.get_document_type_display()})"
