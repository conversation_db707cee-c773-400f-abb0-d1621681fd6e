# Recruitment / ATS Module

The Recruitment or ATS (Applicant Tracking System) module helps HR teams manage the complete talent acquisition lifecycle, from job requisition to onboarding. It centralizes job openings, candidate data, resumes, interviews, and hiring outcomes.

## Core Workflow Stages

### 1. Job Requisition & Approval
- Managers or HR create job requisitions
- Department heads or leadership approve the requisition

### 2. Job Posting
- Approved jobs are posted to:
  - Career pages
  - Job boards (e.g., LinkedIn, Indeed)
  - Internal job portals (for internal hiring)

### 3. Candidate Application & Intake
- Candidates apply via the portal or referrals
- ATS captures resumes, parses details, and stores applications

### 4. Resume Screening & Shortlisting
- HR or AI engine screens applications based on keywords, skills, and job match
- Shortlisted candidates move to the interview stage

### 5. Interview Scheduling
- HR schedules interviews with selected interviewers
- Interview feedback is recorded in the system

### 6. Evaluation & Selection
- Candidate scores and panel recommendations are reviewed
- Final decision: hire/reject

### 7. Offer Management
- HR generates and sends offer letters
- Tracks acceptance/rejection

### 8. Onboarding Initiation
- Successful candidates are onboarded into the HRMS system
- Onboarding checklist is triggered

## Key Users and Roles

| Role | Responsibilities |
|------|-----------------|
| Recruiter/HR | Manages job posts, resumes, scheduling, and offers |
| Hiring Manager | Creates job requisitions, reviews candidates, gives feedback |
| Interviewer | Conducts interviews and submits evaluations |
| Candidate | Applies to jobs and responds to communication |
| Department Head | Approves requisitions and hiring decisions |

## Core Features

### Job Requisition Management
- Create, approve, and track job openings
- Define job details, requirements, and qualifications
- Track requisition status and approvals

### Resume Upload & Parsing
- Upload and store candidate resumes
- Parse resume data (skills, education, experience)
- Match candidates to job requirements

### Candidate Profile Database
- Maintain a central database of all candidates
- Track candidate skills, experience, and qualifications
- Reuse candidate profiles for future roles

### Application Tracking
- Track application status from applied to hired/rejected
- Record all interactions with candidates
- Maintain a complete audit trail of the recruitment process

### Interview Workflow
- Schedule and manage interviews
- Collect structured feedback from interviewers
- Track interview outcomes and decisions

### Offer Management
- Generate and send offer letters
- Track offer status (sent, accepted, rejected)
- Manage offer negotiations and revisions

### Onboarding Integration
- Initiate onboarding process for hired candidates
- Track onboarding tasks and progress
- Ensure a smooth transition from candidate to employee

## Models

### Job Requisition
- **JobRequisition**: Represents a job requisition created by managers or HR

### Job Posting
- **JobPosting**: Represents a job posting published to various channels
- **RecruitmentSource**: Represents recruitment sources like job boards, career pages, etc.

### Candidate
- **Candidate**: Represents job candidates
- **CandidateSkill**: Represents skills possessed by candidates with proficiency level
- **CandidateEducation**: Represents candidate's educational background
- **CandidateExperience**: Represents candidate's work experience
- **Skill**: Represents skills required for jobs and possessed by candidates

### Application
- **Application**: Represents job applications submitted by candidates
- **ApplicationStageHistory**: Tracks the history of application stage changes

### Interview
- **Interview**: Represents interviews scheduled for candidates
- **InterviewFeedback**: Represents feedback provided by interviewers
- **InterviewQuestion**: Represents questions asked during interviews

### Offer
- **Offer**: Represents job offers made to candidates

### Onboarding
- **Onboarding**: Represents the onboarding process for new hires
- **OnboardingTask**: Represents tasks to be completed during onboarding

## API Endpoints

### Job Requisition
- `GET /api/v1/job-requisitions/`: List all job requisitions
- `POST /api/v1/job-requisitions/`: Create a new job requisition
- `GET /api/v1/job-requisitions/{id}/`: Retrieve a specific job requisition
- `PUT /api/v1/job-requisitions/{id}/`: Update a specific job requisition
- `DELETE /api/v1/job-requisitions/{id}/`: Delete a specific job requisition
- `POST /api/v1/job-requisitions/{id}/submit-for-approval/`: Submit a job requisition for approval
- `POST /api/v1/job-requisitions/{id}/approve/`: Approve a job requisition
- `POST /api/v1/job-requisitions/{id}/reject/`: Reject a job requisition

### Job Posting
- `GET /api/v1/job-postings/`: List all job postings
- `POST /api/v1/job-postings/`: Create a new job posting
- `GET /api/v1/job-postings/{id}/`: Retrieve a specific job posting
- `PUT /api/v1/job-postings/{id}/`: Update a specific job posting
- `DELETE /api/v1/job-postings/{id}/`: Delete a specific job posting
- `POST /api/v1/job-postings/{id}/publish/`: Publish a job posting
- `POST /api/v1/job-postings/{id}/expire/`: Expire a job posting
- `POST /api/v1/job-postings/{id}/close/`: Close a job posting

### Candidate
- `GET /api/v1/candidates/`: List all candidates
- `POST /api/v1/candidates/`: Create a new candidate
- `GET /api/v1/candidates/{id}/`: Retrieve a specific candidate
- `PUT /api/v1/candidates/{id}/`: Update a specific candidate
- `DELETE /api/v1/candidates/{id}/`: Delete a specific candidate
- `GET /api/v1/candidates/{id}/skills/`: Get all skills for a specific candidate
- `POST /api/v1/candidates/{id}/add-skill/`: Add a skill to a candidate
- `GET /api/v1/candidates/{id}/education/`: Get all education records for a specific candidate
- `POST /api/v1/candidates/{id}/add-education/`: Add an education record to a candidate
- `GET /api/v1/candidates/{id}/experience/`: Get all experience records for a specific candidate
- `POST /api/v1/candidates/{id}/add-experience/`: Add an experience record to a candidate

### Application
- `GET /api/v1/applications/`: List all applications
- `POST /api/v1/applications/`: Create a new application
- `GET /api/v1/applications/{id}/`: Retrieve a specific application
- `PUT /api/v1/applications/{id}/`: Update a specific application
- `DELETE /api/v1/applications/{id}/`: Delete a specific application
- `GET /api/v1/applications/{id}/stage-history/`: Get the stage history for a specific application
- `POST /api/v1/applications/{id}/change-stage/`: Change the stage of an application
- `POST /api/v1/applications/{id}/shortlist/`: Shortlist an application
- `POST /api/v1/applications/{id}/reject/`: Reject an application
- `POST /api/v1/applications/{id}/withdraw/`: Withdraw an application

### Interview
- `GET /api/v1/interviews/`: List all interviews
- `POST /api/v1/interviews/`: Create a new interview
- `GET /api/v1/interviews/{id}/`: Retrieve a specific interview
- `PUT /api/v1/interviews/{id}/`: Update a specific interview
- `DELETE /api/v1/interviews/{id}/`: Delete a specific interview
- `GET /api/v1/interviews/{id}/feedback/`: Get all feedback for a specific interview
- `GET /api/v1/interviews/{id}/questions/`: Get all questions for a specific interview
- `POST /api/v1/interviews/{id}/add-question/`: Add a question to an interview
- `POST /api/v1/interviews/{id}/complete/`: Mark an interview as completed
- `POST /api/v1/interviews/{id}/cancel/`: Cancel an interview
- `POST /api/v1/interviews/{id}/reschedule/`: Reschedule an interview
- `POST /api/v1/interviews/{id}/no-show/`: Mark an interview as no show

### Offer
- `GET /api/v1/offers/`: List all offers
- `POST /api/v1/offers/`: Create a new offer
- `GET /api/v1/offers/{id}/`: Retrieve a specific offer
- `PUT /api/v1/offers/{id}/`: Update a specific offer
- `DELETE /api/v1/offers/{id}/`: Delete a specific offer
- `POST /api/v1/offers/{id}/submit-for-approval/`: Submit an offer for approval
- `POST /api/v1/offers/{id}/approve/`: Approve an offer
- `POST /api/v1/offers/{id}/send/`: Send an offer to the candidate
- `POST /api/v1/offers/{id}/accept/`: Mark an offer as accepted by the candidate
- `POST /api/v1/offers/{id}/negotiate/`: Mark an offer as being negotiated by the candidate
- `POST /api/v1/offers/{id}/decline/`: Mark an offer as declined by the candidate
- `POST /api/v1/offers/{id}/withdraw/`: Withdraw an offer

### Onboarding
- `GET /api/v1/onboarding/`: List all onboarding records
- `POST /api/v1/onboarding/`: Create a new onboarding record
- `GET /api/v1/onboarding/{id}/`: Retrieve a specific onboarding record
- `PUT /api/v1/onboarding/{id}/`: Update a specific onboarding record
- `DELETE /api/v1/onboarding/{id}/`: Delete a specific onboarding record
- `GET /api/v1/onboarding/{id}/tasks/`: Get all tasks for a specific onboarding record
- `POST /api/v1/onboarding/{id}/add-task/`: Add a task to an onboarding record
- `POST /api/v1/onboarding/{id}/start/`: Start the onboarding process
- `POST /api/v1/onboarding/{id}/complete/`: Complete the onboarding process
- `POST /api/v1/onboarding/{id}/cancel/`: Cancel the onboarding process

## Key Metrics Tracked

- Time to Fill
- Cost per Hire
- Offer Acceptance Rate
- Interview-to-Offer Ratio
- Drop-off/Withdrawal Rate
- Source of Hire (e.g., referral, job board)
- Recruiter Efficiency
- Hiring Manager Satisfaction
- Candidate Experience Score

## Integration with Other Modules

The Recruitment/ATS Module integrates with:

1. **Employee Module**: For employee information and creating new employee records
2. **Organization Module**: For organization, department, and location information
3. **Leave Module**: For leave information during onboarding
4. **Payroll Module**: For salary information during offer creation
