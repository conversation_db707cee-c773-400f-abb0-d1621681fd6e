# JWT Authentication Guide

This guide explains how to use J<PERSON><PERSON> (JSON Web Token) authentication in the HR Management API.

## Overview

The HR Management API uses JWT for authentication. JWT is a compact, URL-safe means of representing claims to be transferred between two parties. The claims in a JWT are encoded as a JSON object that is digitally signed using JSON Web Signature (JWS).

## How JWT Works

1. The client sends a request to the server with their credentials (username and password).
2. The server verifies the credentials and, if valid, generates a JWT token.
3. The server sends the token back to the client.
4. The client includes the token in the Authorization header of subsequent requests.
5. The server verifies the token and, if valid, processes the request.

## Token Types

The HR Management API uses two types of tokens:

- **Access Token**: Used to authenticate requests. Has a short lifespan (1 hour by default).
- **Refresh Token**: Used to obtain a new access token when the current one expires. Has a longer lifespan (1 day by default).

## Obtaining Tokens

To obtain a token, send a POST request to the `/api/token/` endpoint with your credentials:

```http
POST /api/token/
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

### Response

```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user_id": 1,
  "username": "your_username",
  "email": "<EMAIL>",
  "is_staff": true,
  "is_superuser": false
}
```

## Using Tokens

Include the access token in the Authorization header of your requests:

```http
GET /api/v1/employees/
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Refreshing Tokens

When your access token expires, you can obtain a new one by sending a POST request to the `/api/token/refresh/` endpoint with your refresh token:

```http
POST /api/token/refresh/
Content-Type: application/json

{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### Response

```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Verifying Tokens

You can verify that a token is valid by sending a POST request to the `/api/token/verify/` endpoint:

```http
POST /api/token/verify/
Content-Type: application/json

{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

If the token is valid, the server will respond with a 200 OK status code. If the token is invalid, the server will respond with a 401 Unauthorized status code.

## Logging Out

To log out, you need to blacklist the refresh token. Send a POST request to the `/api/v1/logout/` endpoint with your refresh token:

```http
POST /api/v1/logout/
Content-Type: application/json
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### Response

```json
{
  "detail": "Successfully logged out."
}
```

## Getting User Information

You can get information about the authenticated user by sending a GET request to the `/api/v1/user-info/` endpoint:

```http
GET /api/v1/user-info/
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### Response

```json
{
  "id": 1,
  "username": "your_username",
  "email": "<EMAIL>",
  "is_staff": true,
  "is_superuser": false
}
```

## Token Customization

The HR Management API customizes the token response to include additional user information. This is done using a custom token serializer.

### Custom Claims in the Token

The token includes the following custom claims:

- `username`: The username of the user
- `email`: The email of the user
- `is_staff`: Whether the user is a staff member
- `is_superuser`: Whether the user is a superuser

### Additional Response Data

The token response includes the following additional data:

- `user_id`: The ID of the user
- `username`: The username of the user
- `email`: The email of the user
- `is_staff`: Whether the user is a staff member
- `is_superuser`: Whether the user is a superuser

## Token Settings

The HR Management API uses the following token settings:

- `ACCESS_TOKEN_LIFETIME`: 1 hour
- `REFRESH_TOKEN_LIFETIME`: 1 day
- `ROTATE_REFRESH_TOKENS`: False
- `BLACKLIST_AFTER_ROTATION`: True
- `ALGORITHM`: HS256
- `AUTH_HEADER_TYPES`: Bearer

You can customize these settings in the `settings.py` file.
