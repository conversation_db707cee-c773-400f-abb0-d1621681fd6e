#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix inconsistent Swagger tags across the HR Shell application.

This script updates all existing tags to match the organized structure.
"""

import os
import re
from pathlib import Path

# Tag mapping to fix inconsistencies
TAG_FIXES = {
    'Employees': 'Employee Management',
    'Employee': 'Employee Management',
    'Departments': 'Employee Management',
    'Department': 'Employee Management',
    'Organization': 'Organization Structure',
    'Organizations': 'Organization Structure',
    'Leave': 'Leave Management',
    'Leaves': 'Leave Management',
    'Payroll': 'Payroll Management',
    'Payrolls': 'Payroll Management',
    'Recruitment': 'Recruitment & Hiring',
    'Recruitments': 'Recruitment & Hiring',
    'Onboarding': 'Onboarding & Offboarding',
    'Onboardings': 'Onboarding & Offboarding',
    'Offboarding': 'Onboarding & Offboarding',
    'Offboardings': 'Onboarding & Offboarding',
    'AI': 'AI Assistant',
    'Health': 'System',
    'API': 'System',
    'System': 'System'
}

def find_python_files(directory):
    """Find all Python files in the project, excluding virtual environment."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Skip virtual environment, cache directories, and other non-project directories
        if any(skip_dir in root for skip_dir in ['.venv', '__pycache__', '.git', 'site-packages', 'Scripts', 'Lib']):
            continue
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    return python_files

def fix_tags_in_file(file_path):
    """Fix tags in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Fix tags in swagger_auto_schema decorators
        for old_tag, new_tag in TAG_FIXES.items():
            # Pattern to match tags=['old_tag'] or tags=["old_tag"]
            pattern = rf"tags=\[(['\"]){old_tag}\1\]"
            replacement = f"tags=['{new_tag}']"
            content = re.sub(pattern, replacement, content)

        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True

        return False

    except Exception as e:
        print(f"  ❌ Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix all tags."""
    print("🔧 Starting Swagger tag fixes...")

    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent

    # Find all Python files in our project only
    python_files = find_python_files(project_root)

    # Filter to only our app files
    project_files = [f for f in python_files if any(app in f for app in [
        'employees_app', 'organization_app', 'leave_app', 'payroll_app',
        'recruitment_app', 'onboarding_offboarding_app', 'common'
    ]) and 'views' in f]

    print(f"📁 Found {len(project_files)} Python files to check")

    fixed_count = 0

    # Process each file
    for file_path in project_files:
        if fix_tags_in_file(file_path):
            print(f"  ✅ Fixed tags in: {os.path.relpath(file_path, project_root)}")
            fixed_count += 1

    print(f"\n✨ Tag fixes completed!")
    print(f"📊 Fixed {fixed_count} files")

    if fixed_count > 0:
        print("\n📋 Next steps:")
        print("1. Review the changes in your files")
        print("2. Test your API documentation at /swagger-ui/")
        print("3. Commit the changes to version control")

if __name__ == "__main__":
    main()
