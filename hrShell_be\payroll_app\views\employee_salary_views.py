from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from payroll_app.models.employee_salary import EmployeeSalary, EmployeeSalaryComponent, SalaryRevision
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from payroll_app.serializers.employee_salary_serializer import (
    EmployeeSalarySerializer, EmployeeSalaryComponentSerializer, SalaryRevisionSerializer
)
from payroll_app.filters.payroll_filters import EmployeeSalaryFilter


class EmployeeSalaryViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Payroll Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for employee salaries.
    """
    queryset = EmployeeSalary.objects.all()
    serializer_class = EmployeeSalarySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = EmployeeSalaryFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email']
    ordering_fields = ['employee__first_name', 'effective_from', 'basic_salary', 'gross_salary', 'created_at']
    ordering = ['-effective_from']

    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get all active employee salaries.
        """
        active = EmployeeSalary.objects.filter(is_active=True)
        page = self.paginate_queryset(active)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(active, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all salaries for a specific employee.
        """
        salaries = EmployeeSalary.objects.filter(employee_id=employee_pk)
        page = self.paginate_queryset(salaries)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(salaries, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def current(self, request, employee_pk=None):
        """
        Get the current active salary for a specific employee.
        """
        try:
            salary = EmployeeSalary.objects.filter(
                employee_id=employee_pk,
                is_active=True
            ).latest('effective_from')
            serializer = self.get_serializer(salary)
            return Response(serializer.data)
        except EmployeeSalary.DoesNotExist:
            return Response(
                {"detail": "No active salary found for this employee."},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['get'])
    def components(self, request, pk=None):
        """
        Get all components for a specific employee salary.
        """
        salary = self.get_object()
        components = salary.components.all()
        serializer = EmployeeSalaryComponentSerializer(components, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def revisions(self, request, pk=None):
        """
        Get all revisions for a specific employee salary.
        """
        salary = self.get_object()
        revisions = SalaryRevision.objects.filter(
            previous_salary=salary
        ) | SalaryRevision.objects.filter(
            new_salary=salary
        )
        serializer = SalaryRevisionSerializer(revisions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """
        Deactivate a specific employee salary.
        """
        salary = self.get_object()
        salary.is_active = False
        salary.save()
        serializer = self.get_serializer(salary)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """
        Activate a specific employee salary and deactivate all others for the same employee.
        """
        salary = self.get_object()

        # Deactivate all other salaries for this employee
        EmployeeSalary.objects.filter(
            employee=salary.employee,
            is_active=True
        ).update(is_active=False)

        # Activate this salary
        salary.is_active = True
        salary.save()

        serializer = self.get_serializer(salary)
        return Response(serializer.data)
