from django.db import models
from enum import Enum
from datetime import timedelta


class LeaveRequestStatus(Enum):
    PENDING = 'pending'
    APPROVED = 'approved'
    REJECTED = 'rejected'
    CANCELLED = 'cancelled'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class LeaveRequest(models.Model):
    """
    Leave Request model for tracking leave applications
    """
    # Associations
    employee = models.ForeignKey(
        'employees_app.Employee',
        on_delete=models.CASCADE,
        related_name='leave_requests'
    )
    leave_type = models.ForeignKey(
        'leave_app.LeaveType',
        on_delete=models.CASCADE,
        related_name='requests'
    )
    
    # Leave details
    start_date = models.DateField()
    end_date = models.DateField()
    total_days = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        editable=False
    )
    half_day = models.BooleanField(
        default=False,
        help_text="Whether this is a half-day leave"
    )
    first_half = models.BooleanField(
        default=True,
        help_text="If half_day is True, whether it's the first half or second half"
    )
    
    # Reason and documentation
    reason = models.TextField()
    attachment = models.FileField(
        upload_to='leave_attachments/',
        null=True,
        blank=True
    )
    
    # Status
    status = models.CharField(
        max_length=10,
        choices=LeaveRequestStatus.choices(),
        default=LeaveRequestStatus.PENDING.value
    )
    
    # Approval
    approved_by = models.ForeignKey(
        'employees_app.Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_leave_requests'
    )
    approval_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-start_date']
        verbose_name = "Leave Request"
        verbose_name_plural = "Leave Requests"
    
    def __str__(self):
        return f"{self.employee} - {self.leave_type.name} ({self.start_date} to {self.end_date})"
    
    def save(self, *args, **kwargs):
        # Calculate total days
        if self.half_day:
            self.total_days = 0.5
        else:
            delta = self.end_date - self.start_date
            self.total_days = delta.days + 1
        
        super().save(*args, **kwargs)
    
    def get_overlapping_requests(self):
        """
        Get all leave requests that overlap with this one
        """
        return LeaveRequest.objects.filter(
            employee=self.employee,
            status='approved',
        ).filter(
            # Leave starts within the range or before the range and ends within or after the range
            (
                (models.Q(start_date__lte=self.start_date) & models.Q(end_date__gte=self.start_date)) |
                (models.Q(start_date__lte=self.end_date) & models.Q(end_date__gte=self.end_date)) |
                (models.Q(start_date__gte=self.start_date) & models.Q(end_date__lte=self.end_date))
            )
        ).exclude(id=self.id)
