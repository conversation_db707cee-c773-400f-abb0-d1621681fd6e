# Swagger Integration Guide for HR Shell

This document provides detailed instructions for integrating new endpoints with the Swagger documentation in the HR Shell project.

## Table of Contents

1. [Overview](#overview)
2. [Adding New Endpoints](#adding-new-endpoints)
3. [Grouping Endpoints](#grouping-endpoints)
4. [Adding Sample Payloads](#adding-sample-payloads)
5. [Testing Documentation](#testing-documentation)
6. [Troubleshooting](#troubleshooting)

## Overview

The HR Shell project uses Swagger for API documentation. The Swagger documentation is generated using the `drf-yasg` package and is customized to provide a better user experience.

### Key Files

- `common/swagger.py`: Contains the Swagger schema generation logic
- `common/templates/swagger-ui.html`: Contains the Swagger UI template
- `common/urls.py`: Contains the URL patterns for the Swagger documentation

### Accessing Swagger Documentation

- Swagger UI: [http://localhost:8000/swagger-ui/](http://localhost:8000/swagger-ui/)
- ReDoc: [http://localhost:8000/redoc/](http://localhost:8000/redoc/)
- OpenAPI Schema: [http://localhost:8000/api/schema/](http://localhost:8000/api/schema/)

## Adding New Endpoints

When adding new endpoints to the project, follow these steps to include them in the Swagger documentation:

### 1. Identify the Appropriate Group

Determine which functional group the endpoint belongs to. The current groups are:

- Employee Management
- Organization Structure
- Leave Management
- Attendance Management
- Payroll Management
- Compensation & Benefits
- Recruitment & Hiring
- Onboarding & Offboarding
- Document Management
- Authentication & Security

### 2. Add the Endpoint to the Schema

Open `common/swagger.py` and locate the `get_all_paths` method. Add your endpoint to the appropriate section:

```python
# Example: Adding a new endpoint to Employee Management
self.add_crud_endpoints(paths, "/api/v1/employee-certifications/", "Employee Management", "Employee Certifications")
```

The `add_crud_endpoints` method takes the following parameters:
- `paths`: The paths dictionary to add the endpoint to
- `base_path`: The base path of the endpoint
- `tag`: The tag group to add the endpoint to
- `resource_name`: The name of the resource (used for generating sample payloads)

### 3. Add Sample Payloads

If your endpoint uses a new resource type, add sample payloads for it in the `get_sample_payload` and `get_sample_response` methods:

```python
# Example: Adding sample payloads for Employee Certifications
elif tag == "Employee Certifications":
    return {
        "employee_id": 1,
        "certification_name": "Project Management Professional (PMP)",
        "issuing_organization": "Project Management Institute",
        "issue_date": "2022-05-15",
        "expiry_date": "2025-05-15",
        "credential_id": "PMP-123456",
        "credential_url": "https://pmi.org/certifications/verify/PMP-123456"
    }
```

### 4. Add Schema Definitions

If your endpoint uses a new resource type, add schema definitions for it in the `get_request_body_schema` and `get_response_schema` methods:

```python
# Example: Adding schema definitions for Employee Certifications
elif tag == "Employee Certifications":
    schema["properties"].update({
        "employee_id": {"type": "integer", "description": "ID of the employee"},
        "certification_name": {"type": "string", "description": "Name of the certification"},
        "issuing_organization": {"type": "string", "description": "Organization that issued the certification"},
        "issue_date": {"type": "string", "format": "date", "description": "Date the certification was issued"},
        "expiry_date": {"type": "string", "format": "date", "description": "Date the certification expires"},
        "credential_id": {"type": "string", "description": "ID or number of the certification credential"},
        "credential_url": {"type": "string", "format": "uri", "description": "URL to verify the certification"}
    })
```

## Grouping Endpoints

Endpoints are grouped by functionality to make the API documentation more organized and easier to navigate.

### Adding a New Group

If your endpoint doesn't fit into any existing group, you can add a new group:

1. Add the new tag to the `get_tags` method:

```python
# Example: Adding a new tag for Training & Development
{"name": "Training & Development", "description": "Endpoints for managing employee training, certifications, and skills development"}
```

2. Add your endpoints to the new group in the `get_all_paths` method:

```python
# Training & Development
self.add_crud_endpoints(paths, "/api/v1/training-programs/", "Training & Development", "Training Programs")
self.add_crud_endpoints(paths, "/api/v1/training-sessions/", "Training & Development", "Training Sessions")
self.add_crud_endpoints(paths, "/api/v1/employee-trainings/", "Training & Development", "Employee Trainings")
```

### Reorganizing Endpoints

If you need to reorganize existing endpoints into different groups:

1. Move the endpoint declarations in the `get_all_paths` method to the appropriate section
2. Update the tag parameter to reflect the new group

## Adding Sample Payloads

Sample payloads help API users understand what data to send and what to expect in response.

### Request Payloads

Add sample request payloads in the `get_sample_payload` method:

```python
# Example: Sample payload for a Training Program
elif tag == "Training Programs":
    return {
        "title": "Advanced Python Programming",
        "description": "Learn advanced Python programming techniques",
        "duration_hours": 20,
        "max_participants": 15,
        "prerequisites": "Basic Python knowledge",
        "learning_objectives": "Master advanced Python concepts",
        "status": "active"
    }
```

### Response Payloads

Add sample response payloads in the `get_sample_response` method:

```python
# Example: Sample response for a Training Program
elif tag == "Training Programs":
    sample.update({
        "id": 1,
        "created_at": "2023-01-01T12:00:00Z",
        "updated_at": "2023-01-15T14:30:00Z",
        "title": "Advanced Python Programming",
        "description": "Learn advanced Python programming techniques",
        "duration_hours": 20,
        "max_participants": 15,
        "prerequisites": "Basic Python knowledge",
        "learning_objectives": "Master advanced Python concepts",
        "status": "active",
        "instructor": {
            "id": 5,
            "name": "Jane Smith"
        },
        "sessions_count": 3,
        "enrolled_participants": 12
    })
```

## Testing Documentation

After adding new endpoints or making changes to the Swagger documentation, test the documentation to ensure it works correctly:

1. Run the development server:
   ```bash
   python manage.py runserver
   ```

2. Open the Swagger UI in your browser:
   [http://localhost:8000/swagger-ui/](http://localhost:8000/swagger-ui/)

3. Verify that your endpoints appear in the correct group
4. Test the sample payloads to ensure they are valid
5. Check that the endpoint descriptions are clear and accurate

## Troubleshooting

### Common Issues

#### Endpoint Not Appearing in Documentation

- Check that you've added the endpoint to the `get_all_paths` method
- Verify that the URL pattern is correct
- Ensure that the view is properly configured

#### Sample Payloads Not Showing

- Check that you've added sample payloads in the `get_sample_payload` method
- Verify that the tag name matches exactly

#### Schema Generation Errors

- Check for syntax errors in the schema definition
- Verify that all required fields are included
- Ensure that the schema is valid JSON

### Getting Help

If you encounter issues with the Swagger documentation, consult the following resources:

- [drf-yasg Documentation](https://drf-yasg.readthedocs.io/)
- [OpenAPI Specification](https://swagger.io/specification/)
- [Django REST Framework Documentation](https://www.django-rest-framework.org/)

---

By following this guide, you can ensure that all new endpoints are properly documented in the Swagger documentation, making the API easier to use and understand.
