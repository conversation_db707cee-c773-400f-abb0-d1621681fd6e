from django.db import models
from django.core.validators import MinValueValidator
from organization_app.models.organization import Organization
from payroll_app.models.payroll import Payroll


class BankTransfer(models.Model):
    """
    Model to represent bank transfers for salary disbursement.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('generated', 'Generated'),
        ('sent', 'Sent to Bank'),
        ('processed', 'Processed'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    TRANSFER_TYPE_CHOICES = [
        ('salary', 'Salary'),
        ('bonus', 'Bonus'),
        ('reimbursement', 'Reimbursement'),
        ('other', 'Other'),
    ]
    
    payroll = models.ForeignKey(Payroll, on_delete=models.CASCADE, related_name='bank_transfers', null=True, blank=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='bank_transfers')
    reference_number = models.CharField(max_length=50, unique=True)
    transfer_date = models.DateField()
    bank_name = models.CharField(max_length=100)
    account_number = models.CharField(max_length=50)
    transfer_type = models.CharField(max_length=20, choices=TRANSFER_TYPE_CHOICES, default='salary')
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    total_employees = models.PositiveIntegerField(default=0)
    file_format = models.CharField(max_length=20, default='csv')
    transfer_file = models.FileField(upload_to='bank_transfers/%Y/%m/', null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-transfer_date']
        verbose_name = 'Bank Transfer'
        verbose_name_plural = 'Bank Transfers'
    
    def __str__(self):
        return f"{self.reference_number} - {self.transfer_date} ({self.total_amount})"
