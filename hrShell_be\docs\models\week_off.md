# WeekOff Model

The WeekOff model represents weekly off days for different departments and locations.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `organization` | ForeignKey | Organization this week off belongs to | Yes |
| `location` | ForeignKey | Specific location this week off applies to | No |
| `department` | ForeignKey | Specific department this week off applies to | No |
| `day_of_week` | Integer | Day of the week (0=Monday, 1=Tuesday, ..., 6=Sunday) | Yes (default: 6) |
| `is_half_day` | Boolean | Whether this is a half-day off | Yes (default: False) |
| `first_half` | Boolean | If is_half_day is True, whether it's the first half or second half | Yes (default: True) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this week off belongs to |
| `location` | Location | The specific location this week off applies to (optional) |
| `department` | Department | The specific department this week off applies to (optional) |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the day of week, location, and department |

## Example

```python
week_off = WeekOff.objects.create(
    organization=acme_corp,
    day_of_week=6,  # Sunday
    is_half_day=False
)
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/week-offs/` | GET | List all week offs |
| `/api/v1/week-offs/` | POST | Create a new week off |
| `/api/v1/week-offs/{id}/` | GET | Retrieve a specific week off |
| `/api/v1/week-offs/{id}/` | PUT | Update a specific week off |
| `/api/v1/week-offs/{id}/` | DELETE | Delete a specific week off |
| `/api/v1/week-offs/organization/{organization_id}/` | GET | List week offs for a specific organization |
| `/api/v1/week-offs/location/{location_id}/` | GET | List week offs for a specific location |
| `/api/v1/week-offs/department/{department_id}/` | GET | List week offs for a specific department |
| `/api/v1/week-offs/employee/{employee_id}/` | GET | List week offs for a specific employee |

## Notes

- If `location` is not specified, the week off applies to all locations in the organization.
- If `department` is not specified, the week off applies to all departments in the organization.
- The `employee/{employee_id}/` endpoint determines the applicable week offs based on the employee's department and location.
- Week offs can be configured differently for different departments and locations within the same organization.
