from django.contrib import admin
from payroll_app.models.salary_structure import SalaryStructure, SalaryComponent
from payroll_app.models.employee_salary import EmployeeSalary, EmployeeSalaryComponent, SalaryRevision
from payroll_app.models.payroll import Payroll, PayrollItem, PayrollItemComponent
from payroll_app.models.payslip import Payslip
from payroll_app.models.loan import Loan, LoanInstallment
from payroll_app.models.bonus import Bonus, BonusBatch
from payroll_app.models.tax import TaxSlab, EmployeeTaxDeclaration
from payroll_app.models.bank_transfer import BankTransfer


@admin.register(SalaryComponent)
class SalaryComponentAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'component_type', 'calculation_type', 'value', 'is_taxable', 'is_active')
    list_filter = ('component_type', 'calculation_type', 'is_taxable', 'is_fixed', 'is_active', 'organization')
    search_fields = ('name', 'code', 'description')


@admin.register(SalaryStructure)
class SalaryStructureAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'organization', 'salary_type', 'is_active')
    list_filter = ('salary_type', 'is_active', 'organization')
    search_fields = ('name', 'code', 'description')
    filter_horizontal = ('components',)


@admin.register(EmployeeSalary)
class EmployeeSalaryAdmin(admin.ModelAdmin):
    list_display = ('employee', 'salary_structure', 'effective_from', 'basic_salary', 'gross_salary', 'is_active')
    list_filter = ('is_active', 'salary_structure', 'effective_from')
    search_fields = ('employee__first_name', 'employee__last_name', 'employee__email')
    date_hierarchy = 'effective_from'


@admin.register(EmployeeSalaryComponent)
class EmployeeSalaryComponentAdmin(admin.ModelAdmin):
    list_display = ('employee_salary', 'salary_component', 'value', 'is_active')
    list_filter = ('is_active', 'salary_component__component_type')
    search_fields = ('employee_salary__employee__first_name', 'employee_salary__employee__last_name', 'salary_component__name')


@admin.register(SalaryRevision)
class SalaryRevisionAdmin(admin.ModelAdmin):
    list_display = ('employee', 'revision_date', 'revision_type', 'percentage_increase', 'amount_increase', 'approved_by')
    list_filter = ('revision_type', 'revision_date')
    search_fields = ('employee__first_name', 'employee__last_name', 'employee__email')
    date_hierarchy = 'revision_date'


@admin.register(Payroll)
class PayrollAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'start_date', 'end_date', 'payment_date', 'status', 'total_earnings', 'total_deductions', 'net_payable')
    list_filter = ('status', 'organization', 'payment_date')
    search_fields = ('name',)
    date_hierarchy = 'payment_date'


@admin.register(PayrollItem)
class PayrollItemAdmin(admin.ModelAdmin):
    list_display = ('payroll', 'employee', 'working_days', 'leave_days', 'lop_days', 'gross_earnings', 'total_deductions', 'net_payable', 'status')
    list_filter = ('status', 'payroll')
    search_fields = ('employee__first_name', 'employee__last_name', 'employee__email')


@admin.register(PayrollItemComponent)
class PayrollItemComponentAdmin(admin.ModelAdmin):
    list_display = ('payroll_item', 'name', 'component_type', 'amount', 'is_taxable')
    list_filter = ('component_type', 'is_taxable')
    search_fields = ('name', 'payroll_item__employee__first_name', 'payroll_item__employee__last_name')


@admin.register(Payslip)
class PayslipAdmin(admin.ModelAdmin):
    list_display = ('employee', 'payslip_number', 'month', 'year', 'generation_date', 'gross_earnings', 'total_deductions', 'net_payable', 'status')
    list_filter = ('status', 'month', 'year', 'email_sent')
    search_fields = ('employee__first_name', 'employee__last_name', 'employee__email', 'payslip_number')
    date_hierarchy = 'generation_date'


@admin.register(Loan)
class LoanAdmin(admin.ModelAdmin):
    list_display = ('employee', 'loan_type', 'loan_amount', 'interest_rate', 'term_months', 'emi_amount', 'start_date', 'remaining_amount', 'status')
    list_filter = ('loan_type', 'status', 'start_date')
    search_fields = ('employee__first_name', 'employee__last_name', 'employee__email', 'purpose')
    date_hierarchy = 'start_date'


@admin.register(LoanInstallment)
class LoanInstallmentAdmin(admin.ModelAdmin):
    list_display = ('loan', 'installment_number', 'due_date', 'amount', 'paid_amount', 'status')
    list_filter = ('status', 'due_date')
    search_fields = ('loan__employee__first_name', 'loan__employee__last_name', 'remarks')
    date_hierarchy = 'due_date'


@admin.register(Bonus)
class BonusAdmin(admin.ModelAdmin):
    list_display = ('employee', 'bonus_type', 'amount', 'payment_date', 'status', 'is_taxable')
    list_filter = ('bonus_type', 'status', 'is_taxable', 'is_prorated', 'payment_date')
    search_fields = ('employee__first_name', 'employee__last_name', 'employee__email', 'description')
    date_hierarchy = 'payment_date'


@admin.register(BonusBatch)
class BonusBatchAdmin(admin.ModelAdmin):
    list_display = ('name', 'organization', 'bonus_type', 'payment_date', 'total_amount', 'status')
    list_filter = ('bonus_type', 'status', 'payment_date')
    search_fields = ('name', 'description')
    date_hierarchy = 'payment_date'


@admin.register(TaxSlab)
class TaxSlabAdmin(admin.ModelAdmin):
    list_display = ('organization', 'financial_year', 'name', 'min_income', 'max_income', 'tax_rate', 'gender', 'age_group', 'is_active')
    list_filter = ('financial_year', 'gender', 'age_group', 'is_active')
    search_fields = ('name', 'financial_year')


@admin.register(EmployeeTaxDeclaration)
class EmployeeTaxDeclarationAdmin(admin.ModelAdmin):
    list_display = ('employee', 'financial_year', 'declaration_type', 'declaration_name', 'declared_amount', 'verified_amount', 'status')
    list_filter = ('financial_year', 'declaration_type', 'status')
    search_fields = ('employee__first_name', 'employee__last_name', 'employee__email', 'declaration_name')


@admin.register(BankTransfer)
class BankTransferAdmin(admin.ModelAdmin):
    list_display = ('reference_number', 'organization', 'transfer_date', 'bank_name', 'transfer_type', 'total_amount', 'total_employees', 'status')
    list_filter = ('transfer_type', 'status', 'transfer_date')
    search_fields = ('reference_number', 'bank_name', 'account_number')
    date_hierarchy = 'transfer_date'
