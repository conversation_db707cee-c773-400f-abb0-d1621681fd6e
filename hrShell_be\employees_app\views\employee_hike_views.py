from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Avg, Sum, Count
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from employees_app.models.employee_hike import EmployeeHike
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from employees_app.serializers.employee_hike_serializer import (
    EmployeeHikeSerializer,
    EmployeeHikeListSerializer,
    EmployeeHikeDetailSerializer
)
from employees_app.filters import EmployeeHikeFilter
from datetime import datetime


class EmployeeHikeViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing EmployeeHike instances.

    Additional actions:
    - employee_hikes: Get hikes for a specific employee
    - department_hikes: Get hikes for a specific department
    - date_range_hikes: Get hikes within a date range
    - approve_hike: Approve a hike
    - reject_hike: Reject a hike
    - hike_statistics: Get statistics about hikes
    """
    queryset = EmployeeHike.objects.all()
    serializer_class = EmployeeHikeSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = EmployeeHikeFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'hike_reason']
    ordering_fields = ['hike_percentage', 'hike_effective_date', 'previous_salary', 'new_salary']

    def get_serializer_class(self):
        """
        Return different serializers based on the action
        """
        if self.action == 'list':
            return EmployeeHikeListSerializer
        elif self.action == 'retrieve':
            return EmployeeHikeDetailSerializer
        return EmployeeHikeSerializer

    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_hikes(self, request, employee_id=None):
        """
        Get all hikes for a specific employee
        """
        try:
            employee = Employee.objects.get(pk=employee_id)
        except Employee.DoesNotExist:
            return Response(
                {"detail": "Employee not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        hikes = self.queryset.filter(employee=employee)
        page = self.paginate_queryset(hikes)

        if page is not None:
            serializer = EmployeeHikeListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = EmployeeHikeListSerializer(hikes, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_hikes(self, request, department_id=None):
        """
        Get all hikes for a specific department
        """
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return Response(
                {"detail": "Department not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        hikes = self.queryset.filter(department=department)
        page = self.paginate_queryset(hikes)

        if page is not None:
            serializer = EmployeeHikeListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = EmployeeHikeListSerializer(hikes, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def date_range_hikes(self, request):
        """
        Get all hikes within a date range
        """
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)

        if not start_date or not end_date:
            return Response(
                {"detail": "Both start_date and end_date are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"detail": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )

        hikes = self.queryset.filter(
            hike_effective_date__gte=start_date,
            hike_effective_date__lte=end_date
        )
        page = self.paginate_queryset(hikes)

        if page is not None:
            serializer = EmployeeHikeListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = EmployeeHikeListSerializer(hikes, many=True)
        return Response(serializer.data)

    def approve_hike(self, request, pk=None):
        """
        Approve a hike (admin only)
        """
        hike = self.get_object()

        # Check if the hike is already approved
        if hasattr(hike, 'status') and hike.status == 'approved':
            return Response(
                {"detail": "This hike is already approved"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Set the approved_by field to the current user if it's an admin
        if request.user.is_staff:
            try:
                admin_employee = Employee.objects.get(email=request.user.email)
                hike.approved_by = admin_employee

                # Set status to approved if the field exists
                if hasattr(hike, 'status'):
                    hike.status = 'approved'

                hike.save()
                serializer = EmployeeHikeDetailSerializer(hike)
                return Response(serializer.data)
            except Employee.DoesNotExist:
                return Response(
                    {"detail": "Admin user is not linked to an employee record"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            return Response(
                {"detail": "Only admin users can approve hikes"},
                status=status.HTTP_403_FORBIDDEN
            )

    def reject_hike(self, request, pk=None):
        """
        Reject a hike (admin only)
        """
        hike = self.get_object()

        # Check if the hike has a status field and if it's already rejected
        if hasattr(hike, 'status') and hike.status == 'rejected':
            return Response(
                {"detail": "This hike is already rejected"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Set status to rejected if the field exists
        if hasattr(hike, 'status'):
            hike.status = 'rejected'
            hike.save()
            serializer = EmployeeHikeDetailSerializer(hike)
            return Response(serializer.data)
        else:
            return Response(
                {"detail": "This hike model does not support rejection"},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def hike_statistics(self, request):
        """
        Get statistics about hikes
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)
        year = request.query_params.get('year', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                queryset = queryset.filter(department=department)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Filter by year if provided
        if year:
            try:
                year = int(year)
                queryset = queryset.filter(hike_effective_date__year=year)
            except ValueError:
                return Response(
                    {"detail": "Invalid year format"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Calculate statistics
        stats = {
            'total_hikes': queryset.count(),
            'average_hike_percentage': queryset.aggregate(Avg('hike_percentage'))['hike_percentage__avg'] or 0,
            'total_employees_hiked': queryset.values('employee').distinct().count(),
            'department_wise_count': []
        }

        # Get department-wise statistics
        departments = Department.objects.all()
        for dept in departments:
            dept_hikes = queryset.filter(department=dept)
            if dept_hikes.exists():
                dept_stats = {
                    'department_id': dept.id,
                    'department_name': dept.name,
                    'hike_count': dept_hikes.count(),
                    'average_hike_percentage': dept_hikes.aggregate(Avg('hike_percentage'))['hike_percentage__avg'] or 0
                }
                stats['department_wise_count'].append(dept_stats)

        return Response(stats)
