from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from organization_app.models.organization import Organization
from organization_app.models.location import Location
from organization_app.models.business_unit import BusinessUnit
from organization_app.models.designation import Designation
import json


class OrganizationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test organizations
        cls.organization1 = Organization.objects.create(
            name="Test Organization 1",
            registration_number="TEST123",
            business_type="private_limited",
            status="active"
        )
        
        cls.organization2 = Organization.objects.create(
            name="Test Organization 2",
            registration_number="TEST456",
            business_type="public_limited",
            status="active"
        )
    
    def setUp(self):
        self.client = APIClient()
        # Add authentication if needed
        # self.client.credentials(HTTP_AUTHORIZATION='Bearer ' + token)
    
    def test_list_organizations(self):
        url = reverse('organization:organization-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_retrieve_organization(self):
        url = reverse('organization:organization-detail', args=[self.organization1.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Test Organization 1')
        self.assertEqual(response.data['registration_number'], 'TEST123')
    
    def test_create_organization(self):
        url = reverse('organization:organization-list')
        data = {
            'name': 'New Organization',
            'registration_number': 'NEW123',
            'business_type': 'llc',
            'status': 'active'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Organization.objects.count(), 3)
        self.assertEqual(Organization.objects.get(registration_number='NEW123').name, 'New Organization')
    
    def test_update_organization(self):
        url = reverse('organization:organization-detail', args=[self.organization1.id])
        data = {
            'name': 'Updated Organization',
            'registration_number': 'TEST123',
            'business_type': 'private_limited',
            'status': 'active'
        }
        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.organization1.refresh_from_db()
        self.assertEqual(self.organization1.name, 'Updated Organization')
    
    def test_delete_organization(self):
        url = reverse('organization:organization-detail', args=[self.organization2.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Organization.objects.count(), 1)


# Add more test classes for other views as needed
