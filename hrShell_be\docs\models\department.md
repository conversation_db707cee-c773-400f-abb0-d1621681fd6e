# Department Model

The Department model represents a department in the organization.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the department | Yes (unique) |
| `description` | Text | Description of the department | No |
| `manager` | ForeignKey | Employee who manages the department | No |
| `status` | String | Status of the department (choices: active, inactive) | Yes (default: active) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Properties

| Property | Type | Description |
|----------|------|-------------|
| `employee_count` | Integer | Number of employees in the department |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `manager` | Employee | The employee who manages the department |
| `employees` | Employee | The employees who belong to the department |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name of the department |

## Example

```python
department = Department.objects.create(
    name="Engineering",
    description="Software development department",
    manager=john_doe,
    status="active"
)

print(department.name)  # Output: "Engineering"
print(department.employee_count)  # Output: Number of employees in the department
```

## Validation Rules

- Name must be unique
- Status must be one of: active or inactive
