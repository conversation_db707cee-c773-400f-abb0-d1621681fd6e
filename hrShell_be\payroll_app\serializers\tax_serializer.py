from rest_framework import serializers
from payroll_app.models.tax import TaxSlab, EmployeeTaxDeclaration


class TaxSlabSerializer(serializers.ModelSerializer):
    organization_name = serializers.SerializerMethodField()
    gender_display = serializers.SerializerMethodField()
    age_group_display = serializers.SerializerMethodField()
    
    class Meta:
        model = TaxSlab
        fields = [
            'id', 'organization', 'organization_name', 'financial_year', 'name',
            'min_income', 'max_income', 'tax_rate', 'surcharge_rate', 'cess_rate',
            'gender', 'gender_display', 'age_group', 'age_group_display', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_organization_name(self, obj):
        return obj.organization.name if obj.organization else None
    
    def get_gender_display(self, obj):
        return obj.get_gender_display()
    
    def get_age_group_display(self, obj):
        return obj.get_age_group_display()
    
    def validate(self, data):
        # Validate income range
        if data.get('max_income') and data.get('min_income', 0) >= data.get('max_income'):
            raise serializers.ValidationError({"max_income": "Maximum income must be greater than minimum income"})
        
        # Validate tax rate
        if data.get('tax_rate', 0) < 0 or data.get('tax_rate', 0) > 100:
            raise serializers.ValidationError({"tax_rate": "Tax rate must be between 0 and 100"})
        
        # Validate surcharge rate
        if data.get('surcharge_rate', 0) < 0 or data.get('surcharge_rate', 0) > 100:
            raise serializers.ValidationError({"surcharge_rate": "Surcharge rate must be between 0 and 100"})
        
        # Validate cess rate
        if data.get('cess_rate', 0) < 0 or data.get('cess_rate', 0) > 100:
            raise serializers.ValidationError({"cess_rate": "Cess rate must be between 0 and 100"})
        
        return data


class EmployeeTaxDeclarationSerializer(serializers.ModelSerializer):
    employee_name = serializers.SerializerMethodField()
    declaration_type_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    verified_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = EmployeeTaxDeclaration
        fields = [
            'id', 'employee', 'employee_name', 'financial_year', 'declaration_type',
            'declaration_type_display', 'declaration_name', 'declared_amount',
            'proof_document', 'verified_amount', 'status', 'status_display',
            'remarks', 'verified_by', 'verified_by_name', 'verification_date',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_employee_name(self, obj):
        return f"{obj.employee.first_name} {obj.employee.last_name}" if obj.employee else None
    
    def get_declaration_type_display(self, obj):
        return obj.get_declaration_type_display()
    
    def get_status_display(self, obj):
        return obj.get_status_display()
    
    def get_verified_by_name(self, obj):
        if not obj.verified_by:
            return None
        return f"{obj.verified_by.first_name} {obj.verified_by.last_name}"
    
    def validate(self, data):
        # Validate declared amount
        if data.get('declared_amount', 0) <= 0:
            raise serializers.ValidationError({"declared_amount": "Declared amount must be greater than 0"})
        
        # Validate verified amount if provided
        if 'verified_amount' in data and data['verified_amount'] is not None:
            if data['verified_amount'] < 0:
                raise serializers.ValidationError({"verified_amount": "Verified amount cannot be negative"})
            
            if data['verified_amount'] > data.get('declared_amount', 0):
                raise serializers.ValidationError({"verified_amount": "Verified amount cannot be greater than declared amount"})
        
        return data
