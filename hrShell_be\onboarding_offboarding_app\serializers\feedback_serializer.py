from rest_framework import serializers
from onboarding_offboarding_app.models.feedback import ExitFeedback
from employees_app.serializers.employee_serializer import EmployeeSerializer
from onboarding_offboarding_app.serializers.offboarding_serializer import OffboardingRequestSerializer


class ExitFeedbackSerializer(serializers.ModelSerializer):
    """
    Serializer for the ExitFeedback model.
    """
    employee_details = EmployeeSerializer(source='employee', read_only=True)
    offboarding_request_details = OffboardingRequestSerializer(source='offboarding_request', read_only=True)
    conducted_by_details = EmployeeSerializer(source='conducted_by', read_only=True)
    
    class Meta:
        model = ExitFeedback
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate the exit feedback data.
        """
        # Validate employee matches the offboarding request employee
        employee = data.get('employee')
        offboarding_request = data.get('offboarding_request')
        
        if employee and offboarding_request and employee != offboarding_request.employee:
            raise serializers.ValidationError("Employee must match the offboarding request employee.")
        
        return data
