from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from leave_app.models.leave_approval import LeaveApproval, ApprovalStatus
from leave_app.models.leave_request import LeaveRequest, LeaveRequestStatus
from leave_app.models.leave_balance import LeaveBalance
from leave_app.serializers.leave_approval_serializer import LeaveApprovalSerializer
from leave_app.filters.leave_filters import LeaveApprovalFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class LeaveApprovalViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Leave Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for managing leave approvals
    
    list:
    Return a list of all leave approvals
    
    create:
    Create a new leave approval
    
    retrieve:
    Return the given leave approval
    
    update:
    Update the given leave approval
    
    partial_update:
    Partially update the given leave approval
    
    destroy:
    Delete the given leave approval
    
    Additional actions:
    - approver_approvals: Get leave approvals for a specific approver
    - pending_approvals: Get all pending leave approvals
    - approve: Approve a leave approval
    - reject: Reject a leave approval
    - skip: Skip a leave approval
    """
    queryset = LeaveApproval.objects.all()
    serializer_class = LeaveApprovalSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LeaveApprovalFilter
    ordering_fields = ['leave_request__start_date', 'level', 'created_at']
    ordering = ['leave_request__start_date', 'level']
    
    @action(detail=False, methods=['get'], url_path='approver/(?P<approver_id>[^/.]+)')
    def approver_approvals(self, request, approver_id=None):
        """
        Get leave approvals for a specific approver
        """
        approvals = self.queryset.filter(approver_id=approver_id)
        page = self.paginate_queryset(approvals)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(approvals, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def pending_approvals(self, request):
        """
        Get all pending leave approvals
        """
        approvals = self.queryset.filter(status=ApprovalStatus.PENDING.value)
        page = self.paginate_queryset(approvals)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(approvals, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """
        Approve a leave approval
        """
        approval = self.get_object()
        
        if approval.status != ApprovalStatus.PENDING.value:
            return Response(
                {"detail": "Only pending approvals can be approved"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get comments from request data
        comments = request.data.get('comments', '')
        
        # Update approval status
        approval.status = ApprovalStatus.APPROVED.value
        approval.comments = comments
        approval.approval_date = timezone.now()
        approval.save()
        
        # Check if this is the final approval
        leave_request = approval.leave_request
        pending_approvals = leave_request.approvals.filter(status=ApprovalStatus.PENDING.value)
        
        if not pending_approvals.exists():
            # All approvals are complete, update leave request status
            leave_request.status = LeaveRequestStatus.APPROVED.value
            leave_request.approved_by = approval.approver
            leave_request.approval_date = timezone.now()
            leave_request.save()
            
            # Update leave balance
            try:
                current_year = leave_request.start_date.year
                leave_balance = LeaveBalance.objects.get(
                    employee=leave_request.employee,
                    leave_type=leave_request.leave_type,
                    year=current_year
                )
                
                # Update used days
                leave_balance.used += leave_request.total_days
                leave_balance.save()
                
            except LeaveBalance.DoesNotExist:
                # If no balance exists, create one
                LeaveBalance.objects.create(
                    employee=leave_request.employee,
                    leave_type=leave_request.leave_type,
                    year=current_year,
                    used=leave_request.total_days
                )
        
        serializer = self.get_serializer(approval)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """
        Reject a leave approval
        """
        approval = self.get_object()
        
        if approval.status != ApprovalStatus.PENDING.value:
            return Response(
                {"detail": "Only pending approvals can be rejected"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get comments from request data
        comments = request.data.get('comments', '')
        
        # Update approval status
        approval.status = ApprovalStatus.REJECTED.value
        approval.comments = comments
        approval.approval_date = timezone.now()
        approval.save()
        
        # Update leave request status
        leave_request = approval.leave_request
        leave_request.status = LeaveRequestStatus.REJECTED.value
        leave_request.approved_by = approval.approver
        leave_request.approval_date = timezone.now()
        leave_request.rejection_reason = comments
        leave_request.save()
        
        # Mark all other pending approvals as skipped
        leave_request.approvals.filter(status=ApprovalStatus.PENDING.value).update(
            status=ApprovalStatus.SKIPPED.value,
            comments="Skipped due to rejection at an earlier level",
            approval_date=timezone.now()
        )
        
        serializer = self.get_serializer(approval)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def skip(self, request, pk=None):
        """
        Skip a leave approval
        """
        approval = self.get_object()
        
        if approval.status != ApprovalStatus.PENDING.value:
            return Response(
                {"detail": "Only pending approvals can be skipped"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get comments from request data
        comments = request.data.get('comments', '')
        
        # Update approval status
        approval.status = ApprovalStatus.SKIPPED.value
        approval.comments = comments
        approval.approval_date = timezone.now()
        approval.save()
        
        # Check if this is the final approval
        leave_request = approval.leave_request
        pending_approvals = leave_request.approvals.filter(status=ApprovalStatus.PENDING.value)
        
        if not pending_approvals.exists():
            # All approvals are complete, update leave request status
            leave_request.status = LeaveRequestStatus.APPROVED.value
            leave_request.approved_by = request.user.employee
            leave_request.approval_date = timezone.now()
            leave_request.save()
            
            # Update leave balance
            try:
                current_year = leave_request.start_date.year
                leave_balance = LeaveBalance.objects.get(
                    employee=leave_request.employee,
                    leave_type=leave_request.leave_type,
                    year=current_year
                )
                
                # Update used days
                leave_balance.used += leave_request.total_days
                leave_balance.save()
                
            except LeaveBalance.DoesNotExist:
                # If no balance exists, create one
                LeaveBalance.objects.create(
                    employee=leave_request.employee,
                    leave_type=leave_request.leave_type,
                    year=current_year,
                    used=leave_request.total_days
                )
        
        serializer = self.get_serializer(approval)
        return Response(serializer.data)
