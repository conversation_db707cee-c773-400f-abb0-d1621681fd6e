from rest_framework import serializers
from recruitment_app.models.skill import Skill
from organization_app.serializers.organization_serializer import OrganizationSerializer


class SkillSerializer(serializers.ModelSerializer):
    """
    Serializer for the Skill model.
    """
    organization_details = OrganizationSerializer(source='organization', read_only=True)
    parent_skill_details = serializers.SerializerMethodField()
    
    class Meta:
        model = Skill
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def get_parent_skill_details(self, obj):
        """
        Get the parent skill details.
        """
        if obj.parent_skill:
            return {
                'id': obj.parent_skill.id,
                'name': obj.parent_skill.name,
                'code': obj.parent_skill.code,
                'skill_type': obj.parent_skill.skill_type
            }
        return None
