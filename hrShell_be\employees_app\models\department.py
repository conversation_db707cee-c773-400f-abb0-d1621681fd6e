from django.db import models
from enum import Enum


class DepartmentStatus(str, Enum):
    ACTIVE = 'active'
    INACTIVE = 'inactive'

    @classmethod
    def choices(cls):
        return [(item.value, item.name.title()) for item in cls]


class Department(models.Model):
    """
    Department model for storing department information
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    manager = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_departments'
    )
    status = models.CharField(
        max_length=10,
        choices=DepartmentStatus.choices(),
        default=DepartmentStatus.ACTIVE.value
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    @property
    def employee_count(self):
        return self.employees.count()
