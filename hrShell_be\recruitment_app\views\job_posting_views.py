from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.utils.text import slugify
from recruitment_app.models.job_posting import JobPosting
from recruitment_app.serializers.job_posting_serializer import JobPostingSerializer
from recruitment_app.filters.recruitment_filters import JobPostingFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class JobPostingViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for job postings.
    """
    queryset = JobPosting.objects.all()
    serializer_class = JobPostingSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = JobPostingFilter
    search_fields = ['title', 'description', 'requirements', 'responsibilities', 'qualifications', 'benefits']
    ordering_fields = ['published_date', 'expiry_date', 'title', 'view_count', 'application_count', 'created_at']
    ordering = ['-published_date']
    
    def perform_create(self, serializer):
        """
        Create a slug for the job posting if not provided.
        """
        if not serializer.validated_data.get('slug'):
            title = serializer.validated_data.get('title')
            slug = slugify(title)
            
            # Ensure slug is unique
            if JobPosting.objects.filter(slug=slug).exists():
                slug = f"{slug}-{timezone.now().strftime('%Y%m%d%H%M%S')}"
            
            serializer.save(slug=slug)
        else:
            serializer.save()
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all job postings with a specific status.
        """
        postings = JobPosting.objects.filter(status=status_value)
        page = self.paginate_queryset(postings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(postings, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def requisition(self, request, requisition_pk=None):
        """
        Get all job postings for a specific requisition.
        """
        postings = JobPosting.objects.filter(job_requisition_id=requisition_pk)
        page = self.paginate_queryset(postings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(postings, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def source(self, request, source_pk=None):
        """
        Get all job postings for a specific source.
        """
        postings = JobPosting.objects.filter(sources__id=source_pk)
        page = self.paginate_queryset(postings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(postings, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get all active job postings (published and not expired).
        """
        today = timezone.now().date()
        postings = JobPosting.objects.filter(
            status='published',
            published_date__lte=today
        ).filter(
            expiry_date__isnull=True
        ) | JobPosting.objects.filter(
            status='published',
            published_date__lte=today,
            expiry_date__gte=today
        )
        
        page = self.paginate_queryset(postings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(postings, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def featured(self, request):
        """
        Get all featured job postings.
        """
        postings = JobPosting.objects.filter(is_featured=True, status='published')
        page = self.paginate_queryset(postings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(postings, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """
        Publish a job posting.
        """
        posting = self.get_object()
        
        if posting.status != 'draft':
            return Response(
                {"detail": "Only draft postings can be published."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update posting status
        posting.status = 'published'
        posting.published_date = timezone.now().date()
        
        # Set expiry date if provided
        expiry_date = request.data.get('expiry_date')
        if expiry_date:
            posting.expiry_date = expiry_date
        
        posting.save()
        
        serializer = self.get_serializer(posting)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def expire(self, request, pk=None):
        """
        Expire a job posting.
        """
        posting = self.get_object()
        
        if posting.status != 'published':
            return Response(
                {"detail": "Only published postings can be expired."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        posting.status = 'expired'
        posting.save()
        
        serializer = self.get_serializer(posting)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def close(self, request, pk=None):
        """
        Close a job posting.
        """
        posting = self.get_object()
        
        if posting.status in ['closed', 'expired']:
            return Response(
                {"detail": "Posting is already closed or expired."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        posting.status = 'closed'
        posting.save()
        
        serializer = self.get_serializer(posting)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def increment_view(self, request, pk=None):
        """
        Increment the view count for a job posting.
        """
        posting = self.get_object()
        posting.view_count += 1
        posting.save()
        
        return Response({"detail": "View count incremented.", "view_count": posting.view_count})
    
    @action(detail=True, methods=['post'])
    def add_source(self, request, pk=None):
        """
        Add a source to a job posting.
        """
        posting = self.get_object()
        source_id = request.data.get('source_id')
        
        if not source_id:
            return Response(
                {"detail": "Source ID is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        posting.sources.add(source_id)
        
        serializer = self.get_serializer(posting)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def remove_source(self, request, pk=None):
        """
        Remove a source from a job posting.
        """
        posting = self.get_object()
        source_id = request.data.get('source_id')
        
        if not source_id:
            return Response(
                {"detail": "Source ID is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        posting.sources.remove(source_id)
        
        serializer = self.get_serializer(posting)
        return Response(serializer.data)
