from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from onboarding_offboarding_app.models.onboarding import OnboardingPlan, OnboardingTask
from onboarding_offboarding_app.serializers.onboarding_serializer import OnboardingPlanSerializer, OnboardingTaskSerializer
from onboarding_offboarding_app.filters.onboarding_offboarding_filters import OnboardingPlanFilter, OnboardingTaskFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class OnboardingPlanViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for onboarding plans.
    """
    queryset = OnboardingPlan.objects.all()
    serializer_class = OnboardingPlanSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OnboardingPlanFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email', 'notes']
    ordering_fields = ['start_date', 'end_date', 'status', 'created_at']
    ordering = ['-start_date']
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all onboarding plans with a specific status.
        """
        plans = OnboardingPlan.objects.filter(status=status_value)
        page = self.paginate_queryset(plans)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(plans, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get the onboarding plan for a specific employee.
        """
        try:
            plan = OnboardingPlan.objects.get(employee_id=employee_pk)
            serializer = self.get_serializer(plan)
            return Response(serializer.data)
        except OnboardingPlan.DoesNotExist:
            return Response(
                {"detail": "No onboarding plan found for this employee."},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def department(self, request, department_pk=None):
        """
        Get all onboarding plans for a specific department.
        """
        plans = OnboardingPlan.objects.filter(department_id=department_pk)
        page = self.paginate_queryset(plans)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(plans, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def mentor(self, request, mentor_pk=None):
        """
        Get all onboarding plans for a specific mentor.
        """
        plans = OnboardingPlan.objects.filter(mentor_id=mentor_pk)
        page = self.paginate_queryset(plans)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(plans, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def tasks(self, request, pk=None):
        """
        Get all tasks for a specific onboarding plan.
        """
        plan = self.get_object()
        tasks = OnboardingTask.objects.filter(plan=plan)
        
        serializer = OnboardingTaskSerializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """
        Start an onboarding plan.
        """
        plan = self.get_object()
        
        if plan.status != 'pending':
            return Response(
                {"detail": "Only pending onboarding plans can be started."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        plan.status = 'in_progress'
        plan.save()
        
        serializer = self.get_serializer(plan)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """
        Complete an onboarding plan.
        """
        plan = self.get_object()
        
        if plan.status != 'in_progress':
            return Response(
                {"detail": "Only in-progress onboarding plans can be completed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if all mandatory tasks are completed
        incomplete_mandatory_tasks = OnboardingTask.objects.filter(
            plan=plan,
            is_mandatory=True
        ).exclude(status='completed')
        
        if incomplete_mandatory_tasks.exists():
            return Response(
                {"detail": "Cannot complete onboarding plan. There are incomplete mandatory tasks."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        plan.status = 'completed'
        plan.save()
        
        serializer = self.get_serializer(plan)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel an onboarding plan.
        """
        plan = self.get_object()
        
        if plan.status == 'completed':
            return Response(
                {"detail": "Completed onboarding plans cannot be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        plan.status = 'cancelled'
        plan.save()
        
        # Cancel all pending tasks
        OnboardingTask.objects.filter(plan=plan).exclude(status__in=['completed', 'cancelled']).update(status='cancelled')
        
        serializer = self.get_serializer(plan)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def send_welcome_email(self, request, pk=None):
        """
        Send welcome email to the employee.
        """
        plan = self.get_object()
        
        if plan.welcome_email_sent:
            return Response(
                {"detail": "Welcome email has already been sent."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Logic to send welcome email would go here
        
        plan.welcome_email_sent = True
        plan.welcome_email_sent_at = timezone.now()
        plan.save()
        
        serializer = self.get_serializer(plan)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def assign_mentor(self, request, pk=None):
        """
        Assign a mentor to an onboarding plan.
        """
        plan = self.get_object()
        
        # Validate required fields
        if 'mentor' not in request.data:
            return Response(
                {"detail": "Field 'mentor' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        plan.mentor_id = request.data.get('mentor')
        plan.save()
        
        serializer = self.get_serializer(plan)
        return Response(serializer.data)


class OnboardingTaskViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for onboarding tasks.
    """
    queryset = OnboardingTask.objects.all()
    serializer_class = OnboardingTaskSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OnboardingTaskFilter
    search_fields = ['name', 'description', 'notes']
    ordering_fields = ['sequence', 'due_date', 'status', 'created_at']
    ordering = ['sequence', 'due_date']
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all onboarding tasks with a specific status.
        """
        tasks = OnboardingTask.objects.filter(status=status_value)
        page = self.paginate_queryset(tasks)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def plan(self, request, plan_pk=None):
        """
        Get all onboarding tasks for a specific plan.
        """
        tasks = OnboardingTask.objects.filter(plan_id=plan_pk)
        page = self.paginate_queryset(tasks)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def category(self, request, category=None):
        """
        Get all onboarding tasks with a specific category.
        """
        tasks = OnboardingTask.objects.filter(category=category)
        page = self.paginate_queryset(tasks)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def assigned_to(self, request, employee_pk=None):
        """
        Get all onboarding tasks assigned to a specific employee.
        """
        tasks = OnboardingTask.objects.filter(assigned_to_id=employee_pk)
        page = self.paginate_queryset(tasks)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """
        Start an onboarding task.
        """
        task = self.get_object()
        
        if task.status != 'not_started':
            return Response(
                {"detail": "Only not started tasks can be started."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.status = 'in_progress'
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """
        Complete an onboarding task.
        """
        task = self.get_object()
        
        if task.status in ['completed', 'cancelled']:
            return Response(
                {"detail": "Task is already completed or cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.status = 'completed'
        task.completed_date = timezone.now().date()
        
        # Set completed_by if provided
        completed_by_id = request.data.get('completed_by')
        if completed_by_id:
            task.completed_by_id = completed_by_id
        
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel an onboarding task.
        """
        task = self.get_object()
        
        if task.status == 'completed':
            return Response(
                {"detail": "Completed tasks cannot be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.status = 'cancelled'
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """
        Assign an onboarding task to an employee.
        """
        task = self.get_object()
        
        # Validate required fields
        if 'assigned_to' not in request.data:
            return Response(
                {"detail": "Field 'assigned_to' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.assigned_to_id = request.data.get('assigned_to')
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
