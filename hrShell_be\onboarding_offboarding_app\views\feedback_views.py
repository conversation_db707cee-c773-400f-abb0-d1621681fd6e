from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from onboarding_offboarding_app.models.feedback import ExitFeedback
from onboarding_offboarding_app.serializers.feedback_serializer import ExitFeedbackSerializer
from onboarding_offboarding_app.filters.onboarding_offboarding_filters import ExitFeedbackFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class ExitFeedbackViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for exit feedback.
    """
    queryset = ExitFeedback.objects.all()
    serializer_class = ExitFeedbackSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ExitFeedbackFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'reason_for_leaving', 'what_did_you_like', 'what_could_be_improved', 'additional_comments']
    ordering_fields = ['overall_experience', 'interview_date', 'created_at']
    ordering = ['-created_at']
    
    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all exit feedback for a specific employee.
        """
        feedback = ExitFeedback.objects.filter(employee_id=employee_pk)
        page = self.paginate_queryset(feedback)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(feedback, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def offboarding_request(self, request, offboarding_request_pk=None):
        """
        Get the exit feedback for a specific offboarding request.
        """
        try:
            feedback = ExitFeedback.objects.get(offboarding_request_id=offboarding_request_pk)
            serializer = self.get_serializer(feedback)
            return Response(serializer.data)
        except ExitFeedback.DoesNotExist:
            return Response(
                {"detail": "No exit feedback found for this offboarding request."},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def conducted_by(self, request, employee_pk=None):
        """
        Get all exit feedback conducted by a specific employee.
        """
        feedback = ExitFeedback.objects.filter(conducted_by_id=employee_pk)
        page = self.paginate_queryset(feedback)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(feedback, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def ratings_summary(self, request):
        """
        Get a summary of exit feedback ratings.
        """
        from django.db.models import Avg
        
        summary = {
            'overall_experience': ExitFeedback.objects.aggregate(Avg('overall_experience'))['overall_experience__avg'],
            'work_environment': ExitFeedback.objects.aggregate(Avg('work_environment'))['work_environment__avg'],
            'work_life_balance': ExitFeedback.objects.aggregate(Avg('work_life_balance'))['work_life_balance__avg'],
            'compensation_benefits': ExitFeedback.objects.aggregate(Avg('compensation_benefits'))['compensation_benefits__avg'],
            'career_growth': ExitFeedback.objects.aggregate(Avg('career_growth'))['career_growth__avg'],
            'management': ExitFeedback.objects.aggregate(Avg('management'))['management__avg'],
            'would_recommend_percentage': self._calculate_percentage('would_recommend'),
            'would_return_percentage': self._calculate_percentage('would_return'),
        }
        
        return Response(summary)
    
    def _calculate_percentage(self, field):
        """
        Calculate the percentage of True values for a boolean field.
        """
        total = ExitFeedback.objects.filter(**{f"{field}__isnull": False}).count()
        if total == 0:
            return None
        
        true_count = ExitFeedback.objects.filter(**{field: True}).count()
        return (true_count / total) * 100
