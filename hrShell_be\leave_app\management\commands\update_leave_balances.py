from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Q
from employees_app.models.employee import Employee
from leave_app.models.leave_type import LeaveType
from leave_app.models.leave_policy import LeavePolicy, AccrualMethod
from leave_app.models.leave_balance import LeaveBalance
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Update leave balances based on accrual policies'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--year',
            type=int,
            help='Year to update leave balances for (default: current year)',
        )
        parser.add_argument(
            '--employee-id',
            type=int,
            help='Employee ID to update leave balances for (default: all employees)',
        )
        parser.add_argument(
            '--leave-type-id',
            type=int,
            help='Leave Type ID to update leave balances for (default: all leave types)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform a dry run without making any changes',
        )
    
    def handle(self, *args, **options):
        year = options['year'] or timezone.now().year
        employee_id = options['employee_id']
        leave_type_id = options['leave_type_id']
        dry_run = options['dry_run']
        
        self.stdout.write(f"Updating leave balances for year {year}")
        if dry_run:
            self.stdout.write("DRY RUN: No changes will be made")
        
        # Get active employees
        employees = Employee.objects.filter(status='active')
        if employee_id:
            employees = employees.filter(id=employee_id)
        
        # Get active leave types
        leave_types = LeaveType.objects.filter(status='active')
        if leave_type_id:
            leave_types = leave_types.filter(id=leave_type_id)
        
        # Get current month and quarter
        current_date = timezone.now().date()
        current_month = current_date.month
        current_quarter = (current_month - 1) // 3 + 1
        
        # Process each employee
        for employee in employees:
            self.stdout.write(f"Processing employee: {employee}")
            
            # Get applicable leave policies for the employee
            for leave_type in leave_types:
                self.stdout.write(f"  Processing leave type: {leave_type.name}")
                
                # Find applicable policy
                policy = self._get_applicable_policy(employee, leave_type)
                if not policy:
                    self.stdout.write(f"    No policy found for {leave_type.name}")
                    continue
                
                # Get or create leave balance
                try:
                    balance = LeaveBalance.objects.get(
                        employee=employee,
                        leave_type=leave_type,
                        year=year
                    )
                    self.stdout.write(f"    Found existing balance: {balance.closing_balance}")
                except LeaveBalance.DoesNotExist:
                    if dry_run:
                        self.stdout.write(f"    Would create new balance for {leave_type.name}")
                        continue
                    
                    # Create new balance
                    balance = LeaveBalance.objects.create(
                        employee=employee,
                        leave_type=leave_type,
                        year=year,
                        opening_balance=0
                    )
                    self.stdout.write(f"    Created new balance for {leave_type.name}")
                
                # Update balance based on accrual method
                if policy.accrual_method == AccrualMethod.YEARLY.value:
                    self._update_yearly_accrual(balance, policy, current_date, dry_run)
                
                elif policy.accrual_method == AccrualMethod.MONTHLY.value:
                    self._update_monthly_accrual(balance, policy, current_month, dry_run)
                
                elif policy.accrual_method == AccrualMethod.QUARTERLY.value:
                    self._update_quarterly_accrual(balance, policy, current_quarter, dry_run)
                
                elif policy.accrual_method == AccrualMethod.BIANNUALLY.value:
                    self._update_biannual_accrual(balance, policy, current_month, dry_run)
        
        self.stdout.write(self.style.SUCCESS('Successfully updated leave balances'))
    
    def _get_applicable_policy(self, employee, leave_type):
        """
        Get the most specific applicable policy for an employee and leave type.
        """
        # Get employee details
        department = employee.department
        business_unit = department.business_unit if department and hasattr(department, 'business_unit') else None
        location = business_unit.primary_location if business_unit and hasattr(business_unit, 'primary_location') else None
        designation = employee.designation if hasattr(employee, 'designation') else None
        employee_type = employee.employee_type if hasattr(employee, 'employee_type') else None
        
        # Build query for policies
        query = Q(leave_type=leave_type)
        
        # Try to find the most specific policy
        if department and designation and location and business_unit and employee_type:
            policy = LeavePolicy.objects.filter(
                query,
                department=department,
                designation=designation,
                location=location,
                business_unit=business_unit,
                employee_type=employee_type
            ).first()
            if policy:
                return policy
        
        # Try with department, designation, location, business_unit
        if department and designation and location and business_unit:
            policy = LeavePolicy.objects.filter(
                query,
                department=department,
                designation=designation,
                location=location,
                business_unit=business_unit,
                employee_type__isnull=True
            ).first()
            if policy:
                return policy
        
        # Try with department, designation, location
        if department and designation and location:
            policy = LeavePolicy.objects.filter(
                query,
                department=department,
                designation=designation,
                location=location,
                business_unit__isnull=True,
                employee_type__isnull=True
            ).first()
            if policy:
                return policy
        
        # Try with department, designation
        if department and designation:
            policy = LeavePolicy.objects.filter(
                query,
                department=department,
                designation=designation,
                location__isnull=True,
                business_unit__isnull=True,
                employee_type__isnull=True
            ).first()
            if policy:
                return policy
        
        # Try with department only
        if department:
            policy = LeavePolicy.objects.filter(
                query,
                department=department,
                designation__isnull=True,
                location__isnull=True,
                business_unit__isnull=True,
                employee_type__isnull=True
            ).first()
            if policy:
                return policy
        
        # Try with designation only
        if designation:
            policy = LeavePolicy.objects.filter(
                query,
                department__isnull=True,
                designation=designation,
                location__isnull=True,
                business_unit__isnull=True,
                employee_type__isnull=True
            ).first()
            if policy:
                return policy
        
        # Try with location only
        if location:
            policy = LeavePolicy.objects.filter(
                query,
                department__isnull=True,
                designation__isnull=True,
                location=location,
                business_unit__isnull=True,
                employee_type__isnull=True
            ).first()
            if policy:
                return policy
        
        # Try with business_unit only
        if business_unit:
            policy = LeavePolicy.objects.filter(
                query,
                department__isnull=True,
                designation__isnull=True,
                location__isnull=True,
                business_unit=business_unit,
                employee_type__isnull=True
            ).first()
            if policy:
                return policy
        
        # Try with employee_type only
        if employee_type:
            policy = LeavePolicy.objects.filter(
                query,
                department__isnull=True,
                designation__isnull=True,
                location__isnull=True,
                business_unit__isnull=True,
                employee_type=employee_type
            ).first()
            if policy:
                return policy
        
        # Try with organization only (default policy)
        return LeavePolicy.objects.filter(
            query,
            department__isnull=True,
            designation__isnull=True,
            location__isnull=True,
            business_unit__isnull=True,
            employee_type__isnull=True
        ).first()
    
    def _update_yearly_accrual(self, balance, policy, current_date, dry_run):
        """
        Update balance based on yearly accrual.
        """
        # Check if it's the start of the year or if no accrual has been done yet
        if current_date.month == 1 and current_date.day <= 5 and balance.accrued == 0:
            accrual_amount = policy.leave_type.max_days_per_year
            
            if dry_run:
                self.stdout.write(f"    Would accrue {accrual_amount} days (yearly)")
            else:
                balance.accrued = accrual_amount
                balance.save()
                self.stdout.write(f"    Accrued {accrual_amount} days (yearly)")
    
    def _update_monthly_accrual(self, balance, policy, current_month, dry_run):
        """
        Update balance based on monthly accrual.
        """
        # Calculate monthly accrual amount
        monthly_accrual = policy.leave_type.max_days_per_year / 12
        
        # Calculate expected accrual for the current month
        expected_accrual = monthly_accrual * current_month
        
        # Check if accrual needs to be updated
        if balance.accrued < expected_accrual:
            accrual_amount = expected_accrual - balance.accrued
            
            if dry_run:
                self.stdout.write(f"    Would accrue {accrual_amount:.2f} days (monthly)")
            else:
                balance.accrued = expected_accrual
                balance.save()
                self.stdout.write(f"    Accrued {accrual_amount:.2f} days (monthly)")
    
    def _update_quarterly_accrual(self, balance, policy, current_quarter, dry_run):
        """
        Update balance based on quarterly accrual.
        """
        # Calculate quarterly accrual amount
        quarterly_accrual = policy.leave_type.max_days_per_year / 4
        
        # Calculate expected accrual for the current quarter
        expected_accrual = quarterly_accrual * current_quarter
        
        # Check if accrual needs to be updated
        if balance.accrued < expected_accrual:
            accrual_amount = expected_accrual - balance.accrued
            
            if dry_run:
                self.stdout.write(f"    Would accrue {accrual_amount:.2f} days (quarterly)")
            else:
                balance.accrued = expected_accrual
                balance.save()
                self.stdout.write(f"    Accrued {accrual_amount:.2f} days (quarterly)")
    
    def _update_biannual_accrual(self, balance, policy, current_month, dry_run):
        """
        Update balance based on biannual accrual.
        """
        # Calculate biannual accrual amount
        biannual_accrual = policy.leave_type.max_days_per_year / 2
        
        # Calculate expected accrual for the current period
        expected_accrual = 0
        if current_month >= 7:
            expected_accrual = biannual_accrual * 2
        elif current_month >= 1:
            expected_accrual = biannual_accrual
        
        # Check if accrual needs to be updated
        if balance.accrued < expected_accrual:
            accrual_amount = expected_accrual - balance.accrued
            
            if dry_run:
                self.stdout.write(f"    Would accrue {accrual_amount:.2f} days (biannual)")
            else:
                balance.accrued = expected_accrual
                balance.save()
                self.stdout.write(f"    Accrued {accrual_amount:.2f} days (biannual)")
