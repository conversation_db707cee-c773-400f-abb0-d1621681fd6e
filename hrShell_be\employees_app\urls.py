from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from employees_app.views.employee_views import EmployeeViewSet
from employees_app.views.department_views import DepartmentViewSet
from employees_app.views.auth_views import UserInfoView, LogoutView, RegisterView
from employees_app.views.employee_hike_views import EmployeeHikeViewSet
from employees_app.views.employee_detail_views import EmployeeDetailViewSet
from employees_app.views.employee_attendance_views import EmployeeAttendanceViewSet
from employees_app.views.employee_leave_views import EmployeeLeaveViewSet
from employees_app.views.job_history_views import JobHistoryViewSet
from employees_app.views.salary_views import SalaryViewSet
from employees_app.views.employee_document_views import EmployeeDocumentViewSet
from employees_app.views.skill_offering_views import SkillOfferingViewSet

app_name = 'employees'

router = DefaultRouter()
router.register(r'employees', EmployeeViewSet)
router.register(r'departments', DepartmentViewSet)
router.register(r'employee-hikes', EmployeeHikeViewSet)
router.register(r'employee-details', EmployeeDetailViewSet)
router.register(r'employee-attendance', EmployeeAttendanceViewSet)
router.register(r'employee-leaves', EmployeeLeaveViewSet)
router.register(r'job-history', JobHistoryViewSet)
router.register(r'salaries', SalaryViewSet)
router.register(r'employee-documents', EmployeeDocumentViewSet)
router.register(r'skill-offerings', SkillOfferingViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('user-info/', UserInfoView.as_view(), name='user-info'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('register/', RegisterView.as_view(), name='register'),
]