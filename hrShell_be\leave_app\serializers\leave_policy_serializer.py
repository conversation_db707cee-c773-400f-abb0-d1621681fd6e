from rest_framework import serializers
from leave_app.models.leave_policy import LeavePolicy, AccrualMethod, AccrualFrequency


class LeavePolicySerializer(serializers.ModelSerializer):
    """
    Serializer for the LeavePolicy model
    """
    organization_name = serializers.ReadOnlyField(source='organization.name')
    leave_type_name = serializers.ReadOnlyField(source='leave_type.name')
    department_name = serializers.ReadOnlyField(source='department.name')
    location_name = serializers.ReadOnlyField(source='location.name')
    designation_name = serializers.ReadOnlyField(source='designation.title')
    business_unit_name = serializers.ReadOnlyField(source='business_unit.name')
    accrual_method_display = serializers.ReadOnlyField(source='get_accrual_method_display')
    accrual_frequency_display = serializers.ReadOnlyField(source='get_accrual_frequency_display')
    
    class Meta:
        model = LeavePolicy
        fields = [
            'id', 'name', 'description', 'organization', 'organization_name',
            'leave_type', 'leave_type_name', 'department', 'department_name',
            'location', 'location_name', 'designation', 'designation_name',
            'business_unit', 'business_unit_name', 'employee_type',
            'accrual_method', 'accrual_method_display', 'accrual_frequency',
            'accrual_frequency_display', 'max_accrual', 'carry_forward_limit',
            'carry_forward_expiry_months', 'encashment_limit', 'probation_period_days',
            'apply_sandwich_rule', 'requires_approval', 'auto_approve_after_days',
            'min_days_before_application', 'max_days_before_application',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate that at least one of department, location, designation, business_unit, or employee_type is provided
        """
        department = data.get('department')
        location = data.get('location')
        designation = data.get('designation')
        business_unit = data.get('business_unit')
        employee_type = data.get('employee_type')
        
        if not any([department, location, designation, business_unit, employee_type]):
            raise serializers.ValidationError(
                "At least one of department, location, designation, business_unit, or employee_type must be provided"
            )
        
        return data
