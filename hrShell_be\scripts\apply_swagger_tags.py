#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to apply Swagger tags to all ViewSets in the HR Shell application.

This script helps automate the process of adding @swagger_auto_schema decorators
to ViewSets that don't already have them.
"""

import os
import re
from pathlib import Path

# Mapping of app directories to their corresponding Swagger tags
APP_TAG_MAPPING = {
    'employees_app': 'Employee Management',
    'organization_app': 'Organization Structure',
    'leave_app': 'Leave Management',
    'payroll_app': 'Payroll Management',
    'recruitment_app': 'Recruitment & Hiring',
    'onboarding_offboarding_app': 'Onboarding & Offboarding',
    'common': 'System'
}

# ViewSets that should have specific tags (overrides app-level mapping)
SPECIFIC_TAG_MAPPING = {
    'LoginView': 'Authentication',
    'RegisterView': 'Authentication',
    'UserInfoView': 'Authentication',
    'LogoutView': 'Authentication',
    'CustomTokenObtainPairView': 'Authentication',
    'VectorChatView': 'AI Assistant',
    'HealthCheckView': 'System'
}

# Detailed mapping for specific view types within apps
DETAILED_TAG_MAPPING = {
    # Employee Management subcategories
    'EmployeeAttendanceViewSet': 'Attendance Management',
    'AttendanceViewSet': 'Attendance Management',

    # Payroll subcategories
    'SalaryViewSet': 'Compensation & Benefits',
    'EmployeeHikeViewSet': 'Compensation & Benefits',
    'BonusViewSet': 'Compensation & Benefits',
    'BonusBatchViewSet': 'Compensation & Benefits',
    'LoanViewSet': 'Compensation & Benefits',
    'LoanInstallmentViewSet': 'Compensation & Benefits',
    'TaxSlabViewSet': 'Compensation & Benefits',
    'EmployeeTaxDeclarationViewSet': 'Compensation & Benefits',

    # Document Management
    'DocumentViewSet': 'Document Management',
    'DocumentTemplateViewSet': 'Document Management',
    'EmployeeDocumentViewSet': 'Document Management',
    'OrganizationDocumentViewSet': 'Document Management',
}

def find_python_files(directory):
    """Find all Python files in views directories."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        if 'views' in root and '__pycache__' not in root:
            for file in files:
                if file.endswith('.py') and file != '__init__.py':
                    python_files.append(os.path.join(root, file))
    return python_files

def has_swagger_import(content):
    """Check if file already has swagger imports."""
    return 'from drf_yasg.utils import swagger_auto_schema' in content

def has_swagger_decorator(content, class_name):
    """Check if a class already has swagger decorator."""
    pattern = rf'@swagger_auto_schema\s*\([^)]*\)\s*class\s+{class_name}'
    return bool(re.search(pattern, content, re.MULTILINE | re.DOTALL))

def get_viewset_classes(content):
    """Extract ViewSet class names from file content."""
    pattern = r'class\s+(\w+(?:ViewSet|View))\s*\([^)]*\):'
    matches = re.findall(pattern, content)
    return matches

def add_swagger_imports(content):
    """Add swagger imports if not present."""
    if has_swagger_import(content):
        return content

    # Find the last import statement
    import_lines = []
    other_lines = []
    in_imports = True

    for line in content.split('\n'):
        if in_imports and (line.startswith('from ') or line.startswith('import ') or line.strip() == ''):
            import_lines.append(line)
        else:
            if line.strip() and in_imports:
                in_imports = False
            other_lines.append(line)

    # Add swagger imports
    swagger_imports = [
        'from drf_yasg.utils import swagger_auto_schema',
        'from drf_yasg import openapi'
    ]

    # Combine imports and other content
    all_imports = import_lines + swagger_imports
    return '\n'.join(all_imports) + '\n' + '\n'.join(other_lines)

def add_swagger_decorator(content, class_name, tag):
    """Add swagger decorator to a class."""
    if has_swagger_decorator(content, class_name):
        return content

    # Find the class definition
    pattern = rf'(class\s+{class_name}\s*\([^)]*\):)'

    decorator = f'''@swagger_auto_schema(
    tags=['{tag}'],
    operation_description="{class_name.replace('ViewSet', '').replace('View', '').lower()} management endpoints"
)
\\1'''

    return re.sub(pattern, decorator, content)

def determine_tag(file_path, class_name):
    """Determine the appropriate tag for a class."""
    # Check specific mapping first
    if class_name in SPECIFIC_TAG_MAPPING:
        return SPECIFIC_TAG_MAPPING[class_name]

    # Check detailed mapping for specific view types
    if class_name in DETAILED_TAG_MAPPING:
        return DETAILED_TAG_MAPPING[class_name]

    # Check app-level mapping
    for app_dir, tag in APP_TAG_MAPPING.items():
        if app_dir in file_path:
            return tag

    return 'System'

def process_file(file_path):
    """Process a single Python file to add swagger decorators."""
    print(f"Processing: {file_path}")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Skip if file doesn't contain ViewSets or Views
        if 'ViewSet' not in content and 'View' not in content:
            return

        # Get ViewSet classes
        viewset_classes = get_viewset_classes(content)
        if not viewset_classes:
            return

        # Add swagger imports
        content = add_swagger_imports(content)

        # Add decorators to each ViewSet
        for class_name in viewset_classes:
            tag = determine_tag(file_path, class_name)
            content = add_swagger_decorator(content, class_name, tag)

        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"  ✅ Updated {len(viewset_classes)} classes: {', '.join(viewset_classes)}")

    except Exception as e:
        print(f"  ❌ Error processing {file_path}: {e}")

def main():
    """Main function to process all files."""
    print("🚀 Starting Swagger tag application...")

    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent

    # Find all Python files in views directories
    python_files = find_python_files(project_root)

    print(f"📁 Found {len(python_files)} Python files in views directories")

    # Process each file
    for file_path in python_files:
        process_file(file_path)

    print("\n✨ Swagger tag application completed!")
    print("\n📋 Next steps:")
    print("1. Review the changes in your files")
    print("2. Test your API documentation at /swagger-ui/")
    print("3. Commit the changes to version control")
    print("4. Update any custom action methods with specific tags if needed")

if __name__ == "__main__":
    main()
