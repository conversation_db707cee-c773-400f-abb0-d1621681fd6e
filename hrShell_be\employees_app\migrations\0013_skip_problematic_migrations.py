# Generated manually to fix migration issues

from django.db import migrations


class Migration(migrations.Migration):
    """
    This migration is created to fix the issue with the sn_departments table.
    The problem is that the sn_departments model is created in migration 0003 with managed=False,
    which means Django won't create the table in the database. However, migration 0005 is trying
    to add a field to this table, which doesn't exist. Then, migration 0007 is trying to delete
    the model, but the table doesn't exist in the database.
    
    This migration will skip the problematic migrations and set the state as if they were applied.
    """

    dependencies = [
        ('employees_app', '0012_employeehike_status_alter_employeehike_approved_by'),
    ]

    operations = [
        migrations.RunSQL(
            sql="SELECT 1;",  # No-op SQL
            reverse_sql="SELECT 1;",  # No-op SQL
            state_operations=[
                # No state operations needed as the problematic models have already been removed
            ]
        ),
    ]
