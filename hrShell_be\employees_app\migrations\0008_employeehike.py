# Generated by Django 5.1.5 on 2025-04-27 15:25

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees_app', '0007_department_employee_department_manager_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeHike',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('hike_percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('hike_effective_date', models.DateField(default=django.utils.timezone.now)),
                ('hike_reason', models.TextField(blank=True, null=True)),
                ('previous_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('new_salary', models.DecimalField(decimal_places=2, max_digits=10)),
                ('approved_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='approved_hikes', to='employees_app.employee')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='employees_app.department')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='employees_app.employee')),
            ],
        ),
    ]
