from django.db import models
from enum import Enum


class LocationStatus(Enum):
    ACTIVE = 'active'
    INACTIVE = 'inactive'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class Location(models.Model):
    """
    Location model for storing branch/office information
    """
    # Basic information
    name = models.CharField(max_length=200)
    organization = models.ForeignKey(
        'organization_app.Organization',
        on_delete=models.CASCADE,
        related_name='locations'
    )
    
    # Address information
    address_line1 = models.CharField(max_length=255)
    address_line2 = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.Char<PERSON>ield(max_length=100)
    postal_code = models.CharField(max_length=20)
    
    # Contact information
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    
    # Geo coordinates
    latitude = models.DecimalField(
        max_digits=9, 
        decimal_places=6, 
        blank=True, 
        null=True,
        help_text="Latitude coordinate for maps"
    )
    longitude = models.DecimalField(
        max_digits=9, 
        decimal_places=6, 
        blank=True, 
        null=True,
        help_text="Longitude coordinate for maps"
    )
    
    # Working hours
    working_hours_start = models.TimeField(blank=True, null=True)
    working_hours_end = models.TimeField(blank=True, null=True)
    working_days = models.CharField(
        max_length=100, 
        blank=True, 
        null=True,
        help_text="Comma-separated list of working days (e.g., 'Mon,Tue,Wed,Thu,Fri')"
    )
    
    # Status
    is_headquarters = models.BooleanField(default=False)
    status = models.CharField(
        max_length=10,
        choices=LocationStatus.choices(),
        default=LocationStatus.ACTIVE.value
    )
    
    # Custom fields
    custom_fields = models.JSONField(default=dict, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = "Location"
        verbose_name_plural = "Locations"
        unique_together = ('organization', 'name')
    
    def __str__(self):
        return f"{self.name} ({self.organization.name})"
