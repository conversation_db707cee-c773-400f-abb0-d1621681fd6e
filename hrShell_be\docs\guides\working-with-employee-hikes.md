# Working with Employee Hikes

This guide explains how to work with employee hikes in the HR Management API.

## Overview

The HR Management API provides endpoints for managing employee salary hikes. You can create, retrieve, update, and delete employee hikes, as well as filter and search for them.

## Prerequisites

Before working with employee hikes, make sure you have:

- Authentication credentials (JWT token)
- Appropriate permissions (admin or staff)
- Existing employees and departments

## Creating an Employee Hike

To create a new employee hike, send a POST request to the `/api/v1/employee-hikes/` endpoint:

```http
POST /api/v1/employee-hikes/
Content-Type: application/json
Authorization: Bearer your_access_token

{
  "employee": 1,
  "hike_percentage": "10.00",
  "hike_effective_date": "2023-05-01",
  "hike_reason": "Annual performance review",
  "department": 1,
  "previous_salary": "50000.00",
  "new_salary": "55000.00",
  "approved_by": 2
}
```

### Required Fields

- `employee`: ID of the employee receiving the hike
- `hike_percentage`: Percentage of the salary hike (decimal)
- `department`: ID of the employee's department
- `previous_salary`: Employee's salary before the hike
- `new_salary`: Employee's salary after the hike
- `approved_by`: ID of the employee who approved the hike

### Optional Fields

- `hike_effective_date`: Date when the hike becomes effective (default: current date)
- `hike_reason`: Reason for the salary hike

### Validation Rules

- Hike percentage must be positive and cannot exceed 100%
- New salary must be greater than previous salary
- The hike percentage must match the difference between the new and previous salary

## Retrieving Employee Hikes

### List All Employee Hikes

To list all employee hikes, send a GET request to the `/api/v1/employee-hikes/` endpoint:

```http
GET /api/v1/employee-hikes/
Authorization: Bearer your_access_token
```

### Get a Specific Employee Hike

To get a specific employee hike, send a GET request to the `/api/v1/employee-hikes/{id}/` endpoint:

```http
GET /api/v1/employee-hikes/1/
Authorization: Bearer your_access_token
```

## Filtering and Searching

You can filter and search for employee hikes using query parameters:

```http
GET /api/v1/employee-hikes/?employee=1&min_percentage=5&max_percentage=15
Authorization: Bearer your_access_token
```

### Available Filters

- `employee`: Filter by employee ID
- `department`: Filter by department ID
- `employee_name`: Filter by employee name
- `department_name`: Filter by department name
- `min_percentage`: Filter by minimum hike percentage
- `max_percentage`: Filter by maximum hike percentage
- `effective_after`: Filter by effective date (format: YYYY-MM-DD)
- `effective_before`: Filter by effective date (format: YYYY-MM-DD)
- `min_new_salary`: Filter by minimum new salary
- `max_new_salary`: Filter by maximum new salary

### Searching

You can search for employee hikes using the `search` parameter:

```http
GET /api/v1/employee-hikes/?search=performance
Authorization: Bearer your_access_token
```

This will search in the employee's name and the hike reason.

### Ordering

You can order the results using the `ordering` parameter:

```http
GET /api/v1/employee-hikes/?ordering=-hike_percentage
Authorization: Bearer your_access_token
```

Use a minus sign (`-`) to indicate descending order.

## Updating an Employee Hike

### Full Update

To update all fields of an employee hike, send a PUT request to the `/api/v1/employee-hikes/{id}/` endpoint:

```http
PUT /api/v1/employee-hikes/1/
Content-Type: application/json
Authorization: Bearer your_access_token

{
  "employee": 1,
  "hike_percentage": "12.00",
  "hike_effective_date": "2023-05-01",
  "hike_reason": "Updated reason",
  "department": 1,
  "previous_salary": "50000.00",
  "new_salary": "56000.00",
  "approved_by": 2
}
```

### Partial Update

To update specific fields of an employee hike, send a PATCH request to the `/api/v1/employee-hikes/{id}/` endpoint:

```http
PATCH /api/v1/employee-hikes/1/
Content-Type: application/json
Authorization: Bearer your_access_token

{
  "hike_percentage": "12.00",
  "new_salary": "56000.00"
}
```

## Deleting an Employee Hike

To delete an employee hike, send a DELETE request to the `/api/v1/employee-hikes/{id}/` endpoint:

```http
DELETE /api/v1/employee-hikes/1/
Authorization: Bearer your_access_token
```

## Best Practices

- Always validate the hike percentage and salary values before submitting
- Include a meaningful hike reason for better record-keeping
- Use the filtering and searching capabilities to find relevant hikes
- Ensure that only authorized personnel can create and approve hikes
