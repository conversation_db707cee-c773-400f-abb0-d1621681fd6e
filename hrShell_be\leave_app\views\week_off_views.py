from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from leave_app.models.week_off import WeekOff, DayOfWeek
from leave_app.serializers.week_off_serializer import WeekOffSerializer
from leave_app.filters.leave_filters import WeekOffFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class WeekOffViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Leave Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for managing week offs
    
    list:
    Return a list of all week offs
    
    create:
    Create a new week off
    
    retrieve:
    Return the given week off
    
    update:
    Update the given week off
    
    partial_update:
    Partially update the given week off
    
    destroy:
    Delete the given week off
    
    Additional actions:
    - organization_week_offs: Get week offs for a specific organization
    - location_week_offs: Get week offs for a specific location
    - department_week_offs: Get week offs for a specific department
    - employee_week_offs: Get week offs for a specific employee
    """
    queryset = WeekOff.objects.all()
    serializer_class = WeekOffSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = WeekOffFilter
    ordering_fields = ['day_of_week']
    ordering = ['day_of_week']
    
    @action(detail=False, methods=['get'], url_path='organization/(?P<organization_id>[^/.]+)')
    def organization_week_offs(self, request, organization_id=None):
        """
        Get week offs for a specific organization
        """
        week_offs = self.queryset.filter(organization_id=organization_id)
        page = self.paginate_queryset(week_offs)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(week_offs, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='location/(?P<location_id>[^/.]+)')
    def location_week_offs(self, request, location_id=None):
        """
        Get week offs for a specific location
        """
        # Get location-specific week offs and organization-wide week offs
        week_offs = self.queryset.filter(
            Q(location_id=location_id) | Q(location__isnull=True)
        )
        page = self.paginate_queryset(week_offs)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(week_offs, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_week_offs(self, request, department_id=None):
        """
        Get week offs for a specific department
        """
        # Get department-specific week offs and organization-wide week offs
        week_offs = self.queryset.filter(
            Q(department_id=department_id) | Q(department__isnull=True)
        )
        page = self.paginate_queryset(week_offs)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(week_offs, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_week_offs(self, request, employee_id=None):
        """
        Get week offs for a specific employee
        """
        from employees_app.models.employee import Employee
        
        try:
            employee = Employee.objects.get(pk=employee_id)
        except Employee.DoesNotExist:
            return Response(
                {"detail": "Employee not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get week offs based on employee's department and location
        department_id = employee.department_id if employee.department else None
        location_id = None
        
        # Try to get location from employee's department's business unit
        if department_id and hasattr(employee.department, 'business_unit') and employee.department.business_unit:
            if hasattr(employee.department.business_unit, 'primary_location') and employee.department.business_unit.primary_location:
                location_id = employee.department.business_unit.primary_location.id
        
        # Get week offs for the employee's department, location, and organization-wide
        week_offs = self.queryset.filter(
            Q(department_id=department_id) | Q(department__isnull=True)
        )
        
        if location_id:
            week_offs = week_offs.filter(
                Q(location_id=location_id) | Q(location__isnull=True)
            )
        
        page = self.paginate_queryset(week_offs)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(week_offs, many=True)
        return Response(serializer.data)
