from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

# Import models when they are created
# from payroll_app.models.employee_salary import EmployeeSalary
# from payroll_app.models.payroll import Payroll, PayrollItem
# from payroll_app.models.loan import Loan, LoanInstallment

# Example signal handlers to be implemented
"""
@receiver(post_save, sender=EmployeeSalary)
def update_employee_salary_history(sender, instance, created, **kwargs):
    if created:
        # Log the new salary assignment
        logger.info(f"New salary assigned to employee {instance.employee.id}: {instance.gross_salary}")
    else:
        # Log the salary update
        logger.info(f"Salary updated for employee {instance.employee.id}: {instance.gross_salary}")

@receiver(post_save, sender=Payroll)
def process_payroll_items(sender, instance, created, **kwargs):
    if created and instance.status == 'draft':
        # Create payroll items for all active employees
        from employees_app.models.employee import Employee
        from payroll_app.models.employee_salary import EmployeeSalary
        
        employees = Employee.objects.filter(status='active')
        for employee in employees:
            try:
                salary = EmployeeSalary.objects.get(employee=employee, is_active=True)
                PayrollItem.objects.create(
                    payroll=instance,
                    employee=employee,
                    basic_salary=salary.basic_salary,
                    gross_salary=salary.gross_salary,
                    status='draft'
                )
            except EmployeeSalary.DoesNotExist:
                logger.warning(f"No active salary found for employee {employee.id}")

@receiver(post_save, sender=LoanInstallment)
def update_loan_balance(sender, instance, created, **kwargs):
    if created:
        loan = instance.loan
        loan.remaining_amount -= instance.amount
        loan.save()
"""
