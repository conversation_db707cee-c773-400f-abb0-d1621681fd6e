from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from payroll_app.models.salary_structure import SalaryStructure, SalaryComponent
from payroll_app.serializers.salary_structure_serializer import SalaryStructureSerializer, SalaryComponentSerializer
from payroll_app.filters.payroll_filters import SalaryStructureFilter, SalaryComponentFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class SalaryComponentViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Payroll Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for salary components.
    """
    queryset = SalaryComponent.objects.all()
    serializer_class = SalaryComponentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = SalaryComponentFilter
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'component_type', 'calculation_type', 'created_at']
    ordering = ['component_type', 'name']
    
    @action(detail=False, methods=['get'])
    def earnings(self, request):
        """
        Get all earning components.
        """
        earnings = SalaryComponent.objects.filter(component_type='earning')
        page = self.paginate_queryset(earnings)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(earnings, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def deductions(self, request):
        """
        Get all deduction components.
        """
        deductions = SalaryComponent.objects.filter(component_type='deduction')
        page = self.paginate_queryset(deductions)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(deductions, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get all active components.
        """
        active = SalaryComponent.objects.filter(is_active=True)
        page = self.paginate_queryset(active)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(active, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def taxable(self, request):
        """
        Get all taxable components.
        """
        taxable = SalaryComponent.objects.filter(is_taxable=True)
        page = self.paginate_queryset(taxable)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(taxable, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all components for a specific organization.
        """
        components = SalaryComponent.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(components)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(components, many=True)
        return Response(serializer.data)


class SalaryStructureViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Payroll Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for salary structures.
    """
    queryset = SalaryStructure.objects.all()
    serializer_class = SalaryStructureSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = SalaryStructureFilter
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'salary_type', 'created_at']
    ordering = ['name']
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get all active salary structures.
        """
        active = SalaryStructure.objects.filter(is_active=True)
        page = self.paginate_queryset(active)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(active, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all salary structures for a specific organization.
        """
        structures = SalaryStructure.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(structures)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(structures, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def components(self, request, pk=None):
        """
        Get all components for a specific salary structure.
        """
        structure = self.get_object()
        components = structure.components.all()
        serializer = SalaryComponentSerializer(components, many=True)
        return Response(serializer.data)
