from django.db import models
from enum import Enum


class HolidayType(Enum):
    NATIONAL = 'national'
    REGIONAL = 'regional'
    RELIGIOUS = 'religious'
    COMPANY = 'company'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class Holiday(models.Model):
    """
    Holiday model for tracking holidays
    """
    # Basic information
    name = models.CharField(max_length=200)
    date = models.DateField()
    description = models.TextField(blank=True, null=True)
    
    # Associations
    organization = models.ForeignKey(
        'organization_app.Organization',
        on_delete=models.CASCADE,
        related_name='holidays'
    )
    
    # Specific location (optional)
    location = models.ForeignKey(
        'organization_app.Location',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='holidays'
    )
    
    # Holiday type
    holiday_type = models.CharField(
        max_length=20,
        choices=HolidayType.choices(),
        default=HolidayType.COMPANY.value
    )
    
    # Recurring holiday
    is_recurring = models.BooleanField(
        default=False,
        help_text="Whether this holiday recurs every year on the same date"
    )
    
    # Half day
    is_half_day = models.BooleanField(
        default=False,
        help_text="Whether this is a half-day holiday"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['date']
        verbose_name = "Holiday"
        verbose_name_plural = "Holidays"
    
    def __str__(self):
        location_name = self.location.name if self.location else "All Locations"
        return f"{self.name} - {self.date} ({location_name})"
