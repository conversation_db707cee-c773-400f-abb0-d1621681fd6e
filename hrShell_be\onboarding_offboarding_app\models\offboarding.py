from django.db import models
from django.utils import timezone
from employees_app.models.employee import Employee
from organization_app.models import Department


class OffboardingRequest(models.Model):
    """
    Model to represent offboarding requests for employees leaving the company.
    """
    STATUS_CHOICES = [
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('rejected', 'Rejected'),
    ]
    
    EXIT_TYPE_CHOICES = [
        ('resignation', 'Resignation'),
        ('termination', 'Termination'),
        ('retirement', 'Retirement'),
        ('end_of_contract', 'End of Contract'),
        ('layoff', 'Layoff'),
        ('other', 'Other'),
    ]
    
    # Basic Information
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='offboarding_requests')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='submitted')
    exit_type = models.Char<PERSON><PERSON>(max_length=20, choices=EXIT_TYPE_CHOICES, default='resignation')
    
    # Dates
    resignation_date = models.DateField()
    last_working_day = models.DateField()
    notice_period_days = models.PositiveIntegerField(default=30)
    
    # Stakeholders
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, related_name='offboarding_requests')
    manager = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_offboarding_requests')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_offboarding_requests')
    approval_date = models.DateField(null=True, blank=True)
    
    # Reason for Leaving
    exit_reason = models.TextField()
    exit_reason_category = models.CharField(max_length=50, blank=True, null=True)
    
    # Additional Information
    rehire_eligible = models.BooleanField(default=True)
    exit_interview_scheduled = models.BooleanField(default=False)
    exit_interview_date = models.DateTimeField(null=True, blank=True)
    
    # Final Settlement
    final_settlement_processed = models.BooleanField(default=False)
    final_settlement_date = models.DateField(null=True, blank=True)
    final_settlement_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    
    # Notes
    notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-resignation_date']
        verbose_name = 'Offboarding Request'
        verbose_name_plural = 'Offboarding Requests'
    
    def __str__(self):
        return f"Offboarding Request for {self.employee}"
    
    def save(self, *args, **kwargs):
        # Calculate notice period days if not provided
        if not self.notice_period_days:
            delta = self.last_working_day - self.resignation_date
            self.notice_period_days = delta.days
        
        # Set approval date if status is changed to approved
        if self.status == 'approved' and not self.approval_date and self.approved_by:
            self.approval_date = timezone.now().date()
        
        super().save(*args, **kwargs)


class OffboardingTask(models.Model):
    """
    Model to represent tasks to be completed during offboarding.
    """
    STATUS_CHOICES = [
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('not_applicable', 'Not Applicable'),
    ]
    
    CATEGORY_CHOICES = [
        ('hr', 'HR'),
        ('it', 'IT'),
        ('admin', 'Admin'),
        ('finance', 'Finance'),
        ('department', 'Department'),
        ('knowledge_transfer', 'Knowledge Transfer'),
        ('exit_interview', 'Exit Interview'),
        ('other', 'Other'),
    ]
    
    # Basic Information
    request = models.ForeignKey(OffboardingRequest, on_delete=models.CASCADE, related_name='tasks')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='other')
    
    # Task Details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_started')
    due_date = models.DateField(null=True, blank=True)
    completed_date = models.DateField(null=True, blank=True)
    sequence = models.PositiveIntegerField(default=1)
    is_mandatory = models.BooleanField(default=True)
    
    # Assignment
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_offboarding_tasks')
    assigned_to_role = models.CharField(max_length=50, blank=True, null=True)
    
    # Completion Information
    completed_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='completed_offboarding_tasks')
    
    # Document
    document = models.FileField(upload_to='offboarding_documents/%Y/%m/', blank=True, null=True)
    
    # Notes
    notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['sequence', 'due_date']
        verbose_name = 'Offboarding Task'
        verbose_name_plural = 'Offboarding Tasks'
    
    def __str__(self):
        return f"{self.name} for {self.request.employee}"
    
    def save(self, *args, **kwargs):
        # Set due date to last working day if not provided
        if not self.due_date:
            self.due_date = self.request.last_working_day
        
        # Update status to completed if completed_date is set
        if self.completed_date and self.status != 'completed':
            self.status = 'completed'
        
        # Update completed_date if status is set to completed
        if self.status == 'completed' and not self.completed_date:
            self.completed_date = timezone.now().date()
        
        super().save(*args, **kwargs)
