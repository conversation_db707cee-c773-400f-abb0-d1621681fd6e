from rest_framework import serializers
from recruitment_app.models.offer import Offer
from recruitment_app.serializers.application_serializer import ApplicationSerializer
from employees_app.serializers.employee_serializer import EmployeeSerializer


class OfferSerializer(serializers.ModelSerializer):
    """
    Serializer for the Offer model.
    """
    application_details = ApplicationSerializer(source='application', read_only=True)
    approved_by_details = EmployeeSerializer(source='approved_by', read_only=True)
    withdrawn_by_details = EmployeeSerializer(source='withdrawn_by', read_only=True)
    
    class Meta:
        model = Offer
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate the offer data.
        """
        # Validate offer and expiry dates
        offer_date = data.get('offer_date')
        expiry_date = data.get('expiry_date')
        
        if offer_date and expiry_date and offer_date > expiry_date:
            raise serializers.ValidationError("Offer date cannot be later than expiry date.")
        
        # Validate joining date
        joining_date = data.get('joining_date')
        
        if offer_date and joining_date and offer_date > joining_date:
            raise serializers.ValidationError("Offer date cannot be later than joining date.")
        
        return data
    
    def create(self, validated_data):
        """
        Create a new offer and update the application status.
        """
        offer = Offer.objects.create(**validated_data)
        
        # Update the application status
        application = offer.application
        application.status = 'offer'
        application.current_stage = 'offer_stage'
        application.save()
        
        return offer
    
    def update(self, instance, validated_data):
        """
        Update an offer and handle status changes.
        """
        old_status = instance.status
        
        # Update the offer
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        # Handle status changes
        if instance.status != old_status:
            # Update application status based on offer status
            application = instance.application
            
            if instance.status == 'accepted':
                application.status = 'hired'
                application.current_stage = 'onboarding'
            elif instance.status == 'declined':
                application.status = 'rejected'
                application.rejection_reason = instance.decline_reason
            elif instance.status == 'withdrawn':
                application.status = 'rejected'
                application.rejection_reason = instance.withdrawal_reason
            
            application.save()
        
        instance.save()
        return instance
