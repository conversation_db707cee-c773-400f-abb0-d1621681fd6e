# Organization Module Database Schema

This document provides a visual representation of the organization module's database schema in a style similar to ChartDB.

## Database Schema Diagram

```mermaid
erDiagram
    %% Define entities with their fields
    
    "os_organization" {
        bigint id PK
        varchar name
        varchar logo
        varchar registration_number
        varchar gst_number
        varchar pan_number
        varchar ein_number
        varchar business_type
        date establishment_date
        varchar industry_sector
        varchar default_currency
        varchar default_timezone
        int fiscal_year_start_month
        int fiscal_year_start_day
        varchar website
        varchar email
        varchar phone
        varchar status
        jsonb custom_fields
        timestamp created_at
        timestamp updated_at
    }
    
    "os_location" {
        bigint id PK
        bigint organization_id FK
        varchar name
        varchar address_line1
        varchar address_line2
        varchar city
        varchar state
        varchar country
        varchar postal_code
        varchar phone
        varchar email
        decimal latitude
        decimal longitude
        time working_hours_start
        time working_hours_end
        varchar working_days
        boolean is_headquarters
        varchar status
        jsonb custom_fields
        timestamp created_at
        timestamp updated_at
    }
    
    "os_business_unit" {
        bigint id PK
        bigint organization_id FK
        varchar name
        text description
        varchar code
        bigint parent_id FK
        bigint head_id FK
        bigint primary_location_id FK
        varchar status
        timestamp created_at
        timestamp updated_at
    }
    
    "os_designation" {
        bigint id PK
        bigint organization_id FK
        varchar title
        text description
        varchar level
        bigint department_id FK
        varchar pay_grade
        decimal min_salary
        decimal max_salary
        timestamp created_at
        timestamp updated_at
    }
    
    "os_organization_policy" {
        bigint id PK
        bigint organization_id FK
        varchar name
        text description
        varchar policy_type
        text content
        boolean applicable_to_all
        date effective_from
        date effective_to
        timestamp created_at
        timestamp updated_at
    }
    
    "os_organization_document" {
        bigint id PK
        bigint organization_id FK
        varchar title
        text description
        varchar document_type
        varchar file
        varchar version
        date effective_date
        date expiry_date
        bigint business_unit_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    "os_policy_location" {
        bigint id PK
        bigint policy_id FK
        bigint location_id FK
    }
    
    "os_policy_department" {
        bigint id PK
        bigint policy_id FK
        bigint department_id FK
    }
    
    "os_policy_business_unit" {
        bigint id PK
        bigint policy_id FK
        bigint business_unit_id FK
    }
    
    "em_department" {
        bigint id PK
        varchar name
        text description
        bigint manager_id FK
        varchar status
        bigint business_unit_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    "em_employee" {
        bigint id PK
        varchar first_name
        varchar last_name
        varchar email
        varchar phone
        varchar position
        bigint department_id FK
        date hire_date
        varchar gender
        varchar status
        varchar profile_picture
        varchar resume
        varchar contract
        timestamp created_at
        timestamp updated_at
    }
    
    %% Define relationships
    "os_organization" ||--o{ "os_location" : "has"
    "os_organization" ||--o{ "os_business_unit" : "has"
    "os_organization" ||--o{ "os_designation" : "has"
    "os_organization" ||--o{ "os_organization_policy" : "has"
    "os_organization" ||--o{ "os_organization_document" : "has"
    
    "os_location" }o--o{ "os_organization_policy" : "associated via os_policy_location"
    "em_department" }o--o{ "os_organization_policy" : "associated via os_policy_department"
    "os_business_unit" }o--o{ "os_organization_policy" : "associated via os_policy_business_unit"
    
    "os_business_unit" ||--o{ "os_business_unit" : "parent-child"
    "os_business_unit" }o--|| "os_location" : "has primary"
    "os_business_unit" }o--|| "em_employee" : "headed by"
    
    "em_department" }o--|| "os_business_unit" : "belongs to"
    "em_department" ||--o{ "os_designation" : "has"
    
    "em_employee" }o--|| "em_department" : "belongs to"
    
    "os_organization_document" }o--|| "os_business_unit" : "related to"
    
    "os_organization_policy" ||--o{ "os_policy_location" : "has"
    "os_location" ||--o{ "os_policy_location" : "belongs to"
    
    "os_organization_policy" ||--o{ "os_policy_department" : "has"
    "em_department" ||--o{ "os_policy_department" : "belongs to"
    
    "os_organization_policy" ||--o{ "os_policy_business_unit" : "has"
    "os_business_unit" ||--o{ "os_policy_business_unit" : "belongs to"
```

## Table Descriptions

### Organization Tables

1. **os_organization**: Stores company information including name, registration details, business type, fiscal settings, etc.
2. **os_location**: Stores branch/office information including address, contact details, working hours, etc.
3. **os_business_unit**: Stores division/business unit information with hierarchical structure.
4. **os_designation**: Stores job titles/roles with level and salary range information.
5. **os_organization_policy**: Stores company policies with type, content, and effective dates.
6. **os_organization_document**: Stores company documents with type, version, and effective dates.

### Junction Tables

1. **os_policy_location**: Junction table linking policies to specific locations.
2. **os_policy_department**: Junction table linking policies to specific departments.
3. **os_policy_business_unit**: Junction table linking policies to specific business units.

### Related Employee Tables

1. **em_department**: Stores department information, can be linked to business units.
2. **em_employee**: Stores employee information, linked to departments.

## Key Relationships

- An organization has many locations, business units, designations, policies, and documents.
- Business units can have a hierarchical structure (parent-child relationship).
- Departments belong to business units and have many employees.
- Policies can be associated with specific locations, departments, and business units through junction tables.
- Employees belong to departments and can head business units.
- Documents can be related to specific business units.

This schema provides a comprehensive structure for managing organizational hierarchy, locations, designations, policies, and documents.
