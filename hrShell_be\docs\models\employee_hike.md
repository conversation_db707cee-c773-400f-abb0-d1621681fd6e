# EmployeeHike Model

The EmployeeHike model represents a salary hike for an employee in the organization.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `employee` | ForeignKey | The employee receiving the hike | Yes |
| `hike_percentage` | Decimal | Percentage of the salary hike | Yes |
| `hike_effective_date` | Date | Date when the hike becomes effective | Yes (default: current date) |
| `hike_reason` | Text | Reason for the salary hike | No |
| `department` | ForeignKey | Department of the employee | Yes |
| `previous_salary` | Decimal | Employee's salary before the hike | Yes |
| `new_salary` | Decimal | Employee's salary after the hike | Yes |
| `approved_by` | ForeignKey | Employee who approved the hike | Yes |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `employee` | Employee | The employee receiving the hike |
| `department` | Department | The department of the employee |
| `approved_by` | Employee | The employee who approved the hike |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns a string representation of the hike |

## Example

```python
hike = EmployeeHike.objects.create(
    employee=john_doe,
    hike_percentage=10.00,
    hike_reason="Annual performance review",
    department=engineering_department,
    previous_salary=50000.00,
    new_salary=55000.00,
    approved_by=jane_smith
)

print(hike)  # Output: "John Doe - 10.00% hike on 2023-05-01"
```

## Validation Rules

- Hike percentage must be positive and cannot exceed 100%
- New salary must be greater than previous salary
- The hike percentage must match the difference between the new and previous salary
