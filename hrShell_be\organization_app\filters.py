import django_filters
from organization_app.models.organization import Organization
from organization_app.models.location import Location
from organization_app.models.designation import Designation
from organization_app.models.business_unit import BusinessUnit
from organization_app.models.organization_policy import OrganizationPolicy
from organization_app.models.organization_document import OrganizationDocument


class OrganizationFilter(django_filters.FilterSet):
    """
    Filter for Organization model
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    industry_sector = django_filters.CharFilter(lookup_expr='icontains')
    business_type = django_filters.CharFilter()
    status = django_filters.CharFilter()
    establishment_date_from = django_filters.DateFilter(field_name='establishment_date', lookup_expr='gte')
    establishment_date_to = django_filters.DateFilter(field_name='establishment_date', lookup_expr='lte')
    
    class Meta:
        model = Organization
        fields = [
            'name', 'industry_sector', 'business_type', 'status',
            'establishment_date_from', 'establishment_date_to'
        ]


class LocationFilter(django_filters.FilterSet):
    """
    Filter for Location model
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    city = django_filters.CharFilter(lookup_expr='icontains')
    state = django_filters.CharFilter(lookup_expr='icontains')
    country = django_filters.CharFilter(lookup_expr='icontains')
    status = django_filters.CharFilter()
    is_headquarters = django_filters.BooleanFilter()
    
    class Meta:
        model = Location
        fields = [
            'name', 'organization', 'city', 'state', 'country',
            'status', 'is_headquarters'
        ]


class DesignationFilter(django_filters.FilterSet):
    """
    Filter for Designation model
    """
    title = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    department = django_filters.NumberFilter()
    level = django_filters.CharFilter()
    
    class Meta:
        model = Designation
        fields = [
            'title', 'organization', 'department', 'level'
        ]


class BusinessUnitFilter(django_filters.FilterSet):
    """
    Filter for BusinessUnit model
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    parent = django_filters.NumberFilter()
    head = django_filters.NumberFilter()
    primary_location = django_filters.NumberFilter()
    status = django_filters.CharFilter()
    
    class Meta:
        model = BusinessUnit
        fields = [
            'name', 'organization', 'parent', 'head',
            'primary_location', 'status'
        ]


class OrganizationPolicyFilter(django_filters.FilterSet):
    """
    Filter for OrganizationPolicy model
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    policy_type = django_filters.CharFilter()
    applicable_to_all = django_filters.BooleanFilter()
    locations = django_filters.NumberFilter()
    departments = django_filters.NumberFilter()
    business_units = django_filters.NumberFilter()
    effective_from = django_filters.DateFilter(lookup_expr='gte')
    effective_to = django_filters.DateFilter(lookup_expr='lte')
    
    class Meta:
        model = OrganizationPolicy
        fields = [
            'name', 'organization', 'policy_type', 'applicable_to_all',
            'locations', 'departments', 'business_units',
            'effective_from', 'effective_to'
        ]


class OrganizationDocumentFilter(django_filters.FilterSet):
    """
    Filter for OrganizationDocument model
    """
    title = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    document_type = django_filters.CharFilter()
    business_unit = django_filters.NumberFilter()
    effective_date_from = django_filters.DateFilter(field_name='effective_date', lookup_expr='gte')
    effective_date_to = django_filters.DateFilter(field_name='effective_date', lookup_expr='lte')
    expiry_date_from = django_filters.DateFilter(field_name='expiry_date', lookup_expr='gte')
    expiry_date_to = django_filters.DateFilter(field_name='expiry_date', lookup_expr='lte')
    
    class Meta:
        model = OrganizationDocument
        fields = [
            'title', 'organization', 'document_type', 'business_unit',
            'effective_date_from', 'effective_date_to',
            'expiry_date_from', 'expiry_date_to'
        ]
