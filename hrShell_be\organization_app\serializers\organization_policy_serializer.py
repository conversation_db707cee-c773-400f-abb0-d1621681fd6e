from rest_framework import serializers
from organization_app.models.organization_policy import OrganizationPolicy, PolicyType


class OrganizationPolicySerializer(serializers.ModelSerializer):
    """
    Serializer for the OrganizationPolicy model
    """
    organization_name = serializers.ReadOnlyField(source='organization.name')
    policy_type_display = serializers.ReadOnlyField(source='get_policy_type_display')
    location_names = serializers.SerializerMethodField()
    department_names = serializers.SerializerMethodField()
    business_unit_names = serializers.SerializerMethodField()
    
    class Meta:
        model = OrganizationPolicy
        fields = [
            'id', 'name', 'organization', 'organization_name', 'description',
            'policy_type', 'policy_type_display', 'content', 'applicable_to_all',
            'locations', 'location_names', 'departments', 'department_names',
            'business_units', 'business_unit_names', 'effective_from',
            'effective_to', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_location_names(self, obj):
        return [location.name for location in obj.locations.all()]
    
    def get_department_names(self, obj):
        return [department.name for department in obj.departments.all()]
    
    def get_business_unit_names(self, obj):
        return [business_unit.name for business_unit in obj.business_units.all()]
    
    def validate_policy_type(self, value):
        """
        Validate that the policy type is one of the allowed choices
        """
        try:
            PolicyType(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError(
                f"Policy type must be one of: {', '.join([pt.value for pt in PolicyType])}"
            )
    
    def validate(self, data):
        """
        Validate that the policy name is unique within the organization and policy type
        """
        organization = data.get('organization')
        name = data.get('name')
        policy_type = data.get('policy_type')
        
        # Skip validation if organization, name, or policy_type is not provided
        if not organization or not name or not policy_type:
            return data
        
        # Check if we're updating an existing instance
        instance = getattr(self, 'instance', None)
        if instance and instance.organization == organization and instance.name == name and instance.policy_type == policy_type:
            return data
        
        # Check if a policy with the same name and type already exists in the organization
        if OrganizationPolicy.objects.filter(
            organization=organization, 
            name=name,
            policy_type=policy_type
        ).exists():
            raise serializers.ValidationError(
                {"name": "A policy with this name and type already exists in the organization."}
            )
        
        return data
