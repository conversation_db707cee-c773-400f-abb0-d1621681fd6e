# Generated by Django 5.2.1 on 2025-05-25 09:48

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees_app', '0013_skip_problematic_migrations'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='employeedocument',
            options={'ordering': ['-created_at'], 'verbose_name': 'Employee Document', 'verbose_name_plural': 'Employee Documents'},
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='description',
            field=models.TextField(blank=True, help_text='Description of the document', null=True),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='document_name',
            field=models.CharField(blank=True, help_text='Name or title of the document', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='document_type',
            field=models.CharField(choices=[('resume', 'Resume'), ('contract', 'Contract'), ('offer_letter', 'Offer Letter'), ('experience_certificate', 'Experience Certificate'), ('qualification_certificate', 'Qualification Certificate'), ('id_proof', 'ID Proof'), ('address_proof', 'Address Proof'), ('bank_details', 'Bank Details'), ('other', 'Other')], default='other', help_text='Type of document being uploaded', max_length=50),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='file',
            field=models.FileField(blank=True, help_text='Document file', null=True, upload_to='employee_documents/%Y/%m/'),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='is_verified',
            field=models.BooleanField(default=False, help_text='Whether the document has been verified'),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='employeedocument',
            name='upload_date',
            field=models.DateField(auto_now_add=True, default=django.utils.timezone.now, help_text='Date when document was uploaded'),
            preserve_default=False,
        ),
    ]
