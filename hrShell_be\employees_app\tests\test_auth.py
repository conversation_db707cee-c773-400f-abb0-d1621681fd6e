from django.urls import reverse
from django.contrib.auth.models import User
from rest_framework.test import APITestCase
from rest_framework import status


class AuthenticationTests(APITestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # URL for obtaining token
        self.token_url = reverse('token_obtain_pair')
        
        # URL for user info
        self.user_info_url = reverse('employees:user-info')
        
        # URL for logout
        self.logout_url = reverse('employees:logout')

    def test_obtain_token(self):
        """
        Test that we can obtain a token with valid credentials.
        """
        data = {
            'username': 'testuser',
            'password': 'testpassword'
        }
        response = self.client.post(self.token_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user_id', response.data)
        self.assertIn('username', response.data)
        self.assertIn('email', response.data)
        
    def test_obtain_token_invalid_credentials(self):
        """
        Test that we cannot obtain a token with invalid credentials.
        """
        data = {
            'username': 'testuser',
            'password': 'wrongpassword'
        }
        response = self.client.post(self.token_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
    def test_access_protected_view_with_token(self):
        """
        Test that we can access a protected view with a valid token.
        """
        # First, obtain a token
        data = {
            'username': 'testuser',
            'password': 'testpassword'
        }
        response = self.client.post(self.token_url, data, format='json')
        token = response.data['access']
        
        # Then, use the token to access a protected view
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.get(self.user_info_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], 'testuser')
        self.assertEqual(response.data['email'], '<EMAIL>')
        
    def test_access_protected_view_without_token(self):
        """
        Test that we cannot access a protected view without a token.
        """
        response = self.client.get(self.user_info_url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
    def test_logout(self):
        """
        Test that we can logout by blacklisting the refresh token.
        """
        # First, obtain a token
        data = {
            'username': 'testuser',
            'password': 'testpassword'
        }
        response = self.client.post(self.token_url, data, format='json')
        access_token = response.data['access']
        refresh_token = response.data['refresh']
        
        # Then, use the access token to logout
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        response = self.client.post(self.logout_url, {'refresh': refresh_token}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['detail'], 'Successfully logged out.')
