from rest_framework import serializers
from recruitment_app.models.job_requisition import JobRequisition
from organization_app.serializers.organization_serializer import OrganizationSerializer
from organization_app.serializers.business_unit_serializer import BusinessUnitSerializer
from organization_app.serializers.department_serializer import DepartmentSerializer
from organization_app.serializers.designation_serializer import DesignationSerializer
from organization_app.serializers.location_serializer import LocationSerializer
from employees_app.serializers.employee_serializer import EmployeeSerializer


class JobRequisitionSerializer(serializers.ModelSerializer):
    """
    Serializer for the JobRequisition model.
    """
    organization_details = OrganizationSerializer(source='organization', read_only=True)
    business_unit_details = BusinessUnitSerializer(source='business_unit', read_only=True)
    department_details = DepartmentSerializer(source='department', read_only=True)
    designation_details = DesignationSerializer(source='designation', read_only=True)
    location_details = LocationSerializer(source='location', read_only=True)
    requested_by_details = EmployeeSerializer(source='requested_by', read_only=True)
    approved_by_details = EmployeeSerializer(source='approved_by', read_only=True)
    replacing_employee_details = EmployeeSerializer(source='replacing_employee', read_only=True)
    
    class Meta:
        model = JobRequisition
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate the job requisition data.
        """
        # Validate min and max experience
        min_experience = data.get('min_experience', 0)
        max_experience = data.get('max_experience')
        
        if max_experience is not None and min_experience > max_experience:
            raise serializers.ValidationError("Minimum experience cannot be greater than maximum experience.")
        
        # Validate min and max salary
        min_salary = data.get('min_salary')
        max_salary = data.get('max_salary')
        
        if min_salary is not None and max_salary is not None and min_salary > max_salary:
            raise serializers.ValidationError("Minimum salary cannot be greater than maximum salary.")
        
        # Validate target hiring date
        target_hiring_date = data.get('target_hiring_date')
        
        if target_hiring_date and target_hiring_date < data.get('requested_date', None):
            raise serializers.ValidationError("Target hiring date cannot be earlier than the requested date.")
        
        return data
