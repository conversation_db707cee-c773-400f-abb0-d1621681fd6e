# Augment Guidelines for HR Shell

This document provides guidelines for developers working on the HR Shell project. It covers documentation requirements, integration steps, best practices, and workflow recommendations.

## Table of Contents

1. [Documentation Requirements](#documentation-requirements)
2. [API Documentation](#api-documentation)
3. [Integration Steps](#integration-steps)
4. [Best Practices](#best-practices)
5. [Workflow Recommendations](#workflow-recommendations)
6. [Module-Specific Guidelines](#module-specific-guidelines)

## Documentation Requirements

### Feature Documentation

When completing a feature, ensure the following documentation is updated:

1. **API Documentation**:
   - Update Swagger documentation for any new or modified endpoints
   - Include sample request/response payloads
   - Group endpoints logically by functionality
   - Add detailed descriptions for parameters and responses

2. **README Updates**:
   - Add the feature to the appropriate section in the README
   - Include a brief description of the feature
   - Mention any configuration requirements

3. **Module Documentation**:
   - Update or create module-specific documentation in the `docs/` directory
   - Document the module's purpose, components, and relationships
   - Include database schema information if applicable

4. **Code Documentation**:
   - Add docstrings to all new classes and methods
   - Document parameters, return values, and exceptions
   - Include usage examples for complex functionality

### Documentation Format

- Use Markdown for all documentation files
- Follow a consistent style throughout the documentation
- Include code examples where appropriate
- Use diagrams for complex relationships or workflows

## API Documentation

### Swagger Documentation

The project uses Swagger for API documentation. When adding or modifying endpoints:

1. **Update Swagger Schema**:
   - Add the endpoint to the appropriate tag group in `common/swagger.py`
   - Include detailed descriptions for the endpoint and its parameters
   - Add sample request and response payloads

2. **Test Documentation**:
   - Verify that the endpoint appears correctly in the Swagger UI
   - Test the sample payloads to ensure they are valid
   - Check that the endpoint is grouped correctly

### Accessing Swagger Documentation

- Swagger UI: [http://localhost:8000/swagger-ui/](http://localhost:8000/swagger-ui/)
- ReDoc: [http://localhost:8000/redoc/](http://localhost:8000/redoc/)
- OpenAPI Schema: [http://localhost:8000/api/schema/](http://localhost:8000/api/schema/)

## Integration Steps

### Adding a New Module

When adding a new module to the project:

1. **Create the Module Structure**:
   ```bash
   mkdir -p hrShell_be/new_module_app/{migrations,templates,static,tests}
   touch hrShell_be/new_module_app/__init__.py
   touch hrShell_be/new_module_app/admin.py
   touch hrShell_be/new_module_app/apps.py
   touch hrShell_be/new_module_app/models.py
   touch hrShell_be/new_module_app/serializers.py
   touch hrShell_be/new_module_app/urls.py
   touch hrShell_be/new_module_app/views.py
   ```

2. **Register the Module**:
   - Add the module to `INSTALLED_APPS` in `settings.py`
   - Include the module's URLs in the main URL configuration

3. **Create Database Migrations**:
   ```bash
   python manage.py makemigrations new_module_app
   python manage.py migrate
   ```

4. **Update API Documentation**:
   - Add the module's endpoints to the Swagger documentation
   - Group the endpoints under an appropriate tag

5. **Create Module Documentation**:
   - Create a markdown file in the `docs/` directory
   - Document the module's purpose, components, and relationships

### Integrating with Existing Modules

When integrating a new module with existing modules:

1. **Identify Dependencies**:
   - Determine which existing modules the new module depends on
   - Identify any circular dependencies and resolve them

2. **Update Models**:
   - Add foreign keys or other relationships to connect the modules
   - Create migrations for the relationship changes

3. **Update API Endpoints**:
   - Add endpoints for retrieving related data
   - Update existing endpoints to include related data if necessary

4. **Update Documentation**:
   - Document the relationships between modules
   - Update the API documentation to reflect the new relationships

## Best Practices

### Code Quality

- Follow PEP 8 style guidelines for Python code
- Use meaningful variable and function names
- Keep functions and methods small and focused
- Write unit tests for all new functionality

### API Design

- Use RESTful principles for API design
- Group related endpoints under the same resource
- Use consistent naming conventions for endpoints
- Include pagination for list endpoints
- Implement proper error handling and validation

### Database

- Use Django's ORM for database operations
- Create indexes for frequently queried fields
- Use migrations for all database changes
- Avoid raw SQL queries unless absolutely necessary

### Security

- Validate all user input
- Use Django's built-in security features
- Implement proper authentication and authorization
- Follow the principle of least privilege

## Workflow Recommendations

### Development Workflow

1. **Create a Feature Branch**:
   ```bash
   git checkout -b feature/new-feature
   ```

2. **Implement the Feature**:
   - Write tests first (TDD approach)
   - Implement the feature
   - Ensure all tests pass

3. **Update Documentation**:
   - Update API documentation
   - Update module documentation
   - Add code documentation

4. **Create a Pull Request**:
   - Provide a detailed description of the changes
   - Reference any related issues
   - Request reviews from appropriate team members

5. **Address Review Comments**:
   - Make requested changes
   - Update documentation if necessary
   - Ensure all tests still pass

6. **Merge the Pull Request**:
   - Squash commits if necessary
   - Merge into the main branch
   - Delete the feature branch

### Release Workflow

1. **Create a Release Branch**:
   ```bash
   git checkout -b release/v1.0.0
   ```

2. **Finalize Documentation**:
   - Ensure all documentation is up to date
   - Update the changelog
   - Update the version number

3. **Run Final Tests**:
   - Run all tests to ensure everything works
   - Fix any issues found

4. **Create a Release**:
   - Tag the release
   - Merge into the main branch
   - Deploy to production

## Module-Specific Guidelines

### Authentication

- **Enhanced Login Endpoint**: Use `/api/auth/login/` for new implementations
- **Comprehensive Documentation**: Include detailed Swagger documentation with examples
- **Security Logging**: Log all authentication attempts for security monitoring
- **Error Handling**: Provide clear, user-friendly error messages
- **Token Management**: Implement proper JWT token lifecycle management
- **Backward Compatibility**: Maintain legacy endpoints for existing integrations

### Employee Management

- Include validation for employee data
- Implement proper error handling for employee operations
- Document the employee lifecycle

### Organization Structure

- Document the organization hierarchy
- Implement proper validation for organizational relationships
- Include endpoints for retrieving organizational structure

### Leave Management

- Document leave policies and workflows
- Implement proper validation for leave requests
- Include endpoints for leave approval workflows

### Payroll Management

- Document payroll calculation methods
- Implement proper validation for payroll data
- Include endpoints for payroll processing and reporting

### Recruitment & Hiring

- Document recruitment workflows
- Implement proper validation for candidate data
- Include endpoints for application tracking and interview scheduling

### Onboarding & Offboarding

- Document onboarding and offboarding workflows
- Implement proper validation for onboarding and offboarding data
- Include endpoints for tracking onboarding and offboarding progress

## Recent Integrations

### Enhanced Login Endpoint (Latest)

**Endpoint**: `POST /api/auth/login/`

**Features Implemented**:
- ✅ Comprehensive Swagger documentation with examples
- ✅ Enhanced error handling and validation
- ✅ Security logging for authentication attempts
- ✅ User information in response payload
- ✅ JWT token management with custom claims
- ✅ Backward compatibility with legacy endpoints
- ✅ Comprehensive test coverage
- ✅ Detailed API documentation

**Files Modified**:
- `employees_app/serializers/token_serializer.py` - Enhanced login serializer
- `employees_app/views/auth_views.py` - New LoginView with Swagger docs
- `common/urls.py` - Added new authentication routes
- `employees_app/tests/test_login.py` - Comprehensive test suite
- `docs/api/authentication.md` - Detailed API documentation

**Usage**:
```bash
# Test the new login endpoint
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'
```

**Documentation**: See `docs/api/authentication.md` for complete API documentation.

---

By following these guidelines, we can ensure that the HR Shell project maintains high-quality code, comprehensive documentation, and a consistent development workflow.
