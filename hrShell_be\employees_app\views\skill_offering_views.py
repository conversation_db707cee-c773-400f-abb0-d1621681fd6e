from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Avg, Q
from django_filters.rest_framework import DjangoFilterBackend
from employees_app.models.skill_offering import SkillOffering, SkillLevel, SkillType
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from employees_app.serializers.skill_offering_serializer import SkillOfferingSerializer
from employees_app.filters import SkillOfferingFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class SkillOfferingViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Employee Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing SkillOffering instances.

    Additional actions:
    - employee_skills: Get skills for a specific employee
    - department_skills: Get skills for employees in a specific department
    - skill_statistics: Get statistics about skills
    - skill_search: Search for employees with specific skills
    - skill_matrix: Get a skill matrix for the organization
    """
    queryset = SkillOffering.objects.all()
    serializer_class = SkillOfferingSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = SkillOfferingFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'skill_name']
    ordering_fields = ['skill_name', 'years_of_experience', 'skill_level']

    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_skills(self, request, employee_id=None):
        """
        Get skills for a specific employee
        """
        try:
            employee = Employee.objects.get(pk=employee_id)
        except Employee.DoesNotExist:
            return Response(
                {"detail": "Employee not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        skills = self.queryset.filter(employee=employee)
        page = self.paginate_queryset(skills)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(skills, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_skills(self, request, department_id=None):
        """
        Get skills for employees in a specific department
        """
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return Response(
                {"detail": "Department not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all employees in the department
        employees = Employee.objects.filter(department=department)

        # Get skills for these employees
        skills = self.queryset.filter(employee__in=employees)
        page = self.paginate_queryset(skills)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(skills, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def skill_statistics(self, request):
        """
        Get statistics about skills
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)
        skill_type = request.query_params.get('skill_type', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Filter by skill type if provided
        if skill_type:
            if skill_type not in dict(SkillType.choices()):
                return Response(
                    {"detail": "Invalid skill type"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            queryset = queryset.filter(actual_skill_type=skill_type)

        # Calculate statistics
        total_skills = queryset.count()
        unique_skills = queryset.values('skill_name').distinct().count()
        avg_experience = queryset.aggregate(Avg('years_of_experience'))['years_of_experience__avg'] or 0

        # Count skills by level
        skill_levels = queryset.values('skill_level').annotate(count=Count('id'))

        # Count skills by type
        skill_types = queryset.values('actual_skill_type').annotate(count=Count('id'))

        # Get top skills
        top_skills = queryset.values('skill_name').annotate(count=Count('id')).order_by('-count')[:10]

        # Get statistics by department
        departments = Department.objects.all()
        department_stats = []

        for dept in departments:
            employees = Employee.objects.filter(department=dept)
            dept_skills = queryset.filter(employee__in=employees)

            if dept_skills.exists():
                department_stats.append({
                    'department_id': dept.id,
                    'department_name': dept.name,
                    'total_skills': dept_skills.count(),
                    'unique_skills': dept_skills.values('skill_name').distinct().count(),
                    'avg_experience': dept_skills.aggregate(Avg('years_of_experience'))['years_of_experience__avg'] or 0
                })

        # Compile statistics
        stats = {
            'total_skills': total_skills,
            'unique_skills': unique_skills,
            'average_experience': avg_experience,
            'skill_levels': [],
            'skill_types': [],
            'top_skills': [],
            'department_statistics': department_stats
        }

        # Format skill levels
        for level in skill_levels:
            level_value = level['skill_level']
            level_display = dict(SkillLevel.choices()).get(level_value, level_value)
            stats['skill_levels'].append({
                'level': level_value,
                'level_display': level_display,
                'count': level['count'],
                'percentage': (level['count'] / total_skills * 100) if total_skills > 0 else 0
            })

        # Format skill types
        for type_count in skill_types:
            type_value = type_count['actual_skill_type']
            type_display = dict(SkillType.choices()).get(type_value, type_value)
            stats['skill_types'].append({
                'type': type_value,
                'type_display': type_display,
                'count': type_count['count'],
                'percentage': (type_count['count'] / total_skills * 100) if total_skills > 0 else 0
            })

        # Format top skills
        for skill in top_skills:
            stats['top_skills'].append({
                'skill_name': skill['skill_name'],
                'count': skill['count'],
                'percentage': (skill['count'] / total_skills * 100) if total_skills > 0 else 0
            })

        return Response(stats)

    @action(detail=False, methods=['get'])
    def skill_search(self, request):
        """
        Search for employees with specific skills
        """
        # Get query parameters
        skill_name = request.query_params.get('skill_name', None)
        skill_level = request.query_params.get('skill_level', None)
        min_experience = request.query_params.get('min_experience', None)
        department_id = request.query_params.get('department_id', None)

        if not skill_name:
            return Response(
                {"detail": "skill_name parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Base queryset
        queryset = self.queryset.filter(skill_name__icontains=skill_name)

        # Filter by skill level if provided
        if skill_level:
            if skill_level not in dict(SkillLevel.choices()):
                return Response(
                    {"detail": "Invalid skill level"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            queryset = queryset.filter(skill_level=skill_level)

        # Filter by minimum experience if provided
        if min_experience:
            try:
                min_experience = float(min_experience)
                queryset = queryset.filter(years_of_experience__gte=min_experience)
            except ValueError:
                return Response(
                    {"detail": "Invalid min_experience value"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def skill_matrix(self, request):
        """
        Get a skill matrix for the organization
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)
        skill_type = request.query_params.get('skill_type', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Filter by skill type if provided
        if skill_type:
            if skill_type not in dict(SkillType.choices()):
                return Response(
                    {"detail": "Invalid skill type"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            queryset = queryset.filter(actual_skill_type=skill_type)

        # Get all unique skills
        unique_skills = queryset.values_list('skill_name', flat=True).distinct()

        # Get all employees with skills
        employees_with_skills = Employee.objects.filter(
            id__in=queryset.values_list('employee', flat=True).distinct()
        )

        # Build the skill matrix
        skill_matrix = []

        for employee in employees_with_skills:
            employee_skills = queryset.filter(employee=employee)

            employee_data = {
                'employee_id': employee.id,
                'employee_name': employee.full_name,
                'department': employee.department.name if employee.department else None,
                'skills': []
            }

            # Add skills for this employee
            for skill_name in unique_skills:
                try:
                    skill = employee_skills.get(skill_name=skill_name)
                    employee_data['skills'].append({
                        'skill_name': skill_name,
                        'skill_level': skill.skill_level,
                        'skill_level_display': skill.get_skill_level_display(),
                        'years_of_experience': skill.years_of_experience,
                        'is_primary_skill': skill.is_primary_skill
                    })
                except SkillOffering.DoesNotExist:
                    # Employee doesn't have this skill
                    pass

            skill_matrix.append(employee_data)

        return Response({
            'unique_skills': list(unique_skills),
            'employees': skill_matrix
        })
