from rest_framework import serializers
from onboarding_offboarding_app.models.onboarding import OnboardingPlan, OnboardingTask
from employees_app.serializers.employee_serializer import EmployeeSerializer
from organization_app.serializers.department_serializer import DepartmentSerializer


class OnboardingTaskSerializer(serializers.ModelSerializer):
    """
    Serializer for the OnboardingTask model.
    """
    assigned_to_details = EmployeeSerializer(source='assigned_to', read_only=True)
    completed_by_details = EmployeeSerializer(source='completed_by', read_only=True)

    class Meta:
        model = OnboardingTask
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
        ref_name = 'OffboardingOnboardingTask'

    def validate(self, data):
        """
        Validate the onboarding task data.
        """
        # Validate due date is not before plan start date
        due_date = data.get('due_date')
        plan = data.get('plan')

        if due_date and plan and due_date < plan.start_date:
            raise serializers.ValidationError("Due date cannot be before the plan start date.")

        return data


class OnboardingPlanSerializer(serializers.ModelSerializer):
    """
    Serializer for the OnboardingPlan model.
    """
    employee_details = EmployeeSerializer(source='employee', read_only=True)
    department_details = DepartmentSerializer(source='department', read_only=True)
    mentor_details = EmployeeSerializer(source='mentor', read_only=True)
    assigned_by_details = EmployeeSerializer(source='assigned_by', read_only=True)
    tasks = OnboardingTaskSerializer(many=True, read_only=True)

    class Meta:
        model = OnboardingPlan
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

    def validate(self, data):
        """
        Validate the onboarding plan data.
        """
        # Validate end date is not before start date
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("End date cannot be before the start date.")

        return data

    def create(self, validated_data):
        """
        Create a new onboarding plan.
        """
        # Create the onboarding plan
        onboarding_plan = OnboardingPlan.objects.create(**validated_data)

        return onboarding_plan
