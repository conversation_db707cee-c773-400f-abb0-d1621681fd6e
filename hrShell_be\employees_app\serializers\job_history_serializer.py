from rest_framework import serializers
from employees_app.models.job_history import JobHistory


class JobHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for the JobHistory model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    approved_by_name = serializers.ReadOnlyField(source='approved_by.full_name')

    class Meta:
        model = JobHistory
        fields = [
            'id', 'employee', 'employee_name', 'previous_job_title',
            'previous_department', 'previous_salary', 'start_date', 'end_date',
            'reason', 'approved_by', 'approved_by_name'
        ]

    def validate(self, data):
        """
        Validate that end_date is after or equal to start_date
        """
        if 'start_date' in data and 'end_date' in data:
            if data['end_date'] < data['start_date']:
                raise serializers.ValidationError("End date must be after or equal to start date")
        return data
