import django_filters
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from employees_app.models.employee_detail import EmployeeDetail, MaritalStatus
from employees_app.models.employee_hike import EmployeeHike
from employees_app.models.employee_attendance import EmployeeAttendance
from employees_app.models.employee_leave import EmployeeLeave
from employees_app.models.job_history import JobHistory
from employees_app.models.salary import Salary
from employees_app.models.employee_documents import EmployeeDocument
from employees_app.models.skill_offering import SkillOffering


class EmployeeFilter(django_filters.FilterSet):
    """
    Custom filter for Employee model
    """
    name = django_filters.CharFilter(method='filter_name')
    department_name = django_filters.CharFilter(field_name='department__name', lookup_expr='icontains')
    hired_after = django_filters.DateFilter(field_name='hire_date', lookup_expr='gte')
    hired_before = django_filters.DateFilter(field_name='hire_date', lookup_expr='lte')

    class Meta:
        model = Employee
        fields = ['status', 'gender', 'department', 'department_name', 'hired_after', 'hired_before']

    def filter_name(self, queryset, name, value):
        """
        Filter by first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(first_name__icontains=value) |
            django_filters.Q(last_name__icontains=value)
        )


class DepartmentFilter(django_filters.FilterSet):
    """
    Custom filter for Department model
    """
    manager = django_filters.NumberFilter(field_name='manager__id')

    class Meta:
        model = Department
        fields = ['status', 'manager']


class EmployeeDetailFilter(django_filters.FilterSet):
    """
    Custom filter for EmployeeDetail model
    """
    employee_name = django_filters.CharFilter(method='filter_employee_name')
    department_name = django_filters.CharFilter(field_name='department__name', lookup_expr='icontains')
    reporting_manager_name = django_filters.CharFilter(method='filter_reporting_manager_name')
    skills = django_filters.CharFilter(field_name='technical_skills', lookup_expr='icontains')

    class Meta:
        model = EmployeeDetail
        fields = [
            'employee', 'department', 'marital_status',
            'employee_name', 'department_name', 'reporting_manager_name',
            'skills'
        ]

    def filter_employee_name(self, queryset, name, value):
        """
        Filter by employee's first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(employee__first_name__icontains=value) |
            django_filters.Q(employee__last_name__icontains=value)
        )

    def filter_reporting_manager_name(self, queryset, name, value):
        """
        Filter by reporting manager's first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(reporting_manager__first_name__icontains=value) |
            django_filters.Q(reporting_manager__last_name__icontains=value)
        )


class EmployeeHikeFilter(django_filters.FilterSet):
    """
    Custom filter for EmployeeHike model
    """
    employee_name = django_filters.CharFilter(method='filter_employee_name')
    department_name = django_filters.CharFilter(field_name='department__name', lookup_expr='icontains')
    min_percentage = django_filters.NumberFilter(field_name='hike_percentage', lookup_expr='gte')
    max_percentage = django_filters.NumberFilter(field_name='hike_percentage', lookup_expr='lte')
    effective_after = django_filters.DateFilter(field_name='hike_effective_date', lookup_expr='gte')
    effective_before = django_filters.DateFilter(field_name='hike_effective_date', lookup_expr='lte')
    min_new_salary = django_filters.NumberFilter(field_name='new_salary', lookup_expr='gte')
    max_new_salary = django_filters.NumberFilter(field_name='new_salary', lookup_expr='lte')

    class Meta:
        model = EmployeeHike
        fields = [
            'employee', 'department', 'department_name',
            'min_percentage', 'max_percentage',
            'effective_after', 'effective_before',
            'min_new_salary', 'max_new_salary'
        ]

    def filter_employee_name(self, queryset, name, value):
        """
        Filter by employee's first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(employee__first_name__icontains=value) |
            django_filters.Q(employee__last_name__icontains=value)
        )


class EmployeeAttendanceFilter(django_filters.FilterSet):
    """
    Custom filter for EmployeeAttendance model
    """
    employee_name = django_filters.CharFilter(method='filter_employee_name')
    date_from = django_filters.DateFilter(field_name='date', lookup_expr='gte')
    date_to = django_filters.DateFilter(field_name='date', lookup_expr='lte')

    class Meta:
        model = EmployeeAttendance
        fields = ['employee', 'date', 'status', 'date_from', 'date_to', 'employee_name']

    def filter_employee_name(self, queryset, name, value):
        """
        Filter by employee's first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(employee__first_name__icontains=value) |
            django_filters.Q(employee__last_name__icontains=value)
        )


class EmployeeLeaveFilter(django_filters.FilterSet):
    """
    Custom filter for EmployeeLeave model
    """
    employee_name = django_filters.CharFilter(method='filter_employee_name')
    start_date_from = django_filters.DateFilter(field_name='start_date', lookup_expr='gte')
    start_date_to = django_filters.DateFilter(field_name='start_date', lookup_expr='lte')
    end_date_from = django_filters.DateFilter(field_name='end_date', lookup_expr='gte')
    end_date_to = django_filters.DateFilter(field_name='end_date', lookup_expr='lte')

    class Meta:
        model = EmployeeLeave
        fields = [
            'employee', 'employee_name', 'leave_type', 'status',
            'start_date_from', 'start_date_to', 'end_date_from', 'end_date_to'
        ]

    def filter_employee_name(self, queryset, name, value):
        """
        Filter by employee's first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(employee__first_name__icontains=value) |
            django_filters.Q(employee__last_name__icontains=value)
        )


class JobHistoryFilter(django_filters.FilterSet):
    """
    Custom filter for JobHistory model
    """
    employee_name = django_filters.CharFilter(method='filter_employee_name')
    start_date_from = django_filters.DateFilter(field_name='start_date', lookup_expr='gte')
    start_date_to = django_filters.DateFilter(field_name='start_date', lookup_expr='lte')
    end_date_from = django_filters.DateFilter(field_name='end_date', lookup_expr='gte')
    end_date_to = django_filters.DateFilter(field_name='end_date', lookup_expr='lte')

    class Meta:
        model = JobHistory
        fields = [
            'employee', 'employee_name', 'previous_department',
            'start_date_from', 'start_date_to', 'end_date_from', 'end_date_to'
        ]

    def filter_employee_name(self, queryset, name, value):
        """
        Filter by employee's first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(employee__first_name__icontains=value) |
            django_filters.Q(employee__last_name__icontains=value)
        )


class SalaryFilter(django_filters.FilterSet):
    """
    Custom filter for Salary model
    """
    employee_name = django_filters.CharFilter(method='filter_employee_name')
    min_basic = django_filters.NumberFilter(field_name='basic', lookup_expr='gte')
    max_basic = django_filters.NumberFilter(field_name='basic', lookup_expr='lte')
    min_net = django_filters.NumberFilter(field_name='net_amount_payable', lookup_expr='gte')
    max_net = django_filters.NumberFilter(field_name='net_amount_payable', lookup_expr='lte')
    effective_date_from = django_filters.DateFilter(field_name='salary_effective_date', lookup_expr='gte')
    effective_date_to = django_filters.DateFilter(field_name='salary_effective_date', lookup_expr='lte')

    class Meta:
        model = Salary
        fields = [
            'employee', 'employee_name', 'payment_method',
            'min_basic', 'max_basic', 'min_net', 'max_net',
            'effective_date_from', 'effective_date_to'
        ]

    def filter_employee_name(self, queryset, name, value):
        """
        Filter by employee's first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(employee__first_name__icontains=value) |
            django_filters.Q(employee__last_name__icontains=value)
        )


class EmployeeDocumentFilter(django_filters.FilterSet):
    """
    Custom filter for EmployeeDocument model
    """
    employee_name = django_filters.CharFilter(method='filter_employee_name')

    class Meta:
        model = EmployeeDocument
        fields = ['employee', 'employee_name']

    def filter_employee_name(self, queryset, name, value):
        """
        Filter by employee's first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(employee__first_name__icontains=value) |
            django_filters.Q(employee__last_name__icontains=value)
        )


class SkillOfferingFilter(django_filters.FilterSet):
    """
    Custom filter for SkillOffering model
    """
    employee_name = django_filters.CharFilter(method='filter_employee_name')
    department_name = django_filters.CharFilter(field_name='department__name', lookup_expr='icontains')
    min_experience = django_filters.NumberFilter(field_name='years_of_experience', lookup_expr='gte')
    max_experience = django_filters.NumberFilter(field_name='years_of_experience', lookup_expr='lte')

    class Meta:
        model = SkillOffering
        fields = [
            'employee', 'employee_name', 'skill_level', 'is_primary_skill',
            'department', 'department_name', 'actual_skill_type',
            'min_experience', 'max_experience'
        ]

    def filter_employee_name(self, queryset, name, value):
        """
        Filter by employee's first_name or last_name containing the value
        """
        return queryset.filter(
            django_filters.Q(employee__first_name__icontains=value) |
            django_filters.Q(employee__last_name__icontains=value)
        )