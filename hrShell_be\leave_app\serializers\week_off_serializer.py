from rest_framework import serializers
from leave_app.models.week_off import WeekOff, DayOfWeek


class WeekOffSerializer(serializers.ModelSerializer):
    """
    Serializer for the WeekOff model
    """
    organization_name = serializers.ReadOnlyField(source='organization.name')
    location_name = serializers.ReadOnlyField(source='location.name')
    department_name = serializers.ReadOnlyField(source='department.name')
    day_of_week_display = serializers.ReadOnlyField(source='get_day_of_week_display')
    
    class Meta:
        model = WeekOff
        fields = [
            'id', 'organization', 'organization_name', 'location', 'location_name',
            'department', 'department_name', 'day_of_week', 'day_of_week_display',
            'is_half_day', 'first_half', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate that the week off is unique for the organization, location, department, and day of week
        """
        organization = data.get('organization')
        location = data.get('location')
        department = data.get('department')
        day_of_week = data.get('day_of_week')
        
        # Check if we're updating an existing instance
        instance = self.instance
        if instance:
            # If we're not changing organization, location, department, or day of week, no need to check
            if (instance.organization == organization and 
                instance.location == location and 
                instance.department == department and 
                instance.day_of_week == day_of_week):
                return data
        
        # Check if a week off already exists for this organization, location, department, and day of week
        existing_week_off = WeekOff.objects.filter(
            organization=organization,
            day_of_week=day_of_week
        )
        
        if location:
            existing_week_off = existing_week_off.filter(location=location)
        else:
            existing_week_off = existing_week_off.filter(location__isnull=True)
        
        if department:
            existing_week_off = existing_week_off.filter(department=department)
        else:
            existing_week_off = existing_week_off.filter(department__isnull=True)
        
        if existing_week_off.exists():
            raise serializers.ValidationError(
                "A week off already exists for this organization, location, department, and day of week"
            )
        
        return data
