from django.db import models
from django.core.validators import MinValueValidator
from organization_app.models.organization import Organization
from employees_app.models.employee import Employee


class TaxSlab(models.Model):
    """
    Model to define tax slabs for income tax calculation.
    """
    GENDER_CHOICES = [
        ('all', 'All'),
        ('male', 'Male'),
        ('female', 'Female'),
        ('other', 'Other'),
    ]
    
    AGE_GROUP_CHOICES = [
        ('all', 'All Ages'),
        ('below_60', 'Below 60 Years'),
        ('60_to_80', '60 to 80 Years'),
        ('above_80', 'Above 80 Years'),
    ]
    
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='tax_slabs')
    financial_year = models.CharField(max_length=10, help_text="Format: YYYY-YY (e.g., 2023-24)")
    name = models.CharField(max_length=100)
    min_income = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    max_income = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], null=True, blank=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0)])
    surcharge_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0)], default=0)
    cess_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0)], default=0)
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, default='all')
    age_group = models.CharField(max_length=10, choices=AGE_GROUP_CHOICES, default='all')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['financial_year', 'min_income']
        verbose_name = 'Tax Slab'
        verbose_name_plural = 'Tax Slabs'
    
    def __str__(self):
        max_income_str = f" to {self.max_income}" if self.max_income else "+"
        return f"{self.financial_year}: {self.min_income}{max_income_str} @ {self.tax_rate}%"


class EmployeeTaxDeclaration(models.Model):
    """
    Model to store employee tax declarations and exemptions.
    """
    DECLARATION_TYPE_CHOICES = [
        ('section_80c', 'Section 80C'),
        ('section_80d', 'Section 80D'),
        ('section_80e', 'Section 80E'),
        ('section_80g', 'Section 80G'),
        ('section_24', 'Section 24 (Home Loan Interest)'),
        ('hra', 'HRA Exemption'),
        ('lta', 'LTA Exemption'),
        ('other', 'Other Exemption'),
    ]
    
    STATUS_CHOICES = [
        ('declared', 'Declared'),
        ('submitted', 'Proof Submitted'),
        ('verified', 'Verified'),
        ('rejected', 'Rejected'),
    ]
    
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='tax_declarations')
    financial_year = models.CharField(max_length=10, help_text="Format: YYYY-YY (e.g., 2023-24)")
    declaration_type = models.CharField(max_length=20, choices=DECLARATION_TYPE_CHOICES)
    declaration_name = models.CharField(max_length=100)
    declared_amount = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    proof_document = models.FileField(upload_to='tax_proofs/%Y/%m/', null=True, blank=True)
    verified_amount = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='declared')
    remarks = models.TextField(blank=True, null=True)
    verified_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='tax_declarations_verified')
    verification_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['financial_year', 'employee__first_name', 'employee__last_name', 'declaration_type']
        verbose_name = 'Employee Tax Declaration'
        verbose_name_plural = 'Employee Tax Declarations'
    
    def __str__(self):
        return f"{self.employee} - {self.get_declaration_type_display()} ({self.declared_amount})"
