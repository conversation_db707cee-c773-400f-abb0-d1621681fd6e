from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from employees_app.models.employee import Employee
from employees_app.serializers.employee_serializer import EmployeeSerializer, EmployeeListSerializer
from employees_app.filters import EmployeeFilter


class EmployeeViewSet(viewsets.ModelViewSet):
    """
    ViewSet for viewing and editing Employee instances.

    Provides CRUD operations for employees including:
    - List all employees with filtering and search
    - Create new employee records
    - Retrieve individual employee details
    - Update employee information
    - Delete employee records
    - Custom actions for activation/deactivation
    """
    queryset = Employee.objects.all()
    serializer_class = EmployeeSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = EmployeeFilter
    search_fields = ['first_name', 'last_name', 'email', 'position']
    ordering_fields = ['first_name', 'last_name', 'hire_date', 'created_at']

    def get_serializer_class(self):
        """
        Return different serializers based on the action
        """
        if self.action == 'list':
            return EmployeeListSerializer
        return EmployeeSerializer

    @swagger_auto_schema(tags=['Employee Management'])
    def list(self, request, *args, **kwargs):
        """List all employees"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def create(self, request, *args, **kwargs):
        """Create a new employee"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an employee"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def update(self, request, *args, **kwargs):
        """Update an employee"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an employee"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an employee"""
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_description="Get all active employees",
        operation_summary="List Active Employees",
        responses={
            200: openapi.Response(
                description="List of active employees",
                schema=EmployeeListSerializer(many=True)
            )
        },
        tags=['Employee Management']
    )
    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Return a list of all active employees
        """
        active_employees = Employee.objects.filter(status='active')
        page = self.paginate_queryset(active_employees)
        if page is not None:
            serializer = EmployeeListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = EmployeeListSerializer(active_employees, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Deactivate an employee by setting their status to inactive",
        operation_summary="Deactivate Employee",
        responses={
            200: openapi.Response(
                description="Employee deactivated successfully",
                schema=EmployeeSerializer()
            )
        },
        tags=['Employee Management']
    )
    @action(detail=True, methods=['patch'])
    def deactivate(self, request, pk=None):
        """
        Deactivate an employee
        """
        employee = self.get_object()
        employee.status = 'inactive'
        employee.save()
        serializer = EmployeeSerializer(employee)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Activate an employee by setting their status to active",
        operation_summary="Activate Employee",
        responses={
            200: openapi.Response(
                description="Employee activated successfully",
                schema=EmployeeSerializer()
            )
        },
        tags=['Employee Management']
    )
    @action(detail=True, methods=['patch'])
    def activate(self, request, pk=None):
        """
        Activate an employee
        """
        employee = self.get_object()
        employee.status = 'active'
        employee.save()
        serializer = EmployeeSerializer(employee)
        return Response(serializer.data)
