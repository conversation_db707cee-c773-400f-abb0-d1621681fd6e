-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create a test table with vector column to verify the extension works
CREATE TABLE IF NOT EXISTS vector_test (
    id SERIAL PRIMARY KEY,
    embedding vector(768),
    content TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert a sample vector
INSERT INTO vector_test (embedding, content)
VALUES (
    '[0.1, 0.2, 0.3, 0.4, 0.5]'::vector, 
    'This is a test vector to verify pgvector is working correctly.'
);

-- Grant permissions to the postgres user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Create indexes for vector search
CREATE INDEX IF NOT EXISTS vector_test_embedding_idx ON vector_test USING ivfflat (embedding vector_l2_ops);

-- Output a message
\echo 'pgvector extension has been successfully installed and configured.'
