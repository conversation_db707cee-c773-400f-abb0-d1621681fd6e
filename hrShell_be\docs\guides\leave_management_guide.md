# Leave Management Guide

This guide provides comprehensive information about the leave management module in the HR Management API.

## Overview

The leave management module allows organizations to:

- Define different types of leave (e.g., Casual, Sick, Annual)
- Create leave policies for different departments, locations, and designations
- Track employee leave balances
- Process leave requests with multi-level approval
- Manage holidays and weekly off days
- Generate reports on leave utilization

## Module Components

The leave management module consists of the following components:

1. **Leave Types**: Different categories of leave (e.g., Casual, Sick, Annual)
2. **Leave Policies**: Rules for leave accrual, approval, carry forward, etc.
3. **Leave Balances**: Employee-specific leave balances for each leave type
4. **Leave Requests**: Applications for leave submitted by employees
5. **Leave Approvals**: Multi-level approval records for leave requests
6. **Holidays**: Organization or location-specific holidays
7. **Week Offs**: Weekly off days for different departments and locations

## Workflow

### Setting Up Leave Management

1. **Create Leave Types**:
   - Define different types of leave (e.g., Casual, Sick, Annual)
   - Specify properties like maximum days, carry forward rules, etc.

2. **Create Leave Policies**:
   - Define policies for different departments, locations, or designations
   - Specify accrual methods, approval requirements, etc.

3. **Set Up Holidays and Week Offs**:
   - Define organization-wide and location-specific holidays
   - Configure weekly off days for different departments and locations

### Leave Application Process

1. **Employee Submits Leave Request**:
   - Employee selects leave type, dates, and provides reason
   - System checks leave balance and policy compliance

2. **Leave Approval Process**:
   - Manager(s) receive notification of leave request
   - Approvers can approve, reject, or skip approval
   - Multi-level approval if configured

3. **Leave Balance Update**:
   - Upon approval, leave balance is updated
   - Leave is marked in attendance records

## API Usage Examples

### Creating a Leave Type

```http
POST /api/v1/leave-types/
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Casual Leave",
  "code": "CL",
  "description": "Leave for personal matters",
  "organization": 1,
  "is_paid": true,
  "max_days_per_year": 12,
  "min_days_per_request": 1,
  "max_days_per_request": 3,
  "carry_forward_allowed": false,
  "encashment_allowed": false,
  "applicable_during_probation": true,
  "probation_period_percentage": 50,
  "requires_documentation": false,
  "status": "active"
}
```

### Creating a Leave Policy

```http
POST /api/v1/leave-policies/
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Engineering Casual Leave Policy",
  "description": "Casual leave policy for engineering department",
  "organization": 1,
  "leave_type": 1,
  "department": 2,
  "accrual_method": "monthly",
  "accrual_frequency": "prorated",
  "max_accrual": 12,
  "carry_forward_limit": 6,
  "carry_forward_expiry_months": 3,
  "probation_period_days": 90,
  "apply_sandwich_rule": true,
  "requires_approval": true,
  "min_days_before_application": 1,
  "max_days_before_application": 30
}
```

### Creating a Leave Balance

```http
POST /api/v1/leave-balances/
Content-Type: application/json
Authorization: Bearer <token>

{
  "employee": 1,
  "leave_type": 1,
  "year": 2023,
  "opening_balance": 12.0
}
```

### Submitting a Leave Request

```http
POST /api/v1/leave-requests/
Content-Type: application/json
Authorization: Bearer <token>

{
  "employee": 1,
  "leave_type": 1,
  "start_date": "2023-05-15",
  "end_date": "2023-05-17",
  "reason": "Personal matters",
  "half_day": false
}
```

### Approving a Leave Request

```http
POST /api/v1/leave-requests/1/approve-request/
Authorization: Bearer <token>
```

### Rejecting a Leave Request

```http
POST /api/v1/leave-requests/1/reject-request/
Content-Type: application/json
Authorization: Bearer <token>

{
  "rejection_reason": "Insufficient team coverage during this period"
}
```

## Advanced Features

### Leave Accrual

The system supports different accrual methods:
- **Yearly**: Full allocation at the start or end of the year
- **Monthly**: Monthly accrual (e.g., 1 day per month)
- **Quarterly**: Quarterly accrual
- **Biannually**: Twice a year accrual

### Carry Forward

Unused leave can be carried forward to the next year based on:
- Maximum carry forward limit
- Expiry period for carried forward leave

### Leave Encashment

Unused leave can be encashed based on:
- Maximum encashment limit
- Encashment rules in the leave policy

### Sandwich Rule

If enabled, weekends and holidays between leave days are counted as leave days.

### Multi-level Approval

Leave requests can require approval from multiple levels:
- Direct manager
- Department head
- HR manager

## Integration with Other Modules

The leave management module integrates with:

1. **Employee Module**: For employee information and reporting structure
2. **Organization Module**: For organization, department, and location information
3. **Attendance Module**: For marking leave days in attendance records

## Best Practices

1. **Define Clear Leave Policies**:
   - Clearly define leave policies for different employee groups
   - Document carry forward and encashment rules

2. **Regular Balance Updates**:
   - Regularly update leave balances (monthly/quarterly)
   - Notify employees of their current leave balances

3. **Approval Workflow**:
   - Define clear approval workflows
   - Set up auto-approval for certain leave types if needed

4. **Documentation**:
   - Require documentation for certain leave types (e.g., sick leave)
   - Store documentation securely

## Troubleshooting

### Common Issues

1. **Insufficient Leave Balance**:
   - Check if the employee has sufficient leave balance
   - Check if the leave policy allows negative balance

2. **Overlapping Leave Requests**:
   - Check if there are any approved leave requests that overlap

3. **Policy Conflicts**:
   - Check if multiple policies apply to the same employee
   - Resolve conflicts based on priority rules

## API Reference

For detailed API documentation, refer to the following model documentation:

- [LeaveType](../models/leave_type.md)
- [LeavePolicy](../models/leave_policy.md)
- [LeaveBalance](../models/leave_balance.md)
- [LeaveRequest](../models/leave_request.md)
- [LeaveApproval](../models/leave_approval.md)
- [Holiday](../models/holiday.md)
- [WeekOff](../models/week_off.md)
