from django.urls import path, include
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>
from leave_app.views.leave_type_views import LeaveTypeViewSet
from leave_app.views.leave_policy_views import LeavePolicyViewSet
from leave_app.views.leave_balance_views import LeaveBalanceViewSet
from leave_app.views.leave_request_views import LeaveRequestViewSet
from leave_app.views.leave_approval_views import LeaveA<PERSON>rovalViewSet
from leave_app.views.holiday_views import HolidayViewSet
from leave_app.views.week_off_views import WeekOffViewSet

app_name = 'leave'

router = DefaultRouter()
router.register(r'leave-types', LeaveTypeViewSet)
router.register(r'leave-policies', LeavePolicyViewSet)
router.register(r'leave-balances', LeaveBalanceViewSet)
router.register(r'leave-requests', LeaveRequestViewSet)
router.register(r'leave-approvals', LeaveApprovalViewSet)
router.register(r'holidays', HolidayViewSet)
router.register(r'week-offs', WeekOffViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
