from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from payroll_app.models.bonus import Bonus, BonusBatch
from payroll_app.serializers.bonus_serializer import BonusSerializer, BonusBatchSerializer
from payroll_app.filters.payroll_filters import BonusFilter
from employees_app.models.employee import Employee


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class BonusViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for bonuses.
    """
    queryset = Bonus.objects.all()
    serializer_class = BonusSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = BonusFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email', 'description']
    ordering_fields = ['employee__first_name', 'bonus_type', 'amount', 'payment_date', 'status', 'created_at']
    ordering = ['-payment_date']

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all bonuses with a specific status.
        """
        bonuses = Bonus.objects.filter(status=status_value)
        page = self.paginate_queryset(bonuses)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(bonuses, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all bonuses for a specific employee.
        """
        bonuses = Bonus.objects.filter(employee_id=employee_pk)
        page = self.paginate_queryset(bonuses)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(bonuses, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def type(self, request, bonus_type=None):
        """
        Get all bonuses of a specific type.
        """
        bonuses = Bonus.objects.filter(bonus_type=bonus_type)
        page = self.paginate_queryset(bonuses)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(bonuses, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """
        Approve a specific bonus.
        """
        bonus = self.get_object()

        if bonus.status != 'pending':
            return Response(
                {"detail": "Only pending bonuses can be approved."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update bonus status
        bonus.status = 'approved'
        bonus.approval_date = timezone.now().date()

        # Set the approver if provided
        approver_id = request.data.get('approved_by')
        if approver_id:
            bonus.approved_by_id = approver_id

        bonus.save()

        serializer = self.get_serializer(bonus)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """
        Reject a specific bonus.
        """
        bonus = self.get_object()

        if bonus.status != 'pending':
            return Response(
                {"detail": "Only pending bonuses can be rejected."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update bonus status
        bonus.status = 'rejected'

        # Set the rejection reason if provided
        rejection_reason = request.data.get('rejection_reason')
        if rejection_reason:
            bonus.rejection_reason = rejection_reason

        # Set the approver if provided
        approver_id = request.data.get('approved_by')
        if approver_id:
            bonus.approved_by_id = approver_id

        bonus.save()

        serializer = self.get_serializer(bonus)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def mark_paid(self, request, pk=None):
        """
        Mark a specific bonus as paid.
        """
        bonus = self.get_object()

        if bonus.status != 'approved':
            return Response(
                {"detail": "Only approved bonuses can be marked as paid."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update bonus status
        bonus.status = 'paid'
        bonus.save()

        serializer = self.get_serializer(bonus)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def create_batch(self, request):
        """
        Create bonuses for multiple employees in a batch.
        """
        batch_data = request.data

        # Validate required fields
        required_fields = ['bonus_type', 'payment_date', 'employee_ids']
        for field in required_fields:
            if field not in batch_data:
                return Response(
                    {"detail": f"Field '{field}' is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Get employee IDs
        employee_ids = batch_data.get('employee_ids', [])
        if not employee_ids:
            return Response(
                {"detail": "No employee IDs provided."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create bonuses
        bonuses_created = 0
        for employee_id in employee_ids:
            try:
                employee = Employee.objects.get(id=employee_id)

                # Create bonus
                Bonus.objects.create(
                    employee=employee,
                    bonus_type=batch_data.get('bonus_type'),
                    amount=batch_data.get('amount', 0),
                    payment_date=batch_data.get('payment_date'),
                    reference_period_start=batch_data.get('reference_period_start'),
                    reference_period_end=batch_data.get('reference_period_end'),
                    description=batch_data.get('description'),
                    is_taxable=batch_data.get('is_taxable', True),
                    is_prorated=batch_data.get('is_prorated', False),
                    status='draft'
                )

                bonuses_created += 1
            except Employee.DoesNotExist:
                pass

        return Response({
            "detail": f"Created {bonuses_created} bonuses.",
            "bonuses_created": bonuses_created
        })


class BonusBatchViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for bonus batches.
    """
    queryset = BonusBatch.objects.all()
    serializer_class = BonusBatchSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'bonus_type', 'payment_date', 'status', 'created_at']
    ordering = ['-payment_date']

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all bonus batches with a specific status.
        """
        batches = BonusBatch.objects.filter(status=status_value)
        page = self.paginate_queryset(batches)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(batches, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all bonus batches for a specific organization.
        """
        batches = BonusBatch.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(batches)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(batches, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def bonuses(self, request, pk=None):
        """
        Get all bonuses for a specific batch.
        """
        batch = self.get_object()

        bonuses = Bonus.objects.filter(
            bonus_type=batch.bonus_type,
            payment_date=batch.payment_date,
            reference_period_start=batch.reference_period_start,
            reference_period_end=batch.reference_period_end
        )

        serializer = BonusSerializer(bonuses, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def process(self, request, pk=None):
        """
        Process a bonus batch by creating individual bonuses.
        """
        batch = self.get_object()

        if batch.status != 'draft':
            return Response(
                {"detail": "Only draft batches can be processed."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get employee IDs from request
        employee_ids = request.data.get('employee_ids', [])
        if not employee_ids:
            return Response(
                {"detail": "No employee IDs provided."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update batch status
        batch.status = 'processing'
        batch.save()

        # Create bonuses
        bonuses_created = 0
        for employee_id in employee_ids:
            try:
                employee = Employee.objects.get(id=employee_id)

                # Calculate bonus amount (this is a simplified example)
                amount = request.data.get('amount', 0)

                # Create bonus
                Bonus.objects.create(
                    employee=employee,
                    bonus_type=batch.bonus_type,
                    amount=amount,
                    payment_date=batch.payment_date,
                    reference_period_start=batch.reference_period_start,
                    reference_period_end=batch.reference_period_end,
                    description=batch.description,
                    is_taxable=True,
                    is_prorated=False,
                    status='draft'
                )

                bonuses_created += 1
            except Employee.DoesNotExist:
                pass

        # Update batch status and total amount
        batch.status = 'completed'
        batch.total_amount = Bonus.objects.filter(
            bonus_type=batch.bonus_type,
            payment_date=batch.payment_date,
            reference_period_start=batch.reference_period_start,
            reference_period_end=batch.reference_period_end
        ).aggregate(total=models.Sum('amount'))['total'] or 0
        batch.save()

        serializer = self.get_serializer(batch)
        return Response({
            "detail": f"Created {bonuses_created} bonuses.",
            "bonuses_created": bonuses_created,
            "batch": serializer.data
        })
