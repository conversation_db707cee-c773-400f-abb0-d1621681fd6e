from django.db import models
from enum import Enum


class DesignationLevel(Enum):
    JUNIOR = 'junior'
    MID = 'mid'
    SENIOR = 'senior'
    LEAD = 'lead'
    MANAGER = 'manager'
    DIRECTOR = 'director'
    EXECUTIVE = 'executive'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class Designation(models.Model):
    """
    Designation model for storing job titles/roles
    """
    # Basic information
    title = models.CharField(max_length=100)
    organization = models.ForeignKey(
        'organization_app.Organization',
        on_delete=models.CASCADE,
        related_name='designations'
    )
    
    # Details
    description = models.TextField(blank=True, null=True)
    level = models.CharField(
        max_length=20,
        choices=DesignationLevel.choices(),
        default=DesignationLevel.MID.value
    )
    
    # Department association
    department = models.ForeignKey(
        'employees_app.Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='designations'
    )
    
    # Pay grade (optional)
    pay_grade = models.CharField(max_length=50, blank=True, null=True)
    min_salary = models.DecimalField(
        max_digits=12, 
        decimal_places=2, 
        blank=True, 
        null=True
    )
    max_salary = models.DecimalField(
        max_digits=12, 
        decimal_places=2, 
        blank=True, 
        null=True
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['title']
        verbose_name = "Designation"
        verbose_name_plural = "Designations"
        unique_together = ('organization', 'title', 'department')
    
    def __str__(self):
        dept_name = self.department.name if self.department else "No Department"
        return f"{self.title} ({dept_name})"
