# LeaveRequest Model

The LeaveRequest model represents leave applications submitted by employees.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `employee` | ForeignKey | Employee requesting leave | Yes |
| `leave_type` | ForeignKey | Type of leave requested | Yes |
| `start_date` | Date | Start date of leave | Yes |
| `end_date` | Date | End date of leave | Yes |
| `total_days` | Decimal | Total days of leave (calculated) | Auto-calculated |
| `half_day` | Boolean | Whether this is a half-day leave | Yes (default: False) |
| `first_half` | Boolean | If half_day is True, whether it's the first half or second half | Yes (default: True) |
| `reason` | Text | Reason for leave | Yes |
| `attachment` | File | Supporting document (if any) | No |
| `status` | String | Status of the leave request (choices: pending, approved, rejected, cancelled) | Yes (default: pending) |
| `approved_by` | ForeignKey | Employee who approved/rejected the request | No |
| `approval_date` | DateTime | Date and time of approval/rejection | No |
| `rejection_reason` | Text | Reason for rejection (if rejected) | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `employee` | Employee | The employee requesting leave |
| `leave_type` | LeaveType | The type of leave requested |
| `approved_by` | Employee | The employee who approved/rejected the request |
| `approvals` | LeaveApproval | The approval records for this request |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, leave type, and date range |
| `save()` | Calculates total days before saving |
| `get_overlapping_requests()` | Returns leave requests that overlap with this one |

## Example

```python
leave_request = LeaveRequest.objects.create(
    employee=john_doe,
    leave_type=casual_leave,
    start_date="2023-05-15",
    end_date="2023-05-17",
    reason="Personal matters",
    half_day=False,
    status="pending"
)
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/leave-requests/` | GET | List all leave requests |
| `/api/v1/leave-requests/` | POST | Create a new leave request |
| `/api/v1/leave-requests/{id}/` | GET | Retrieve a specific leave request |
| `/api/v1/leave-requests/{id}/` | PUT | Update a specific leave request |
| `/api/v1/leave-requests/{id}/` | DELETE | Delete a specific leave request |
| `/api/v1/leave-requests/employee/{employee_id}/` | GET | List leave requests for a specific employee |
| `/api/v1/leave-requests/pending-requests/` | GET | List all pending leave requests |
| `/api/v1/leave-requests/approved-requests/` | GET | List all approved leave requests |
| `/api/v1/leave-requests/rejected-requests/` | GET | List all rejected leave requests |
| `/api/v1/leave-requests/cancelled-requests/` | GET | List all cancelled leave requests |
| `/api/v1/leave-requests/{id}/approve-request/` | POST | Approve a leave request |
| `/api/v1/leave-requests/{id}/reject-request/` | POST | Reject a leave request |
| `/api/v1/leave-requests/{id}/cancel-request/` | POST | Cancel a leave request |
| `/api/v1/leave-requests/date-range-requests/` | GET | List leave requests within a date range |

## Approve/Reject/Cancel API

### Approve Request

To approve a leave request, send a POST request to `/api/v1/leave-requests/{id}/approve-request/`.

### Reject Request

To reject a leave request, send a POST request to `/api/v1/leave-requests/{id}/reject-request/` with the following payload:

```json
{
  "rejection_reason": "Insufficient team coverage during this period"
}
```

### Cancel Request

To cancel a leave request, send a POST request to `/api/v1/leave-requests/{id}/cancel-request/`.

### Date Range Requests

To get leave requests within a date range, send a GET request to `/api/v1/leave-requests/date-range-requests/?start_date=2023-05-01&end_date=2023-05-31`.
