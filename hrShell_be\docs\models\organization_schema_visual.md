# Organization Module Schema Visualization

This document provides a visual representation of the organization module's database schema and the relationships between its models.

## Entity Relationship Diagram

```mermaid
classDiagram
    Organization "1" -- "0..*" Location : has
    Organization "1" -- "0..*" BusinessUnit : has
    Organization "1" -- "0..*" Designation : has
    Organization "1" -- "0..*" OrganizationPolicy : has
    Organization "1" -- "0..*" OrganizationDocument : has
    
    Location "0..*" -- "0..*" OrganizationPolicy : associated with
    BusinessUnit "0..*" -- "0..*" OrganizationPolicy : associated with
    Department "0..*" -- "0..*" OrganizationPolicy : associated with
    
    BusinessUnit "0..1" -- "0..*" BusinessUnit : parent of
    BusinessUnit "0..*" -- "0..1" Location : has primary
    BusinessUnit "0..*" -- "0..1" Employee : headed by
    
    Department "0..*" -- "0..1" BusinessUnit : belongs to
    Department "1" -- "0..*" Designation : has
    
    Employee "0..*" -- "1" Department : belongs to
    
    OrganizationDocument "0..*" -- "0..1" BusinessUnit : related to
    
    class Organization {
        +int id
        +string name
        +image logo
        +string registration_number
        +string gst_number
        +string pan_number
        +string ein_number
        +enum business_type
        +date establishment_date
        +string industry_sector
        +enum default_currency
        +string default_timezone
        +int fiscal_year_start_month
        +int fiscal_year_start_day
        +url website
        +email email
        +string phone
        +enum status
        +json custom_fields
        +datetime created_at
        +datetime updated_at
    }
    
    class Location {
        +int id
        +int organization_id
        +string name
        +string address_line1
        +string address_line2
        +string city
        +string state
        +string country
        +string postal_code
        +string phone
        +email email
        +decimal latitude
        +decimal longitude
        +time working_hours_start
        +time working_hours_end
        +string working_days
        +boolean is_headquarters
        +enum status
        +json custom_fields
        +datetime created_at
        +datetime updated_at
    }
    
    class BusinessUnit {
        +int id
        +int organization_id
        +string name
        +text description
        +string code
        +int parent_id
        +int head_id
        +int primary_location_id
        +enum status
        +datetime created_at
        +datetime updated_at
    }
    
    class Designation {
        +int id
        +int organization_id
        +string title
        +text description
        +enum level
        +int department_id
        +string pay_grade
        +decimal min_salary
        +decimal max_salary
        +datetime created_at
        +datetime updated_at
    }
    
    class OrganizationPolicy {
        +int id
        +int organization_id
        +string name
        +text description
        +enum policy_type
        +text content
        +boolean applicable_to_all
        +date effective_from
        +date effective_to
        +datetime created_at
        +datetime updated_at
    }
    
    class OrganizationDocument {
        +int id
        +int organization_id
        +string title
        +text description
        +enum document_type
        +file file
        +string version
        +date effective_date
        +date expiry_date
        +int business_unit_id
        +datetime created_at
        +datetime updated_at
    }
    
    class Department {
        +int id
        +string name
        +text description
        +int manager_id
        +enum status
        +int business_unit_id
        +datetime created_at
        +datetime updated_at
    }
    
    class Employee {
        +int id
        +string first_name
        +string last_name
        +email email
        +string phone
        +string position
        +int department_id
        +date hire_date
        +enum gender
        +enum status
        +image profile_picture
        +file resume
        +file contract
        +datetime created_at
        +datetime updated_at
    }
```

## Organizational Hierarchy

```mermaid
graph TD
    A[Organization] --> B[Business Unit 1]
    A --> C[Business Unit 2]
    A --> D[Business Unit 3]
    
    B --> E[Department 1]
    B --> F[Department 2]
    
    C --> G[Department 3]
    C --> H[Department 4]
    
    D --> I[Department 5]
    
    E --> J[Employees]
    F --> K[Employees]
    G --> L[Employees]
    H --> M[Employees]
    I --> N[Employees]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:1px
    style C fill:#bbf,stroke:#333,stroke-width:1px
    style D fill:#bbf,stroke:#333,stroke-width:1px
    style E fill:#ddf,stroke:#333,stroke-width:1px
    style F fill:#ddf,stroke:#333,stroke-width:1px
    style G fill:#ddf,stroke:#333,stroke-width:1px
    style H fill:#ddf,stroke:#333,stroke-width:1px
    style I fill:#ddf,stroke:#333,stroke-width:1px
    style J fill:#efe,stroke:#333,stroke-width:1px
    style K fill:#efe,stroke:#333,stroke-width:1px
    style L fill:#efe,stroke:#333,stroke-width:1px
    style M fill:#efe,stroke:#333,stroke-width:1px
    style N fill:#efe,stroke:#333,stroke-width:1px
```

## Location and Policy Relationships

```mermaid
graph LR
    A[Organization] --> B[Locations]
    A --> C[Policies]
    
    B --> D[Business Units]
    C --> D
    C --> E[Departments]
    
    D --> E
    E --> F[Employees]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:1px
    style C fill:#fbb,stroke:#333,stroke-width:1px
    style D fill:#ddf,stroke:#333,stroke-width:1px
    style E fill:#ddf,stroke:#333,stroke-width:1px
    style F fill:#efe,stroke:#333,stroke-width:1px
```
