<!DOCTYPE html>
<html>
<head>
    <title>HR Shell API Documentation</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui.css" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@4.5.0/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@4.5.0/favicon-16x16.png" sizes="16x16" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }

        *,
        *:before,
        *:after {
            box-sizing: inherit;
        }

        body {
            margin: 0;
            background: #fafafa;
        }

        .topbar {
            background-color: #1976D2;
            padding: 10px 0;
        }

        .swagger-ui .topbar {
            background-color: #1976D2;
        }

        .swagger-ui .topbar .download-url-wrapper .select-label {
            color: white;
        }

        .swagger-ui .info .title {
            color: #1976D2;
        }

        .swagger-ui .opblock.opblock-get {
            background: rgba(97, 175, 254, 0.1);
            border-color: #61affe;
        }

        .swagger-ui .opblock.opblock-post {
            background: rgba(73, 204, 144, 0.1);
            border-color: #49cc90;
        }

        .swagger-ui .opblock.opblock-put {
            background: rgba(252, 161, 48, 0.1);
            border-color: #fca130;
        }

        .swagger-ui .opblock.opblock-patch {
            background: rgba(80, 227, 194, 0.1);
            border-color: #50e3c2;
        }

        .swagger-ui .opblock.opblock-delete {
            background: rgba(249, 62, 62, 0.1);
            border-color: #f93e3e;
        }

        .swagger-ui .opblock .opblock-summary-method {
            font-weight: bold;
        }

        .swagger-ui .scheme-container {
            background-color: #f8f9fa;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
        }

        .swagger-ui section.models {
            border: 1px solid #e0e0e0;
        }

        .swagger-ui section.models.is-open h4 {
            border-bottom: 1px solid #e0e0e0;
        }

        .swagger-ui .btn.authorize {
            background-color: #1976D2;
            color: #fff;
            border-color: #1976D2;
        }

        .swagger-ui .btn.authorize svg {
            fill: #fff;
        }

        /* Custom header */
        .custom-header {
            background-color: #1976D2;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .custom-header h1 {
            margin: 0;
            font-size: 24px;
        }

        .custom-header p {
            margin: 10px 0 0;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="custom-header">
        <h1>HR Shell API Documentation</h1>
        <p>Comprehensive API for Human Resource Management</p>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: "{{ schema_url }}",
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "BaseLayout",
                docExpansion: "none",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                displayRequestDuration: true,
                filter: true,
                syntaxHighlight: {
                    activate: true,
                    theme: "agate"
                },
                requestInterceptor: (request) => {
                    // Add CSRF token if needed
                    return request;
                }
            });

            window.ui = ui;
        };
    </script>
</body>
</html>
