from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from datetime import timedelta
from payroll_app.models.loan import Loan, LoanInstallment
from payroll_app.serializers.loan_serializer import LoanSerializer, LoanInstallmentSerializer
from payroll_app.filters.payroll_filters import <PERSON>anFilter, LoanInstallmentFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class LoanViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for loans.
    """
    queryset = Loan.objects.all()
    serializer_class = LoanSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LoanFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email', 'purpose']
    ordering_fields = ['employee__first_name', 'loan_type', 'loan_amount', 'start_date', 'status', 'created_at']
    ordering = ['-start_date']

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all loans with a specific status.
        """
        loans = Loan.objects.filter(status=status_value)
        page = self.paginate_queryset(loans)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(loans, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all loans for a specific employee.
        """
        loans = Loan.objects.filter(employee_id=employee_pk)
        page = self.paginate_queryset(loans)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(loans, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get all active loans.
        """
        loans = Loan.objects.filter(status='active')
        page = self.paginate_queryset(loans)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(loans, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def installments(self, request, pk=None):
        """
        Get all installments for a specific loan.
        """
        loan = self.get_object()
        installments = loan.installments.all()
        serializer = LoanInstallmentSerializer(installments, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """
        Approve a specific loan.
        """
        loan = self.get_object()

        if loan.status != 'pending':
            return Response(
                {"detail": "Only pending loans can be approved."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update loan status
        loan.status = 'approved'
        loan.approval_date = timezone.now().date()

        # Set the approver if provided
        approver_id = request.data.get('approved_by')
        if approver_id:
            loan.approved_by_id = approver_id

        loan.save()

        serializer = self.get_serializer(loan)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """
        Reject a specific loan.
        """
        loan = self.get_object()

        if loan.status != 'pending':
            return Response(
                {"detail": "Only pending loans can be rejected."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update loan status
        loan.status = 'rejected'

        # Set the rejection reason if provided
        rejection_reason = request.data.get('rejection_reason')
        if rejection_reason:
            loan.rejection_reason = rejection_reason

        # Set the approver if provided
        approver_id = request.data.get('approved_by')
        if approver_id:
            loan.approved_by_id = approver_id

        loan.save()

        serializer = self.get_serializer(loan)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """
        Activate a specific loan and generate installments.
        """
        loan = self.get_object()

        if loan.status != 'approved':
            return Response(
                {"detail": "Only approved loans can be activated."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update loan status
        loan.status = 'active'
        loan.save()

        # Generate installments
        self._generate_installments(loan)

        serializer = self.get_serializer(loan)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def close(self, request, pk=None):
        """
        Close a specific loan.
        """
        loan = self.get_object()

        if loan.status != 'active':
            return Response(
                {"detail": "Only active loans can be closed."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if all installments are paid
        unpaid_installments = loan.installments.exclude(status='paid').count()
        if unpaid_installments > 0:
            return Response(
                {"detail": f"Cannot close loan. {unpaid_installments} installments are still unpaid."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update loan status
        loan.status = 'closed'
        loan.remaining_amount = 0
        loan.save()

        serializer = self.get_serializer(loan)
        return Response(serializer.data)

    def _generate_installments(self, loan):
        """
        Generate installments for a loan.
        """
        # Delete any existing installments
        loan.installments.all().delete()

        # Calculate installment details
        principal_per_installment = loan.loan_amount / loan.term_months

        if loan.interest_rate > 0:
            # Simple interest calculation (this is simplified)
            interest_per_installment = (loan.loan_amount * loan.interest_rate / 100) / loan.term_months
        else:
            interest_per_installment = 0

        # Generate installments
        current_date = loan.start_date
        for i in range(1, loan.term_months + 1):
            LoanInstallment.objects.create(
                loan=loan,
                installment_number=i,
                due_date=current_date,
                amount=loan.emi_amount,
                principal_amount=principal_per_installment,
                interest_amount=interest_per_installment,
                status='pending'
            )

            # Move to next month
            current_date = self._add_month(current_date)

    def _add_month(self, date):
        """
        Add one month to a date.
        """
        month = date.month
        year = date.year

        # Move to next month
        month += 1
        if month > 12:
            month = 1
            year += 1

        # Handle different month lengths
        day = min(date.day, self._days_in_month(year, month))

        return date.replace(year=year, month=month, day=day)

    def _days_in_month(self, year, month):
        """
        Get the number of days in a month.
        """
        if month == 2:
            if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0):
                return 29
            return 28
        elif month in [4, 6, 9, 11]:
            return 30
        else:
            return 31


class LoanInstallmentViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for loan installments.
    """
    queryset = LoanInstallment.objects.all()
    serializer_class = LoanInstallmentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LoanInstallmentFilter
    search_fields = ['loan__employee__first_name', 'loan__employee__last_name', 'remarks']
    ordering_fields = ['loan__employee__first_name', 'installment_number', 'due_date', 'amount', 'status', 'created_at']
    ordering = ['loan', 'installment_number']

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all installments with a specific status.
        """
        installments = LoanInstallment.objects.filter(status=status_value)
        page = self.paginate_queryset(installments)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(installments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def loan(self, request, loan_pk=None):
        """
        Get all installments for a specific loan.
        """
        installments = LoanInstallment.objects.filter(loan_id=loan_pk)
        page = self.paginate_queryset(installments)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(installments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all installments for a specific employee.
        """
        installments = LoanInstallment.objects.filter(loan__employee_id=employee_pk)
        page = self.paginate_queryset(installments)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(installments, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def due(self, request):
        """
        Get all installments due in the current month.
        """
        today = timezone.now().date()
        start_of_month = today.replace(day=1)
        end_of_month = self._last_day_of_month(today)

        installments = LoanInstallment.objects.filter(
            due_date__range=[start_of_month, end_of_month],
            status='pending'
        )

        page = self.paginate_queryset(installments)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(installments, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def pay(self, request, pk=None):
        """
        Mark an installment as paid.
        """
        installment = self.get_object()

        if installment.status != 'pending':
            return Response(
                {"detail": "Only pending installments can be marked as paid."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get payment amount
        paid_amount = request.data.get('paid_amount', installment.amount)

        # Update installment
        installment.paid_amount = paid_amount
        installment.payment_date = timezone.now().date()

        # Determine status based on payment amount
        if paid_amount >= installment.amount:
            installment.status = 'paid'
        else:
            installment.status = 'partial'

        installment.save()

        # Update loan remaining amount
        loan = installment.loan
        loan.remaining_amount -= paid_amount
        if loan.remaining_amount <= 0:
            loan.remaining_amount = 0
            loan.status = 'closed'
        loan.save()

        serializer = self.get_serializer(installment)
        return Response(serializer.data)

    def _last_day_of_month(self, date):
        """
        Get the last day of the month for a given date.
        """
        if date.month == 12:
            return date.replace(day=31)
        return date.replace(month=date.month + 1, day=1) - timedelta(days=1)
