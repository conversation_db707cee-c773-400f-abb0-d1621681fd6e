from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Avg, Sum, Count, F, Q
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from employees_app.models.job_history import JobHistory
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from employees_app.serializers.job_history_serializer import JobHistorySerializer
from employees_app.filters import JobH<PERSON>oryFilter
from datetime import datetime, timedelta


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class JobHistoryViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Employee Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing JobHistory instances.

    Additional actions:
    - employee_history: Get job history for a specific employee
    - department_history: Get job history for employees in a specific department
    - date_range_history: Get job history within a date range
    - job_history_statistics: Get statistics about job history
    """
    queryset = JobHistory.objects.all()
    serializer_class = JobHistorySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = JobHistoryFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'previous_job_title']
    ordering_fields = ['start_date', 'end_date']

    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_history(self, request, employee_id=None):
        """
        Get all job history records for a specific employee
        """
        try:
            employee = Employee.objects.get(pk=employee_id)
        except Employee.DoesNotExist:
            return Response(
                {"detail": "Employee not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        history_records = self.queryset.filter(employee=employee)
        page = self.paginate_queryset(history_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(history_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_history(self, request, department_id=None):
        """
        Get all job history records for employees in a specific department
        """
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return Response(
                {"detail": "Department not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all employees in the department
        employees = Employee.objects.filter(department=department)

        # Get job history records for these employees
        history_records = self.queryset.filter(employee__in=employees)
        page = self.paginate_queryset(history_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(history_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def date_range_history(self, request):
        """
        Get all job history records within a date range
        """
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)

        if not start_date or not end_date:
            return Response(
                {"detail": "Both start_date and end_date are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"detail": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get job history records that overlap with the date range
        history_records = self.queryset.filter(
            # Job history starts within the range or before the range and ends within or after the range
            (
                (start_date <= F('start_date')) & (F('start_date') <= end_date)
            ) | (
                (F('start_date') <= start_date) & (start_date <= F('end_date'))
            )
        )

        page = self.paginate_queryset(history_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(history_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def job_history_statistics(self, request):
        """
        Get statistics about job history
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)
        year = request.query_params.get('year', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Filter by year if provided
        if year:
            try:
                year = int(year)
                queryset = queryset.filter(
                    Q(start_date__year=year) | Q(end_date__year=year)
                )
            except ValueError:
                return Response(
                    {"detail": "Invalid year format"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Calculate statistics
        total_records = queryset.count()
        unique_employees = queryset.values('employee').distinct().count()

        # Get average job duration in days
        job_durations = []
        for record in queryset:
            duration = (record.end_date - record.start_date).days
            job_durations.append(duration)

        avg_duration = sum(job_durations) / len(job_durations) if job_durations else 0

        # Get statistics by department
        departments = Department.objects.all()
        department_stats = []

        for dept in departments:
            dept_name = dept.name
            dept_records = queryset.filter(previous_department=dept_name)

            if dept_records.exists():
                department_stats.append({
                    'department_id': dept.id,
                    'department_name': dept_name,
                    'total_records': dept_records.count(),
                    'unique_employees': dept_records.values('employee').distinct().count()
                })

        # Compile statistics
        stats = {
            'total_records': total_records,
            'unique_employees': unique_employees,
            'average_job_duration_days': avg_duration,
            'department_statistics': department_stats
        }

        return Response(stats)
