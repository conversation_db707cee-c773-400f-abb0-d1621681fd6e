from rest_framework import serializers
from employees_app.models.employee_leave import EmployeeLeave


class EmployeeLeaveSerializer(serializers.ModelSerializer):
    """
    Serializer for the EmployeeLeave model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    approved_by_name = serializers.ReadOnlyField(source='approved_by.full_name')
    leave_type_display = serializers.ReadOnlyField(source='get_leave_type_display')
    status_display = serializers.ReadOnlyField(source='get_status_display')

    class Meta:
        model = EmployeeLeave
        fields = [
            'id', 'employee', 'employee_name', 'leave_type', 'leave_type_display',
            'start_date', 'end_date', 'total_days', 'reason', 'status', 'status_display',
            'approved_by', 'approved_by_name', 'created_at'
        ]
        read_only_fields = ['total_days', 'created_at']

    def validate(self, data):
        """
        Validate that end_date is after or equal to start_date
        """
        if 'start_date' in data and 'end_date' in data:
            if data['end_date'] < data['start_date']:
                raise serializers.ValidationError("End date must be after or equal to start date")
        return data
