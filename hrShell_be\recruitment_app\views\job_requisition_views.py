from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from recruitment_app.models.job_requisition import JobRequisition
from recruitment_app.serializers.job_requisition_serializer import JobRequisitionSerializer
from recruitment_app.filters.recruitment_filters import JobRequisitionFilter


class JobRequisitionViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for job requisitions.
    """
    queryset = JobRequisition.objects.all()
    serializer_class = JobRequisitionSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = JobRequisitionFilter
    search_fields = ['title', 'code', 'description', 'requirements', 'responsibilities', 'qualifications', 'skills_required']
    ordering_fields = ['requested_date', 'target_hiring_date', 'title', 'priority', 'status', 'created_at']
    ordering = ['-requested_date']

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all job requisitions with a specific status.
        """
        requisitions = JobRequisition.objects.filter(status=status_value)
        page = self.paginate_queryset(requisitions)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(requisitions, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all job requisitions for a specific organization.
        """
        requisitions = JobRequisition.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(requisitions)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(requisitions, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def department(self, request, department_pk=None):
        """
        Get all job requisitions for a specific department.
        """
        requisitions = JobRequisition.objects.filter(department_id=department_pk)
        page = self.paginate_queryset(requisitions)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(requisitions, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def location(self, request, location_pk=None):
        """
        Get all job requisitions for a specific location.
        """
        requisitions = JobRequisition.objects.filter(location_id=location_pk)
        page = self.paginate_queryset(requisitions)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(requisitions, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def requested_by(self, request, employee_pk=None):
        """
        Get all job requisitions requested by a specific employee.
        """
        requisitions = JobRequisition.objects.filter(requested_by_id=employee_pk)
        page = self.paginate_queryset(requisitions)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(requisitions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def submit_for_approval(self, request, pk=None):
        """
        Submit a job requisition for approval.
        """
        requisition = self.get_object()

        if requisition.status != 'draft':
            return Response(
                {"detail": "Only draft requisitions can be submitted for approval."},
                status=status.HTTP_400_BAD_REQUEST
            )

        requisition.status = 'pending_approval'
        requisition.save()

        serializer = self.get_serializer(requisition)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """
        Approve a job requisition.
        """
        requisition = self.get_object()

        if requisition.status != 'pending_approval':
            return Response(
                {"detail": "Only pending requisitions can be approved."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update requisition status
        requisition.status = 'approved'
        requisition.approval_date = timezone.now().date()

        # Set the approver if provided
        approver_id = request.data.get('approved_by')
        if approver_id:
            requisition.approved_by_id = approver_id

        requisition.save()

        serializer = self.get_serializer(requisition)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """
        Reject a job requisition.
        """
        requisition = self.get_object()

        if requisition.status != 'pending_approval':
            return Response(
                {"detail": "Only pending requisitions can be rejected."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update requisition status
        requisition.status = 'rejected'

        # Set the rejection reason if provided
        rejection_reason = request.data.get('rejection_reason')
        if rejection_reason:
            requisition.rejection_reason = rejection_reason

        # Set the approver if provided
        approver_id = request.data.get('approved_by')
        if approver_id:
            requisition.approved_by_id = approver_id

        requisition.save()

        serializer = self.get_serializer(requisition)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def put_on_hold(self, request, pk=None):
        """
        Put a job requisition on hold.
        """
        requisition = self.get_object()

        if requisition.status not in ['approved', 'pending_approval']:
            return Response(
                {"detail": "Only approved or pending requisitions can be put on hold."},
                status=status.HTTP_400_BAD_REQUEST
            )

        requisition.status = 'on_hold'
        requisition.save()

        serializer = self.get_serializer(requisition)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel a job requisition.
        """
        requisition = self.get_object()

        if requisition.status in ['closed', 'cancelled']:
            return Response(
                {"detail": "Requisition is already closed or cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )

        requisition.status = 'cancelled'
        requisition.save()

        serializer = self.get_serializer(requisition)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def close(self, request, pk=None):
        """
        Close a job requisition.
        """
        requisition = self.get_object()

        if requisition.status in ['closed', 'cancelled']:
            return Response(
                {"detail": "Requisition is already closed or cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )

        requisition.status = 'closed'
        requisition.save()

        serializer = self.get_serializer(requisition)
        return Response(serializer.data)
