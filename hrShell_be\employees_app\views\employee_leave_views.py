from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Avg, Sum, Count, F, Q
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from employees_app.models.employee_leave import EmployeeLeave
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from employees_app.serializers.employee_leave_serializer import EmployeeLeaveSerializer
from employees_app.filters import EmployeeLeaveFilter
from datetime import datetime, timedelta


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class EmployeeLeaveViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Employee Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing EmployeeLeave instances.

    Additional actions:
    - employee_leaves: Get leave records for a specific employee
    - department_leaves: Get leave records for employees in a specific department
    - date_range_leaves: Get leave records within a date range
    - pending_leaves: Get all pending leave requests
    - approve_leave: Approve a leave request (admin only)
    - reject_leave: Reject a leave request (admin only)
    - leave_statistics: Get statistics about leave requests
    """
    queryset = EmployeeLeave.objects.all()
    serializer_class = EmployeeLeaveSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = EmployeeLeaveFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'reason']
    ordering_fields = ['start_date', 'end_date', 'created_at']

    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_leaves(self, request, employee_id=None):
        """
        Get all leave records for a specific employee
        """
        try:
            employee = Employee.objects.get(pk=employee_id)
        except Employee.DoesNotExist:
            return Response(
                {"detail": "Employee not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        leaves = self.queryset.filter(employee=employee)
        page = self.paginate_queryset(leaves)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(leaves, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_leaves(self, request, department_id=None):
        """
        Get all leave records for employees in a specific department
        """
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return Response(
                {"detail": "Department not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all employees in the department
        employees = Employee.objects.filter(department=department)

        # Get leave records for these employees
        leaves = self.queryset.filter(employee__in=employees)
        page = self.paginate_queryset(leaves)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(leaves, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def date_range_leaves(self, request):
        """
        Get all leave records within a date range
        """
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)

        if not start_date or not end_date:
            return Response(
                {"detail": "Both start_date and end_date are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"detail": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get leaves that overlap with the date range
        leaves = self.queryset.filter(
            # Leave starts within the range or before the range and ends within or after the range
            (
                (start_date <= F('start_date')) & (F('start_date') <= end_date)
            ) | (
                (F('start_date') <= start_date) & (start_date <= F('end_date'))
            )
        )

        page = self.paginate_queryset(leaves)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(leaves, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def pending_leaves(self, request):
        """
        Get all pending leave requests
        """
        leaves = self.queryset.filter(status='pending')
        page = self.paginate_queryset(leaves)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(leaves, many=True)
        return Response(serializer.data)

    def approve_leave(self, request, pk=None):
        """
        Approve a leave request (admin only)
        """
        leave = self.get_object()

        if leave.status == 'approved':
            return Response(
                {"detail": "This leave request is already approved"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Set the approved_by field to the current user if it's an admin
        if request.user.is_staff:
            try:
                admin_employee = Employee.objects.get(email=request.user.email)
                leave.approved_by = admin_employee
                leave.status = 'approved'
                leave.save()
                serializer = self.get_serializer(leave)
                return Response(serializer.data)
            except Employee.DoesNotExist:
                return Response(
                    {"detail": "Admin user is not linked to an employee record"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            return Response(
                {"detail": "Only admin users can approve leave requests"},
                status=status.HTTP_403_FORBIDDEN
            )

    def reject_leave(self, request, pk=None):
        """
        Reject a leave request (admin only)
        """
        leave = self.get_object()

        if leave.status == 'rejected':
            return Response(
                {"detail": "This leave request is already rejected"},
                status=status.HTTP_400_BAD_REQUEST
            )

        leave.status = 'rejected'
        leave.save()
        serializer = self.get_serializer(leave)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def leave_statistics(self, request):
        """
        Get statistics about leave requests
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)
        year = request.query_params.get('year', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Filter by year if provided
        if year:
            try:
                year = int(year)
                queryset = queryset.filter(
                    Q(start_date__year=year) | Q(end_date__year=year)
                )
            except ValueError:
                return Response(
                    {"detail": "Invalid year format"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Calculate statistics
        total_leaves = queryset.count()
        pending_leaves = queryset.filter(status='pending').count()
        approved_leaves = queryset.filter(status='approved').count()
        rejected_leaves = queryset.filter(status='rejected').count()

        # Calculate total days by leave type
        leave_types = queryset.values('leave_type').annotate(
            count=Count('id'),
            total_days=Sum('total_days')
        )

        # Get statistics by department
        departments = Department.objects.all()
        department_stats = []

        for dept in departments:
            employees = Employee.objects.filter(department=dept)
            dept_leaves = queryset.filter(employee__in=employees)

            if dept_leaves.exists():
                department_stats.append({
                    'department_id': dept.id,
                    'department_name': dept.name,
                    'total_leaves': dept_leaves.count(),
                    'pending_leaves': dept_leaves.filter(status='pending').count(),
                    'approved_leaves': dept_leaves.filter(status='approved').count(),
                    'rejected_leaves': dept_leaves.filter(status='rejected').count(),
                    'total_days': dept_leaves.aggregate(Sum('total_days'))['total_days__sum'] or 0
                })

        # Compile statistics
        stats = {
            'total_leaves': total_leaves,
            'pending_leaves': pending_leaves,
            'approved_leaves': approved_leaves,
            'rejected_leaves': rejected_leaves,
            'leave_types': leave_types,
            'department_statistics': department_stats
        }

        return Response(stats)
