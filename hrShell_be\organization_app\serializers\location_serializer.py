from rest_framework import serializers
from organization_app.models.location import Location, LocationStatus
from employees_app.serializers.employee_serializer import EmployeeListSerializer


class LocationSerializer(serializers.ModelSerializer):
    """
    Serializer for the Location model
    """
    organization_name = serializers.ReadOnlyField(source='organization.name')
    status_display = serializers.ReadOnlyField(source='get_status_display')
    
    class Meta:
        model = Location
        fields = [
            'id', 'name', 'organization', 'organization_name', 'address_line1',
            'address_line2', 'city', 'state', 'country', 'postal_code',
            'phone', 'email', 'latitude', 'longitude', 'working_hours_start',
            'working_hours_end', 'working_days', 'is_headquarters', 'status',
            'status_display', 'custom_fields', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_status(self, value):
        """
        Validate that the status is one of the allowed choices
        """
        try:
            LocationStatus(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError(
                f"Status must be one of: {', '.join([s.value for s in LocationStatus])}"
            )
    
    def validate(self, data):
        """
        Validate that the location name is unique within the organization
        """
        organization = data.get('organization')
        name = data.get('name')
        
        # Skip validation if organization or name is not provided
        if not organization or not name:
            return data
        
        # Check if we're updating an existing instance
        instance = getattr(self, 'instance', None)
        if instance and instance.organization == organization and instance.name == name:
            return data
        
        # Check if a location with the same name already exists in the organization
        if Location.objects.filter(organization=organization, name=name).exists():
            raise serializers.ValidationError(
                {"name": "A location with this name already exists in the organization."}
            )
        
        return data


class LocationDetailSerializer(LocationSerializer):
    """
    Detailed serializer for the Location model including related entities
    """
    business_units = serializers.SerializerMethodField()
    employee_count = serializers.SerializerMethodField()
    
    class Meta(LocationSerializer.Meta):
        fields = LocationSerializer.Meta.fields + [
            'business_units', 'employee_count'
        ]
    
    def get_business_units(self, obj):
        from organization_app.serializers.business_unit_serializer import BusinessUnitSerializer
        business_units = obj.business_units.all()
        return BusinessUnitSerializer(business_units, many=True).data
    
    def get_employee_count(self, obj):
        # This is a placeholder. You'll need to implement a way to count employees by location
        # This might require adding a location field to the Employee model
        return 0
