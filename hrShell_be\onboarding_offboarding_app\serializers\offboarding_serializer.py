from rest_framework import serializers
from onboarding_offboarding_app.models.offboarding import OffboardingRequest, OffboardingTask
from employees_app.serializers.employee_serializer import EmployeeSerializer
from organization_app.serializers.department_serializer import DepartmentSerializer


class OffboardingTaskSerializer(serializers.ModelSerializer):
    """
    Serializer for the OffboardingTask model.
    """
    assigned_to_details = EmployeeSerializer(source='assigned_to', read_only=True)
    completed_by_details = EmployeeSerializer(source='completed_by', read_only=True)
    
    class Meta:
        model = OffboardingTask
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate the offboarding task data.
        """
        # Validate due date is not after request last working day for pre-exit tasks
        due_date = data.get('due_date')
        request = data.get('request')
        category = data.get('category')
        
        if due_date and request and category in ['knowledge_transfer', 'exit_interview'] and due_date > request.last_working_day:
            raise serializers.ValidationError("Due date for pre-exit tasks cannot be after the last working day.")
        
        return data


class OffboardingRequestSerializer(serializers.ModelSerializer):
    """
    Serializer for the OffboardingRequest model.
    """
    employee_details = EmployeeSerializer(source='employee', read_only=True)
    department_details = DepartmentSerializer(source='department', read_only=True)
    manager_details = EmployeeSerializer(source='manager', read_only=True)
    approved_by_details = EmployeeSerializer(source='approved_by', read_only=True)
    tasks = OffboardingTaskSerializer(many=True, read_only=True)
    
    class Meta:
        model = OffboardingRequest
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate the offboarding request data.
        """
        # Validate last working day is not before resignation date
        resignation_date = data.get('resignation_date')
        last_working_day = data.get('last_working_day')
        
        if resignation_date and last_working_day and resignation_date > last_working_day:
            raise serializers.ValidationError("Last working day cannot be before the resignation date.")
        
        return data
    
    def create(self, validated_data):
        """
        Create a new offboarding request.
        """
        # Create the offboarding request
        offboarding_request = OffboardingRequest.objects.create(**validated_data)
        
        return offboarding_request
