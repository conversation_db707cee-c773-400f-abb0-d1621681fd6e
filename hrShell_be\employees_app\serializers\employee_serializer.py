from rest_framework import serializers
from employees_app.models.employee import Employee, Gender, Status
from django.utils import timezone


class EmployeeSerializer(serializers.ModelSerializer):
    """
    Serializer for the Employee model
    """
    department_name = serializers.ReadOnlyField(source='department.name')
    full_name = serializers.ReadOnlyField()
    hire_date = serializers.DateField(required=False)

    class Meta:
        model = Employee
        fields = [
            'id', 'first_name', 'last_name', 'full_name', 'email', 'phone',
            'position', 'department', 'department_name', 'hire_date',
            'gender', 'status', 'profile_picture', 'resume', 'contract',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_email(self, value):
        """
        Validate that the email is unique
        """
        if self.instance and self.instance.email == value:
            return value

        if Employee.objects.filter(email=value).exists():
            raise serializers.ValidationError("This email is already in use.")
        return value

    def validate_gender(self, value):
        """
        Validate that the gender is one of the allowed choices
        """
        try:
            Gender(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError("Gender must be one of: male, female, or other")

    def validate_status(self, value):
        """
        Validate that the status is one of the allowed choices
        """
        try:
            Status(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError("Status must be one of: active or inactive")

    def validate_hire_date(self, value):
        """
        Ensure hire_date is a date object, not a datetime
        """
        if hasattr(value, 'date'):
            return value.date()
        return value


class EmployeeListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for listing employees
    """
    department_name = serializers.ReadOnlyField(source='department.name')
    full_name = serializers.ReadOnlyField()

    class Meta:
        model = Employee
        fields = [
            'id', 'full_name', 'email', 'position',
            'department_name', 'status', 'profile_picture'
        ]
