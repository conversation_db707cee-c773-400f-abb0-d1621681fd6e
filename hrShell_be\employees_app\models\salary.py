from django.db import models


class PaymentMethod(models.TextChoices):
    BANK_TRANSFER = 'bank_transfer', 'Bank Transfer'
    CHECK = 'check', 'Check'
    CASH = 'cash', 'Cash'


class Salary(models.Model):
    """
    Salary model for tracking employee salaries
    """
    employee = models.ForeignKey(
        'Employee',
        on_delete=models.CASCADE,
        related_name='salaries'
    )
    basic = models.DecimalField(max_digits=10, decimal_places=2)
    allowance = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    deduction = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_amount_payable = models.DecimalField(max_digits=10, decimal_places=2, editable=False)
    payment_method = models.CharField(
        max_length=20,
        choices=PaymentMethod.choices,
        default=PaymentMethod.BANK_TRANSFER
    )
    bank_account_details = models.TextField(blank=True, null=True)
    salary_effective_date = models.DateField()

    class Meta:
        ordering = ['-salary_effective_date']
        verbose_name = "Salary"
        verbose_name_plural = "Salaries"

    def __str__(self):
        return f"{self.employee} - {self.basic} ({self.salary_effective_date})"

    def save(self, *args, **kwargs):
        # Calculate net amount payable
        self.net_amount_payable = self.basic + self.allowance - self.deduction
        super().save(*args, **kwargs)
