from django.urls import path, include
from rest_framework_simplejwt.views import (
    TokenRefreshView,
    TokenVerifyView,
)
from employees_app.serializers.token_serializer import CustomTokenObtainPairView
from employees_app.views.auth_views import RegisterView, LoginView
from common.views.ai_assistant_views import VectorChatView
from common.views.health_check_view import HealthCheckView
from common.swagger import urlpatterns as swagger_urls

urlpatterns = [
    path('api/v1/', include('employees_app.urls')),
    path('api/v1/', include('organization_app.urls')),
    path('api/v1/', include('leave_app.urls')),
    path('api/v1/', include('payroll_app.urls')),
    path('api/v1/', include('recruitment_app.urls')),
    path('api/v1/', include('onboarding_offboarding_app.urls')),

    # AI Assistant endpoints
    path('api/v1/ai/chat/', VectorChatView.as_view(), name='vector_chat'),

    # Authentication endpoints
    path('api/auth/login/', LoginView.as_view(), name='login'),  # New enhanced login endpoint
    path('api/auth/register/', RegisterView.as_view(), name='register'),

    # Legacy authentication endpoints (for backward compatibility)
    path('api/register/', RegisterView.as_view(), name='register_legacy'),
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('api-auth/', include('rest_framework.urls')),

    # Health check endpoint
    path('health/', HealthCheckView.as_view(), name='health_check'),
]

# Add Swagger URLs
urlpatterns += swagger_urls

# Add a redirect from /swagger/ to /swagger-ui/ for backward compatibility
from django.views.generic import RedirectView
urlpatterns += [
    path('swagger/', RedirectView.as_view(url='/swagger-ui/', permanent=False), name='swagger-redirect'),
]
