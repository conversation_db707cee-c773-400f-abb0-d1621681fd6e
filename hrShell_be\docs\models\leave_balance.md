# LeaveBalance Model

The LeaveBalance model represents employee leave balances for each leave type and year.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `employee` | ForeignKey | Employee this balance belongs to | Yes |
| `leave_type` | ForeignKey | Leave type this balance is for | Yes |
| `year` | Integer | Year for which this balance applies | Yes (default: current year) |
| `opening_balance` | Decimal | Opening balance at the start of the year | Yes (default: 0) |
| `accrued` | Decimal | Leave days accrued during the year | Yes (default: 0) |
| `used` | Decimal | Leave days used during the year | Yes (default: 0) |
| `adjusted` | Decimal | Manual adjustments to leave balance | Yes (default: 0) |
| `encashed` | Decimal | Leave days encashed during the year | Yes (default: 0) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `employee` | Employee | The employee this balance belongs to |
| `leave_type` | LeaveType | The leave type this balance is for |

## Properties

| Property | Type | Description |
|----------|------|-------------|
| `closing_balance` | Decimal | Calculated closing balance (opening_balance + accrued - used + adjusted - encashed) |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, leave type, and year |

## Example

```python
leave_balance = LeaveBalance.objects.create(
    employee=john_doe,
    leave_type=casual_leave,
    year=2023,
    opening_balance=12.0,
    accrued=0.0,
    used=0.0,
    adjusted=0.0,
    encashed=0.0
)
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/leave-balances/` | GET | List all leave balances |
| `/api/v1/leave-balances/` | POST | Create a new leave balance |
| `/api/v1/leave-balances/{id}/` | GET | Retrieve a specific leave balance |
| `/api/v1/leave-balances/{id}/` | PUT | Update a specific leave balance |
| `/api/v1/leave-balances/{id}/` | DELETE | Delete a specific leave balance |
| `/api/v1/leave-balances/employee/{employee_id}/` | GET | List leave balances for a specific employee |
| `/api/v1/leave-balances/employee/{employee_id}/year/{year}/` | GET | List leave balances for a specific employee and year |
| `/api/v1/leave-balances/current-year-balances/` | GET | List leave balances for the current year |
| `/api/v1/leave-balances/{id}/adjust-balance/` | POST | Adjust a leave balance |

## Adjust Balance API

To adjust a leave balance, send a POST request to `/api/v1/leave-balances/{id}/adjust-balance/` with the following payload:

```json
{
  "adjustment": 2.0,
  "reason": "Additional leave granted for exceptional performance"
}
```

- `adjustment`: The amount to adjust the balance by (positive or negative)
- `reason`: The reason for the adjustment (optional)
