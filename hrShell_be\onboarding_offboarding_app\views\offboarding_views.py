from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from onboarding_offboarding_app.models.offboarding import OffboardingRequest, OffboardingTask
from onboarding_offboarding_app.serializers.offboarding_serializer import OffboardingRequestSerializer, OffboardingTaskSerializer
from onboarding_offboarding_app.filters.onboarding_offboarding_filters import OffboardingRequestFilter, OffboardingTaskFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class OffboardingRequestViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for offboarding requests.
    """
    queryset = OffboardingRequest.objects.all()
    serializer_class = OffboardingRequestSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OffboardingRequestFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email', 'exit_reason', 'notes']
    ordering_fields = ['resignation_date', 'last_working_day', 'status', 'created_at']
    ordering = ['-resignation_date']
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all offboarding requests with a specific status.
        """
        requests = OffboardingRequest.objects.filter(status=status_value)
        page = self.paginate_queryset(requests)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all offboarding requests for a specific employee.
        """
        requests = OffboardingRequest.objects.filter(employee_id=employee_pk)
        page = self.paginate_queryset(requests)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def department(self, request, department_pk=None):
        """
        Get all offboarding requests for a specific department.
        """
        requests = OffboardingRequest.objects.filter(department_id=department_pk)
        page = self.paginate_queryset(requests)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def manager(self, request, manager_pk=None):
        """
        Get all offboarding requests for a specific manager.
        """
        requests = OffboardingRequest.objects.filter(manager_id=manager_pk)
        page = self.paginate_queryset(requests)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def tasks(self, request, pk=None):
        """
        Get all tasks for a specific offboarding request.
        """
        offboarding_request = self.get_object()
        tasks = OffboardingTask.objects.filter(request=offboarding_request)
        
        serializer = OffboardingTaskSerializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """
        Approve an offboarding request.
        """
        offboarding_request = self.get_object()
        
        if offboarding_request.status != 'submitted':
            return Response(
                {"detail": "Only submitted offboarding requests can be approved."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update request status
        offboarding_request.status = 'approved'
        offboarding_request.approval_date = timezone.now().date()
        
        # Set the approver if provided
        approver_id = request.data.get('approved_by')
        if approver_id:
            offboarding_request.approved_by_id = approver_id
        
        offboarding_request.save()
        
        serializer = self.get_serializer(offboarding_request)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """
        Reject an offboarding request.
        """
        offboarding_request = self.get_object()
        
        if offboarding_request.status != 'submitted':
            return Response(
                {"detail": "Only submitted offboarding requests can be rejected."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update request status
        offboarding_request.status = 'rejected'
        
        # Set the approver if provided
        approver_id = request.data.get('approved_by')
        if approver_id:
            offboarding_request.approved_by_id = approver_id
        
        offboarding_request.save()
        
        serializer = self.get_serializer(offboarding_request)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """
        Start an offboarding process.
        """
        offboarding_request = self.get_object()
        
        if offboarding_request.status != 'approved':
            return Response(
                {"detail": "Only approved offboarding requests can be started."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        offboarding_request.status = 'in_progress'
        offboarding_request.save()
        
        serializer = self.get_serializer(offboarding_request)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """
        Complete an offboarding process.
        """
        offboarding_request = self.get_object()
        
        if offboarding_request.status != 'in_progress':
            return Response(
                {"detail": "Only in-progress offboarding requests can be completed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if all mandatory tasks are completed
        incomplete_mandatory_tasks = OffboardingTask.objects.filter(
            request=offboarding_request,
            is_mandatory=True
        ).exclude(status__in=['completed', 'not_applicable'])
        
        if incomplete_mandatory_tasks.exists():
            return Response(
                {"detail": "Cannot complete offboarding process. There are incomplete mandatory tasks."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        offboarding_request.status = 'completed'
        offboarding_request.save()
        
        serializer = self.get_serializer(offboarding_request)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel an offboarding process.
        """
        offboarding_request = self.get_object()
        
        if offboarding_request.status == 'completed':
            return Response(
                {"detail": "Completed offboarding requests cannot be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        offboarding_request.status = 'cancelled'
        offboarding_request.save()
        
        # Cancel all pending tasks
        OffboardingTask.objects.filter(request=offboarding_request).exclude(status__in=['completed', 'cancelled']).update(status='cancelled')
        
        serializer = self.get_serializer(offboarding_request)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def schedule_exit_interview(self, request, pk=None):
        """
        Schedule an exit interview for an employee.
        """
        offboarding_request = self.get_object()
        
        if offboarding_request.exit_interview_scheduled:
            return Response(
                {"detail": "Exit interview is already scheduled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate required fields
        if 'exit_interview_date' not in request.data:
            return Response(
                {"detail": "Field 'exit_interview_date' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        offboarding_request.exit_interview_scheduled = True
        offboarding_request.exit_interview_date = request.data.get('exit_interview_date')
        offboarding_request.save()
        
        serializer = self.get_serializer(offboarding_request)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def process_final_settlement(self, request, pk=None):
        """
        Process final settlement for an employee.
        """
        offboarding_request = self.get_object()
        
        if offboarding_request.final_settlement_processed:
            return Response(
                {"detail": "Final settlement is already processed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate required fields
        if 'final_settlement_amount' not in request.data:
            return Response(
                {"detail": "Field 'final_settlement_amount' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        offboarding_request.final_settlement_processed = True
        offboarding_request.final_settlement_date = timezone.now().date()
        offboarding_request.final_settlement_amount = request.data.get('final_settlement_amount')
        offboarding_request.save()
        
        serializer = self.get_serializer(offboarding_request)
        return Response(serializer.data)


class OffboardingTaskViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Onboarding & Offboarding'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for offboarding tasks.
    """
    queryset = OffboardingTask.objects.all()
    serializer_class = OffboardingTaskSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OffboardingTaskFilter
    search_fields = ['name', 'description', 'notes']
    ordering_fields = ['sequence', 'due_date', 'status', 'created_at']
    ordering = ['sequence', 'due_date']
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all offboarding tasks with a specific status.
        """
        tasks = OffboardingTask.objects.filter(status=status_value)
        page = self.paginate_queryset(tasks)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def request(self, request, request_pk=None):
        """
        Get all offboarding tasks for a specific request.
        """
        tasks = OffboardingTask.objects.filter(request_id=request_pk)
        page = self.paginate_queryset(tasks)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def category(self, request, category=None):
        """
        Get all offboarding tasks with a specific category.
        """
        tasks = OffboardingTask.objects.filter(category=category)
        page = self.paginate_queryset(tasks)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def assigned_to(self, request, employee_pk=None):
        """
        Get all offboarding tasks assigned to a specific employee.
        """
        tasks = OffboardingTask.objects.filter(assigned_to_id=employee_pk)
        page = self.paginate_queryset(tasks)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(tasks, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """
        Start an offboarding task.
        """
        task = self.get_object()
        
        if task.status != 'not_started':
            return Response(
                {"detail": "Only not started tasks can be started."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.status = 'in_progress'
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """
        Complete an offboarding task.
        """
        task = self.get_object()
        
        if task.status in ['completed', 'cancelled', 'not_applicable']:
            return Response(
                {"detail": "Task is already completed, cancelled, or not applicable."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.status = 'completed'
        task.completed_date = timezone.now().date()
        
        # Set completed_by if provided
        completed_by_id = request.data.get('completed_by')
        if completed_by_id:
            task.completed_by_id = completed_by_id
        
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel an offboarding task.
        """
        task = self.get_object()
        
        if task.status in ['completed', 'not_applicable']:
            return Response(
                {"detail": "Completed or not applicable tasks cannot be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.status = 'cancelled'
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_not_applicable(self, request, pk=None):
        """
        Mark an offboarding task as not applicable.
        """
        task = self.get_object()
        
        if task.status in ['completed', 'cancelled']:
            return Response(
                {"detail": "Completed or cancelled tasks cannot be marked as not applicable."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.status = 'not_applicable'
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """
        Assign an offboarding task to an employee.
        """
        task = self.get_object()
        
        # Validate required fields
        if 'assigned_to' not in request.data:
            return Response(
                {"detail": "Field 'assigned_to' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.assigned_to_id = request.data.get('assigned_to')
        task.save()
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
