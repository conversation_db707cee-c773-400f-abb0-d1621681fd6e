from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from leave_app.models.leave_type import LeaveType
from leave_app.serializers.leave_type_serializer import LeaveTypeSerializer
from leave_app.filters.leave_filters import LeaveTypeFilter


class LeaveTypeViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Leave Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for managing leave types

    list:
    Return a list of all leave types

    create:
    Create a new leave type

    retrieve:
    Return the given leave type

    update:
    Update the given leave type

    partial_update:
    Partially update the given leave type

    destroy:
    Delete the given leave type

    Additional actions:
    - active_leave_types: Get all active leave types
    - inactive_leave_types: Get all inactive leave types
    - paid_leave_types: Get all paid leave types
    - unpaid_leave_types: Get all unpaid leave types
    """
    queryset = LeaveType.objects.all()
    serializer_class = LeaveTypeSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LeaveTypeFilter
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'created_at']
    ordering = ['name']

    @swagger_auto_schema(
        operation_description="Get all active leave types",
        operation_summary="List Active Leave Types",
        responses={
            200: openapi.Response(
                description="List of active leave types",
                schema=LeaveTypeSerializer(many=True)
            )
        },
        tags=['Leave Management']
    )
    @action(detail=False, methods=['get'])
    def active_leave_types(self, request):
        """
        Get all active leave types
        """
        leave_types = self.queryset.filter(status='active')
        page = self.paginate_queryset(leave_types)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(leave_types, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get all inactive leave types",
        operation_summary="List Inactive Leave Types",
        responses={
            200: openapi.Response(
                description="List of inactive leave types",
                schema=LeaveTypeSerializer(many=True)
            )
        },
        tags=['Leave Management']
    )
    @action(detail=False, methods=['get'])
    def inactive_leave_types(self, request):
        """
        Get all inactive leave types
        """
        leave_types = self.queryset.filter(status='inactive')
        page = self.paginate_queryset(leave_types)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(leave_types, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get all paid leave types",
        operation_summary="List Paid Leave Types",
        responses={
            200: openapi.Response(
                description="List of paid leave types",
                schema=LeaveTypeSerializer(many=True)
            )
        },
        tags=['Leave Management']
    )
    @action(detail=False, methods=['get'])
    def paid_leave_types(self, request):
        """
        Get all paid leave types
        """
        leave_types = self.queryset.filter(is_paid=True)
        page = self.paginate_queryset(leave_types)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(leave_types, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_description="Get all unpaid leave types",
        operation_summary="List Unpaid Leave Types",
        responses={
            200: openapi.Response(
                description="List of unpaid leave types",
                schema=LeaveTypeSerializer(many=True)
            )
        },
        tags=['Leave Management']
    )
    @action(detail=False, methods=['get'])
    def unpaid_leave_types(self, request):
        """
        Get all unpaid leave types
        """
        leave_types = self.queryset.filter(is_paid=False)
        page = self.paginate_queryset(leave_types)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(leave_types, many=True)
        return Response(serializer.data)
