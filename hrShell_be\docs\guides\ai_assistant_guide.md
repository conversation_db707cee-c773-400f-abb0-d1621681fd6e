# AI Assistant Guide

## Overview

The AI Assistant module provides an intelligent chat interface that can answer HR-related questions using a combination of vector search and large language models. The system works by:

1. Converting user questions into vector embeddings
2. Searching for relevant information in the vector database
3. Using the retrieved information as context for generating responses with Gemini AI
4. Providing sources for the information used in the response

This guide explains how to use the AI Assistant, how it works behind the scenes, and how to extend its capabilities.

## Features

- **Vector-Enhanced Chat**: Combines vector search with Gemini AI for accurate responses
- **Knowledge Base Integration**: Searches through your organization's HR knowledge base
- **Source Attribution**: Provides sources for the information used in responses
- **Document Processing**: Automatically extracts and indexes information from uploaded documents

## API Endpoints

### Chat with AI Assistant

```
POST /api/v1/ai/chat/
```

**Request Body:**
```json
{
  "question": "What are the best practices for employee onboarding?",
  "max_results": 3  // Optional, default is 3
}
```

**Response:**
```json
{
  "question": "What are the best practices for employee onboarding?",
  "answer": "Based on the information available, here are the best practices for employee onboarding:\n\n1. **Structured Onboarding Program**: Develop a comprehensive onboarding program with clear stages and responsibilities.\n\n2. **Pre-boarding Communication**: Start engaging with new hires before their first day to make them feel welcome.\n\n3. **First Day Experience**: Create a memorable first day with a proper welcome, workspace setup, and introductions.\n\n4. **Documentation and Access**: Ensure all necessary paperwork, accounts, and access rights are prepared in advance.\n\n5. **Role Clarity**: Provide clear job descriptions, expectations, and initial objectives.\n\n6. **Buddy System**: Assign a mentor or buddy to help new employees navigate the organization.\n\n7. **Regular Check-ins**: Schedule frequent feedback sessions during the first few months.\n\n8. **Cultural Integration**: Include activities that help new hires understand and embrace the company culture.\n\n9. **Training and Development**: Offer necessary training for both job-specific skills and company-wide tools.\n\n10. **Measure Effectiveness**: Collect feedback to continuously improve the onboarding process.\n\nEffective onboarding leads to faster productivity, better employee engagement, and reduced turnover.",
  "sources": [
    "HR Shell Onboarding Module Documentation",
    "Best Practices for Employee Onboarding"
  ]
}
```

## How It Works

### Vector Search

The AI Assistant uses vector embeddings to find semantically similar content in the database:

1. **Embedding Creation**: User questions are converted into vector embeddings using Sentence Transformers
2. **Vector Search**: The system searches for similar vectors in the PostgreSQL database using pgvector
3. **Relevance Ranking**: Results are ranked by similarity to the query
4. **Context Formation**: The most relevant results are used as context for the AI

### Response Generation

The AI Assistant uses Google's Gemini AI to generate responses:

1. **Context Preparation**: Relevant information from the vector search is formatted as context
2. **Prompt Construction**: A prompt is created with the context and the user's question
3. **Response Generation**: Gemini AI generates a response based on the prompt
4. **Source Attribution**: Sources of the information are included in the response

## Architecture

The AI Assistant is built with the following components:

- **Vector Operations**: Handles vector embeddings, storage, and search
- **Vector Chat**: Combines vector search with Gemini AI for response generation
- **API Views**: Provides REST API endpoints for the chat interface

### Key Files

- `common/utils/vector_operations.py`: Core vector functionality
- `common/utils/vector_chat.py`: Chat interface with vector search and Gemini AI
- `common/views/ai_assistant_views.py`: API views for the chat interface

## Adding Documents to the Knowledge Base

To expand the AI Assistant's knowledge, you can add documents to the vector database:

```python
from common.utils.vector_operations import VectorProcessor

# Initialize the processor
processor = VectorProcessor()

# Process a PDF file
processor.process_pdf("path/to/document.pdf", metadata={"source": "HR Policy Manual"})

# Process text
processor.process_text("This is some text about HR policies...", metadata={"source": "HR Guidelines"})
```

You can also use the provided script:

```bash
python scripts/process_documents.py --pdf path/to/document.pdf
```

## Configuration

The AI Assistant can be configured through environment variables:

- `SENTENCE_TRANSFORMER_MODEL`: The model to use for creating embeddings (default: "all-MiniLM-L6-v2")
- `GEMINI_API_KEY`: Your Google API key for Gemini AI
- `GEMINI_MODEL`: The Gemini model to use (default: "gemini-1.0-pro")
- `VECTOR_COLLECTION_NAME`: The name of the vector collection (default: "hr_shell_collection")

## Best Practices

1. **Add Diverse Documents**: Include a variety of HR documents to cover different topics
2. **Structure Questions Clearly**: The AI works best with clear, specific questions
3. **Review and Refine**: Regularly review responses and add more documents to improve accuracy
4. **Use Metadata**: Add detailed metadata to documents for better source attribution
5. **Monitor Performance**: Keep track of questions that the AI struggles with and add relevant documents

## Troubleshooting

### Common Issues

- **No Results Found**: Add more documents to the knowledge base
- **Irrelevant Results**: Refine your question or add more specific documents
- **Error Responses**: Check API key and model configuration
- **Slow Responses**: Optimize vector search or reduce the number of results

### Debugging

The AI Assistant logs detailed information about its operations:

```
INFO - Vector search initialized with collection: hr_shell_collection
INFO - Found 3 results in vector database
INFO - Using gemini-1.0-pro model
```

Check the logs for error messages and warnings to diagnose issues.

## Extending the AI Assistant

You can extend the AI Assistant in several ways:

1. **Custom Embeddings**: Implement custom embedding models for specific domains
2. **Additional Data Sources**: Integrate with other data sources like databases or APIs
3. **Custom Prompts**: Modify the system prompt to change the AI's behavior
4. **Feedback Loop**: Implement a feedback system to improve responses over time

## Security Considerations

The AI Assistant has access to your organization's knowledge base, so consider these security measures:

1. **Access Control**: Restrict access to the AI Assistant API
2. **Data Privacy**: Be careful about what documents you add to the knowledge base
3. **API Key Security**: Protect your Gemini API key
4. **Content Filtering**: Implement filters to prevent inappropriate responses

## Conclusion

The AI Assistant provides a powerful way to make your organization's HR knowledge more accessible. By combining vector search with Gemini AI, it can answer questions accurately and provide sources for the information.

For more information, see the [API Documentation](../api_documentation.md) and [Swagger UI](http://localhost:8000/swagger-ui/).
