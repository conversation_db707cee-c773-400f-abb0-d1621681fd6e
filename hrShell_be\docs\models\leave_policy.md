# LeavePolicy Model

The LeavePolicy model represents leave policies with rules for different departments, locations, designations, etc.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the policy | Yes |
| `description` | Text | Description of the policy | No |
| `organization` | ForeignKey | Organization this policy belongs to | Yes |
| `leave_type` | ForeignKey | Leave type this policy applies to | Yes |
| `department` | ForeignKey | Department this policy applies to | No |
| `location` | ForeignKey | Location this policy applies to | No |
| `designation` | ForeignKey | Designation this policy applies to | No |
| `business_unit` | ForeignKey | Business unit this policy applies to | No |
| `employee_type` | String | Employee type this policy applies to | No |
| `accrual_method` | String | Method of accrual (choices: yearly, monthly, quarterly, biannually) | Yes (default: yearly) |
| `accrual_frequency` | String | Frequency of accrual (choices: start_of_period, end_of_period, prorated) | Yes (default: start_of_period) |
| `max_accrual` | Integer | Maximum accrual allowed | Yes (default: 0, 0 = unlimited) |
| `carry_forward_limit` | Integer | Maximum days that can be carried forward | Yes (default: 0) |
| `carry_forward_expiry_months` | Integer | Months after which carried forward leaves expire | Yes (default: 0, 0 = never) |
| `encashment_limit` | Integer | Maximum days that can be encashed | Yes (default: 0) |
| `probation_period_days` | Integer | Duration of probation period in days | Yes (default: 90) |
| `apply_sandwich_rule` | Boolean | Whether to count weekends/holidays between leave days as leave | Yes (default: False) |
| `requires_approval` | Boolean | Whether leave requests require approval | Yes (default: True) |
| `auto_approve_after_days` | Integer | Days after which leave is auto-approved | Yes (default: 0, 0 = never) |
| `min_days_before_application` | Integer | Minimum days before leave date to apply | Yes (default: 0) |
| `max_days_before_application` | Integer | Maximum days before leave date to apply | Yes (default: 0, 0 = no limit) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this policy belongs to |
| `leave_type` | LeaveType | The leave type this policy applies to |
| `department` | Department | The department this policy applies to (optional) |
| `location` | Location | The location this policy applies to (optional) |
| `designation` | Designation | The designation this policy applies to (optional) |
| `business_unit` | BusinessUnit | The business unit this policy applies to (optional) |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name of the policy, leave type, and organization |

## Example

```python
leave_policy = LeavePolicy.objects.create(
    name="Engineering Casual Leave Policy",
    description="Casual leave policy for engineering department",
    organization=acme_corp,
    leave_type=casual_leave,
    department=engineering_department,
    accrual_method="monthly",
    accrual_frequency="prorated",
    max_accrual=12,
    carry_forward_limit=6,
    carry_forward_expiry_months=3,
    encashment_limit=0,
    probation_period_days=90,
    apply_sandwich_rule=True,
    requires_approval=True,
    min_days_before_application=1,
    max_days_before_application=30
)
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/leave-policies/` | GET | List all leave policies |
| `/api/v1/leave-policies/` | POST | Create a new leave policy |
| `/api/v1/leave-policies/{id}/` | GET | Retrieve a specific leave policy |
| `/api/v1/leave-policies/{id}/` | PUT | Update a specific leave policy |
| `/api/v1/leave-policies/{id}/` | DELETE | Delete a specific leave policy |
| `/api/v1/leave-policies/organization/{organization_id}/` | GET | List leave policies for a specific organization |
| `/api/v1/leave-policies/department/{department_id}/` | GET | List leave policies for a specific department |
| `/api/v1/leave-policies/location/{location_id}/` | GET | List leave policies for a specific location |
| `/api/v1/leave-policies/designation/{designation_id}/` | GET | List leave policies for a specific designation |
| `/api/v1/leave-policies/business-unit/{business_unit_id}/` | GET | List leave policies for a specific business unit |
| `/api/v1/leave-policies/employee-type/{employee_type}/` | GET | List leave policies for a specific employee type |
