from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Avg, Su<PERSON>, Count, Min, Max, F, Q
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from employees_app.models.salary import Salary, PaymentMethod
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from employees_app.serializers.salary_serializer import SalarySerializer
from employees_app.filters import SalaryFilter
from datetime import datetime


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class SalaryViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing Salary instances.

    Additional actions:
    - employee_salary: Get salary records for a specific employee
    - department_salaries: Get salary records for employees in a specific department
    - date_range_salaries: Get salary records within a date range
    - payment_method_statistics: Get statistics about payment methods
    - salary_statistics: Get statistics about salaries
    """
    queryset = Salary.objects.all()
    serializer_class = SalarySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = SalaryFilter
    search_fields = ['employee__first_name', 'employee__last_name']
    ordering_fields = ['basic', 'net_amount_payable', 'salary_effective_date']

    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_salary(self, request, employee_id=None):
        """
        Get all salary records for a specific employee
        """
        try:
            employee = Employee.objects.get(pk=employee_id)
        except Employee.DoesNotExist:
            return Response(
                {"detail": "Employee not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        salary_records = self.queryset.filter(employee=employee)
        page = self.paginate_queryset(salary_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(salary_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_salaries(self, request, department_id=None):
        """
        Get all salary records for employees in a specific department
        """
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return Response(
                {"detail": "Department not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all employees in the department
        employees = Employee.objects.filter(department=department)

        # Get salary records for these employees
        salary_records = self.queryset.filter(employee__in=employees)
        page = self.paginate_queryset(salary_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(salary_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def date_range_salaries(self, request):
        """
        Get all salary records within a date range
        """
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)

        if not start_date or not end_date:
            return Response(
                {"detail": "Both start_date and end_date are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"detail": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get salary records that are effective within the date range
        salary_records = self.queryset.filter(
            salary_effective_date__gte=start_date,
            salary_effective_date__lte=end_date
        )

        page = self.paginate_queryset(salary_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(salary_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def payment_method_statistics(self, request):
        """
        Get statistics about payment methods
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Calculate statistics
        payment_method_counts = queryset.values('payment_method').annotate(count=Count('id'))

        # Convert to a more readable format
        stats = {
            'total_records': queryset.count(),
            'payment_methods': []
        }

        for method_count in payment_method_counts:
            method_value = method_count['payment_method']
            method_display = dict(PaymentMethod.choices()).get(method_value, method_value)
            stats['payment_methods'].append({
                'method': method_value,
                'method_display': method_display,
                'count': method_count['count'],
                'percentage': (method_count['count'] / stats['total_records'] * 100) if stats['total_records'] > 0 else 0
            })

        return Response(stats)

    @action(detail=False, methods=['get'])
    def salary_statistics(self, request):
        """
        Get statistics about salaries
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Calculate statistics
        total_records = queryset.count()

        if total_records == 0:
            return Response({
                "detail": "No salary records found"
            }, status=status.HTTP_404_NOT_FOUND)

        avg_basic = queryset.aggregate(Avg('basic'))['basic__avg'] or 0
        avg_allowance = queryset.aggregate(Avg('allowance'))['allowance__avg'] or 0
        avg_deduction = queryset.aggregate(Avg('deduction'))['deduction__avg'] or 0
        avg_net = queryset.aggregate(Avg('net_amount_payable'))['net_amount_payable__avg'] or 0

        min_basic = queryset.aggregate(Min('basic'))['basic__min'] or 0
        max_basic = queryset.aggregate(Max('basic'))['basic__max'] or 0
        min_net = queryset.aggregate(Min('net_amount_payable'))['net_amount_payable__min'] or 0
        max_net = queryset.aggregate(Max('net_amount_payable'))['net_amount_payable__max'] or 0

        # Get statistics by department
        departments = Department.objects.all()
        department_stats = []

        for dept in departments:
            employees = Employee.objects.filter(department=dept)
            dept_records = queryset.filter(employee__in=employees)

            if dept_records.exists():
                department_stats.append({
                    'department_id': dept.id,
                    'department_name': dept.name,
                    'total_records': dept_records.count(),
                    'avg_basic': dept_records.aggregate(Avg('basic'))['basic__avg'] or 0,
                    'avg_net': dept_records.aggregate(Avg('net_amount_payable'))['net_amount_payable__avg'] or 0,
                    'min_basic': dept_records.aggregate(Min('basic'))['basic__min'] or 0,
                    'max_basic': dept_records.aggregate(Max('basic'))['basic__max'] or 0
                })

        # Compile statistics
        stats = {
            'total_records': total_records,
            'averages': {
                'basic': avg_basic,
                'allowance': avg_allowance,
                'deduction': avg_deduction,
                'net_amount_payable': avg_net
            },
            'ranges': {
                'basic': {
                    'min': min_basic,
                    'max': max_basic
                },
                'net_amount_payable': {
                    'min': min_net,
                    'max': max_net
                }
            },
            'department_statistics': department_stats
        }

        return Response(stats)
