from rest_framework import serializers
from employees_app.models.employee_attendance import EmployeeAttendance


class EmployeeAttendanceSerializer(serializers.ModelSerializer):
    """
    Serializer for the EmployeeAttendance model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    status_display = serializers.ReadOnlyField(source='get_status_display')
    
    class Meta:
        model = EmployeeAttendance
        fields = [
            'id', 'employee', 'employee_name', 'date', 'check_in', 'check_out',
            'total_hour', 'status', 'status_display', 'created_at'
        ]
        read_only_fields = ['total_hour', 'created_at']
    
    def validate(self, data):
        """
        Validate that check_out time is after check_in time
        """
        if 'check_in' in data and 'check_out' in data and data['check_in'] and data['check_out']:
            if data['check_out'] <= data['check_in']:
                raise serializers.ValidationError("Check-out time must be after check-in time")
        return data
