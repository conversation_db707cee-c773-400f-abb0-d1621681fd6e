# Payslip Model

The Payslip model represents a payslip generated for an employee.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `payroll_item` | OneToOneField | Payroll item this payslip is for | Yes |
| `employee` | ForeignKey | Employee this payslip is for | Yes |
| `payslip_number` | String | Unique payslip number | Yes |
| `month` | Integer | Month of the payslip (1-12) | Yes |
| `year` | Integer | Year of the payslip | Yes |
| `generation_date` | Date | Date the payslip was generated | Yes |
| `basic_salary` | Decimal | Basic salary amount | Yes |
| `gross_earnings` | Decimal | Gross earnings amount | Yes (default: 0) |
| `total_deductions` | Decimal | Total deductions amount | Yes (default: 0) |
| `net_payable` | Decimal | Net payable amount | Yes (default: 0) |
| `pdf_file` | FileField | PDF file of the payslip | No |
| `status` | String | Status of the payslip | Yes (default: draft) |
| `email_sent` | Boolean | Whether the payslip has been emailed | Yes (default: False) |
| `email_sent_at` | DateTime | Date and time the payslip was emailed | No |
| `remarks` | Text | Any remarks about this payslip | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Choices

### Status Choices
- `draft`: Draft
- `generated`: Generated
- `emailed`: Emailed
- `printed`: Printed

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `payroll_item` | PayrollItem | The payroll item this payslip is for |
| `employee` | Employee | The employee this payslip is for |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, month/year, and payslip number |

## Example

```python
payslip = Payslip.objects.create(
    payroll_item=payroll_item,
    employee=john_doe,
    payslip_number="PS-202304-0001",
    month=4,
    year=2023,
    generation_date=date(2023, 5, 1),
    basic_salary=55000.00,
    gross_earnings=88000.00,
    total_deductions=11000.00,
    net_payable=77000.00,
    status="draft"
)
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/payslips/` | GET | List all payslips |
| `/api/v1/payslips/` | POST | Create a new payslip |
| `/api/v1/payslips/{id}/` | GET | Retrieve a specific payslip |
| `/api/v1/payslips/{id}/` | PUT | Update a specific payslip |
| `/api/v1/payslips/{id}/` | DELETE | Delete a specific payslip |
| `/api/v1/payslips/status/{status}/` | GET | List all payslips with a specific status |
| `/api/v1/payslips/employee/{employee_id}/` | GET | List all payslips for a specific employee |
| `/api/v1/payslips/period/` | GET | List all payslips for a specific month and year |
| `/api/v1/payslips/{id}/generate-pdf/` | POST | Generate PDF for a specific payslip |
| `/api/v1/payslips/{id}/email/` | POST | Email a specific payslip to the employee |
| `/api/v1/payslips/generate-for-payroll/` | POST | Generate payslips for all approved payroll items in a specific payroll |

## Payslip Generation Process

1. **Create Payroll**: Create a payroll for a specific period (e.g., April 2023)
2. **Generate Payroll Items**: Generate payroll items for all active employees
3. **Calculate Payroll Items**: Calculate earnings, deductions, and net payable for each payroll item
4. **Finalize Payroll**: Finalize the payroll and mark it as completed
5. **Generate Payslips**: Generate payslips for all approved payroll items
6. **Email Payslips**: Email payslips to employees

## Payslip PDF Format

The payslip PDF includes the following sections:

### Header
- Company logo and details
- Payslip number and date
- Employee details (name, ID, department, designation)
- Bank details (account number, bank name)

### Earnings
- Basic salary
- House Rent Allowance (HRA)
- Conveyance Allowance
- Medical Allowance
- Special Allowance
- Other earnings
- Total earnings

### Deductions
- Provident Fund (PF)
- Professional Tax
- Income Tax (TDS)
- Loan EMI
- Other deductions
- Total deductions

### Summary
- Total earnings
- Total deductions
- Net payable

### Footer
- Payment date
- Remarks
- Disclaimer
- Contact information

## Email Template

When a payslip is emailed to an employee, the following email template is used:

```
Subject: Payslip for [Month] [Year]

Dear [Employee Name],

Please find attached your payslip for [Month] [Year].

Payslip Details:
- Payslip Number: [Payslip Number]
- Month: [Month] [Year]
- Basic Salary: [Basic Salary]
- Gross Earnings: [Gross Earnings]
- Total Deductions: [Total Deductions]
- Net Payable: [Net Payable]

The amount will be credited to your bank account on [Payment Date].

If you have any questions or concerns regarding your payslip, please contact the HR department.

Regards,
HR Department
[Company Name]
```
