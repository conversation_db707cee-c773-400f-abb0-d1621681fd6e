from rest_framework import serializers
from organization_app.models.organization import Organization, OrganizationStatus, BusinessType, CurrencyChoices


class OrganizationSerializer(serializers.ModelSerializer):
    """
    Serializer for the Organization model
    """
    business_type_display = serializers.ReadOnlyField(source='get_business_type_display')
    default_currency_display = serializers.ReadOnlyField(source='get_default_currency_display')
    status_display = serializers.ReadOnlyField(source='get_status_display')
    
    class Meta:
        model = Organization
        fields = [
            'id', 'name', 'logo', 'registration_number', 'gst_number',
            'pan_number', 'ein_number', 'business_type', 'business_type_display',
            'establishment_date', 'industry_sector', 'default_currency',
            'default_currency_display', 'default_timezone', 'fiscal_year_start_month',
            'fiscal_year_start_day', 'website', 'email', 'phone', 'status',
            'status_display', 'custom_fields', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_business_type(self, value):
        """
        Validate that the business type is one of the allowed choices
        """
        try:
            BusinessType(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError(
                f"Business type must be one of: {', '.join([bt.value for bt in BusinessType])}"
            )
    
    def validate_default_currency(self, value):
        """
        Validate that the currency is one of the allowed choices
        """
        try:
            CurrencyChoices(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError(
                f"Currency must be one of: {', '.join([c.value for c in CurrencyChoices])}"
            )
    
    def validate_status(self, value):
        """
        Validate that the status is one of the allowed choices
        """
        try:
            OrganizationStatus(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError(
                f"Status must be one of: {', '.join([s.value for s in OrganizationStatus])}"
            )


class OrganizationDetailSerializer(OrganizationSerializer):
    """
    Detailed serializer for the Organization model including related entities
    """
    location_count = serializers.SerializerMethodField()
    business_unit_count = serializers.SerializerMethodField()
    designation_count = serializers.SerializerMethodField()
    
    class Meta(OrganizationSerializer.Meta):
        fields = OrganizationSerializer.Meta.fields + [
            'location_count', 'business_unit_count', 'designation_count'
        ]
    
    def get_location_count(self, obj):
        return obj.locations.count()
    
    def get_business_unit_count(self, obj):
        return obj.business_units.count()
    
    def get_designation_count(self, obj):
        return obj.designations.count()
