# Departments API

This section provides detailed information about the department-related endpoints in the HR Management API.

## List Departments

Retrieves a list of departments.

```http
GET /api/v1/departments/
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `status` | string | Filter by status (`active` or `inactive`) |
| `manager_name` | string | Filter by manager name |
| `search` | string | Search in name and description |
| `ordering` | string | Order by field (prefix with `-` for descending order) |

### Response

```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Engineering",
      "description": "Software development department",
      "manager": 1,
      "manager_name": "<PERSON>",
      "status": "active",
      "employee_count": 10,
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z"
    },
    // More departments...
  ]
}
```

## Get Department

Retrieves a specific department by ID.

```http
GET /api/v1/departments/{id}/
```

### Response

```json
{
  "id": 1,
  "name": "Engineering",
  "description": "Software development department",
  "manager": 1,
  "manager_name": "<PERSON> Doe",
  "status": "active",
  "employee_count": 10,
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z",
  "employees": [
    {
      "id": 1,
      "full_name": "John Doe",
      "email": "<EMAIL>",
      "position": "Software Engineer",
      "department_name": "Engineering",
      "status": "active",
      "profile_picture": "http://example.com/media/employee_images/john_doe.jpg"
    },
    // More employees...
  ]
}
```

## Create Department

Creates a new department.

```http
POST /api/v1/departments/
Content-Type: application/json
```

### Request Body

```json
{
  "name": "Marketing",
  "description": "Marketing and communications department",
  "manager": 4,
  "status": "active"
}
```

### Response

```json
{
  "id": 6,
  "name": "Marketing",
  "description": "Marketing and communications department",
  "manager": 4,
  "manager_name": "Alice Williams",
  "status": "active",
  "employee_count": 0,
  "created_at": "2023-03-15T12:00:00Z",
  "updated_at": "2023-03-15T12:00:00Z"
}
```

## Update Department

Updates an existing department.

```http
PUT /api/v1/departments/{id}/
Content-Type: application/json
```

### Request Body

```json
{
  "name": "Marketing",
  "description": "Marketing, communications, and public relations department",
  "manager": 4,
  "status": "active"
}
```

### Response

```json
{
  "id": 6,
  "name": "Marketing",
  "description": "Marketing, communications, and public relations department",
  "manager": 4,
  "manager_name": "Alice Williams",
  "status": "active",
  "employee_count": 0,
  "created_at": "2023-03-15T12:00:00Z",
  "updated_at": "2023-03-15T13:00:00Z"
}
```

## Partial Update Department

Updates specific fields of an existing department.

```http
PATCH /api/v1/departments/{id}/
Content-Type: application/json
```

### Request Body

```json
{
  "description": "Marketing, communications, and public relations department"
}
```

### Response

```json
{
  "id": 6,
  "name": "Marketing",
  "description": "Marketing, communications, and public relations department",
  "manager": 4,
  "manager_name": "Alice Williams",
  "status": "active",
  "employee_count": 0,
  "created_at": "2023-03-15T12:00:00Z",
  "updated_at": "2023-03-15T13:00:00Z"
}
```

## Delete Department

Deletes a department.

```http
DELETE /api/v1/departments/{id}/
```

### Response

```
204 No Content
```

## List Active Departments

Retrieves a list of active departments.

```http
GET /api/v1/departments/active/
```

### Response

```json
{
  "count": 4,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Engineering",
      "description": "Software development department",
      "manager": 1,
      "manager_name": "John Doe",
      "status": "active",
      "employee_count": 10,
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z"
    },
    // More active departments...
  ]
}
```

## Activate Department

Activates an inactive department.

```http
PATCH /api/v1/departments/{id}/activate/
```

### Response

```json
{
  "id": 5,
  "name": "Finance",
  "description": "Financial operations department",
  "manager": 5,
  "manager_name": "Charlie Brown",
  "status": "active",
  "employee_count": 5,
  "created_at": "2023-02-15T12:00:00Z",
  "updated_at": "2023-03-20T14:00:00Z"
}
```

## Deactivate Department

Deactivates an active department.

```http
PATCH /api/v1/departments/{id}/deactivate/
```

### Response

```json
{
  "id": 5,
  "name": "Finance",
  "description": "Financial operations department",
  "manager": 5,
  "manager_name": "Charlie Brown",
  "status": "inactive",
  "employee_count": 5,
  "created_at": "2023-02-15T12:00:00Z",
  "updated_at": "2023-03-20T14:00:00Z"
}
```
