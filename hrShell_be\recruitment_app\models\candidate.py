from django.db import models
from django.core.validators import MinV<PERSON>ueValida<PERSON>, MaxValueValidator
from recruitment_app.models.skill import Skill
from recruitment_app.models.recruitment_source import RecruitmentSource
from organization_app.models.organization import Organization


class Candidate(models.Model):
    """
    Model to represent job candidates.
    """
    GENDER_CHOICES = [
        ('male', 'Male'),
        ('female', 'Female'),
        ('other', 'Other'),
        ('prefer_not_to_say', 'Prefer Not to Say'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('blacklisted', 'Blacklisted'),
        ('hired', 'Hired'),
    ]
    
    # Basic Information
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='candidates')
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField(unique=True)
    phone = models.Char<PERSON><PERSON>(max_length=20, blank=True, null=True)
    gender = models.<PERSON><PERSON><PERSON><PERSON>(max_length=20, choices=GENDER_CHOICES, blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    
    # Address Information
    address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    
    # Professional Information
    headline = models.CharField(max_length=255, blank=True, null=True)
    summary = models.TextField(blank=True, null=True)
    total_experience = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0)], blank=True, null=True)
    current_employer = models.CharField(max_length=255, blank=True, null=True)
    current_designation = models.CharField(max_length=255, blank=True, null=True)
    current_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], blank=True, null=True)
    expected_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], blank=True, null=True)
    notice_period = models.PositiveIntegerField(blank=True, null=True, help_text="Notice period in days")
    
    # Resume and Documents
    resume = models.FileField(upload_to='resumes/%Y/%m/', blank=True, null=True)
    cover_letter = models.FileField(upload_to='cover_letters/%Y/%m/', blank=True, null=True)
    profile_picture = models.ImageField(upload_to='candidate_photos/%Y/%m/', blank=True, null=True)
    
    # Skills and Qualifications
    skills = models.ManyToManyField(Skill, through='CandidateSkill', related_name='candidates')
    highest_qualification = models.CharField(max_length=255, blank=True, null=True)
    
    # Source Information
    source = models.ForeignKey(RecruitmentSource, on_delete=models.SET_NULL, null=True, blank=True, related_name='candidates')
    referred_by = models.CharField(max_length=255, blank=True, null=True)
    
    # Status and Rating
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    rating = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)], blank=True, null=True)
    
    # Social Media
    linkedin_profile = models.URLField(blank=True, null=True)
    github_profile = models.URLField(blank=True, null=True)
    portfolio_website = models.URLField(blank=True, null=True)
    
    # Notes and Tags
    notes = models.TextField(blank=True, null=True)
    tags = models.CharField(max_length=255, blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Candidate'
        verbose_name_plural = 'Candidates'
    
    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"


class CandidateSkill(models.Model):
    """
    Model to represent skills possessed by candidates with proficiency level.
    """
    PROFICIENCY_CHOICES = [
        (1, 'Beginner'),
        (2, 'Elementary'),
        (3, 'Intermediate'),
        (4, 'Advanced'),
        (5, 'Expert'),
    ]
    
    candidate = models.ForeignKey(Candidate, on_delete=models.CASCADE, related_name='candidate_skills')
    skill = models.ForeignKey(Skill, on_delete=models.CASCADE, related_name='candidate_skills')
    proficiency = models.PositiveIntegerField(choices=PROFICIENCY_CHOICES, validators=[MinValueValidator(1), MaxValueValidator(5)])
    years_of_experience = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0)], blank=True, null=True)
    is_primary = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-proficiency', '-years_of_experience']
        verbose_name = 'Candidate Skill'
        verbose_name_plural = 'Candidate Skills'
        unique_together = ['candidate', 'skill']
    
    def __str__(self):
        return f"{self.candidate} - {self.skill} ({self.get_proficiency_display()})"


class CandidateEducation(models.Model):
    """
    Model to represent candidate's educational background.
    """
    candidate = models.ForeignKey(Candidate, on_delete=models.CASCADE, related_name='education')
    institution = models.CharField(max_length=255)
    degree = models.CharField(max_length=255)
    field_of_study = models.CharField(max_length=255)
    start_date = models.DateField()
    end_date = models.DateField(blank=True, null=True)
    is_current = models.BooleanField(default=False)
    grade = models.CharField(max_length=50, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-end_date', '-start_date']
        verbose_name = 'Candidate Education'
        verbose_name_plural = 'Candidate Education'
    
    def __str__(self):
        return f"{self.candidate} - {self.degree} from {self.institution}"


class CandidateExperience(models.Model):
    """
    Model to represent candidate's work experience.
    """
    candidate = models.ForeignKey(Candidate, on_delete=models.CASCADE, related_name='experience')
    company = models.CharField(max_length=255)
    title = models.CharField(max_length=255)
    location = models.CharField(max_length=255, blank=True, null=True)
    start_date = models.DateField()
    end_date = models.DateField(blank=True, null=True)
    is_current = models.BooleanField(default=False)
    description = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-end_date', '-start_date']
        verbose_name = 'Candidate Experience'
        verbose_name_plural = 'Candidate Experience'
    
    def __str__(self):
        return f"{self.candidate} - {self.title} at {self.company}"
