from django.db import models
from enum import Enum
import pytz


class OrganizationStatus(Enum):
    ACTIVE = 'active'
    INACTIVE = 'inactive'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class BusinessType(Enum):
    LLC = 'llc'
    PRIVATE_LIMITED = 'private_limited'
    PUBLIC_LIMITED = 'public_limited'
    PARTNERSHIP = 'partnership'
    SOLE_PROPRIETORSHIP = 'sole_proprietorship'
    CORPORATION = 'corporation'
    OTHER = 'other'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class CurrencyChoices(Enum):
    USD = 'usd'
    EUR = 'eur'
    GBP = 'gbp'
    INR = 'inr'
    JPY = 'jpy'
    CAD = 'cad'
    AUD = 'aud'
    CNY = 'cny'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class Organization(models.Model):
    """
    Organization model for storing company information
    """
    # Basic information
    name = models.CharField(max_length=200, unique=True)
    logo = models.ImageField(upload_to='organization_logos/', null=True, blank=True)
    
    # Registration details
    registration_number = models.CharField(max_length=100, blank=True, null=True)
    gst_number = models.CharField(max_length=100, blank=True, null=True)
    pan_number = models.CharField(max_length=100, blank=True, null=True)
    ein_number = models.CharField(max_length=100, blank=True, null=True)
    
    # Business details
    business_type = models.CharField(
        max_length=50,
        choices=BusinessType.choices(),
        default=BusinessType.PRIVATE_LIMITED.value
    )
    establishment_date = models.DateField(null=True, blank=True)
    industry_sector = models.CharField(max_length=100, blank=True, null=True)
    
    # Settings
    default_currency = models.CharField(
        max_length=10,
        choices=CurrencyChoices.choices(),
        default=CurrencyChoices.USD.value
    )
    default_timezone = models.CharField(
        max_length=50,
        choices=[(tz, tz) for tz in pytz.common_timezones],
        default='UTC'
    )
    fiscal_year_start_month = models.PositiveSmallIntegerField(
        choices=[(i, i) for i in range(1, 13)],
        default=1,
        help_text="Month when fiscal year starts (1-12)"
    )
    fiscal_year_start_day = models.PositiveSmallIntegerField(
        choices=[(i, i) for i in range(1, 32)],
        default=1,
        help_text="Day when fiscal year starts (1-31)"
    )
    
    # Contact information
    website = models.URLField(blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    
    # Status
    status = models.CharField(
        max_length=10,
        choices=OrganizationStatus.choices(),
        default=OrganizationStatus.ACTIVE.value
    )
    
    # Custom fields (JSON field for flexibility)
    custom_fields = models.JSONField(default=dict, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = "Organization"
        verbose_name_plural = "Organizations"
    
    def __str__(self):
        return self.name
