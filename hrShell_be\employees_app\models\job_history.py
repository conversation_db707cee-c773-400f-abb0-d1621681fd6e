from django.db import models


class JobHistory(models.Model):
    """
    Job history model for tracking employee job changes
    """
    employee = models.ForeignKey(
        'Employee',
        on_delete=models.CASCADE,
        related_name='job_history'
    )
    previous_job_title = models.CharField(max_length=100)
    previous_department = models.CharField(max_length=100)
    previous_salary = models.DecimalField(max_digits=10, decimal_places=2)
    start_date = models.DateField()
    end_date = models.DateField()
    reason = models.TextField(blank=True, null=True)
    approved_by = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_job_changes'
    )

    class Meta:
        ordering = ['-end_date']
        verbose_name = "Job History"
        verbose_name_plural = "Job Histories"

    def __str__(self):
        return f"{self.employee} - {self.previous_job_title} ({self.start_date} to {self.end_date})"
