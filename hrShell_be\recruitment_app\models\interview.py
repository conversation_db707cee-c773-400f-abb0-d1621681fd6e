from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from recruitment_app.models.application import Application
from employees_app.models.employee import Employee


class Interview(models.Model):
    """
    Model to represent interviews scheduled for candidates.
    """
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('rescheduled', 'Rescheduled'),
        ('no_show', 'No Show'),
    ]
    
    TYPE_CHOICES = [
        ('phone', 'Phone'),
        ('video', 'Video'),
        ('in_person', 'In Person'),
        ('technical', 'Technical'),
        ('hr', 'HR'),
        ('panel', 'Panel'),
        ('assessment', 'Assessment'),
    ]
    
    ROUND_CHOICES = [
        (1, 'First Round'),
        (2, 'Second Round'),
        (3, 'Third Round'),
        (4, 'Fourth Round'),
        (5, 'Final Round'),
    ]
    
    # Basic Information
    application = models.ForeignKey(Application, on_delete=models.CASCADE, related_name='interviews')
    round = models.PositiveIntegerField(choices=ROUND_CHOICES, default=1)
    interview_type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    
    # Schedule Information
    scheduled_date = models.DateField()
    scheduled_start_time = models.TimeField()
    scheduled_end_time = models.TimeField()
    timezone = models.CharField(max_length=50, default='UTC')
    
    # Location Information
    location = models.CharField(max_length=255, blank=True, null=True)
    meeting_link = models.URLField(blank=True, null=True)
    meeting_id = models.CharField(max_length=100, blank=True, null=True)
    meeting_password = models.CharField(max_length=100, blank=True, null=True)
    
    # Status Information
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    actual_start_time = models.DateTimeField(blank=True, null=True)
    actual_end_time = models.DateTimeField(blank=True, null=True)
    
    # Interviewers
    interviewers = models.ManyToManyField(Employee, related_name='interviews_to_conduct')
    
    # Scheduling Information
    scheduled_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='interviews_scheduled')
    rescheduled_from = models.DateTimeField(blank=True, null=True)
    rescheduled_reason = models.TextField(blank=True, null=True)
    
    # Cancellation Information
    cancellation_reason = models.TextField(blank=True, null=True)
    cancelled_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='interviews_cancelled')
    
    # Notes and Instructions
    notes = models.TextField(blank=True, null=True)
    instructions = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['scheduled_date', 'scheduled_start_time']
        verbose_name = 'Interview'
        verbose_name_plural = 'Interviews'
    
    def __str__(self):
        return f"{self.application.candidate} - {self.get_interview_type_display()} - {self.scheduled_date}"


class InterviewFeedback(models.Model):
    """
    Model to represent feedback provided by interviewers.
    """
    RECOMMENDATION_CHOICES = [
        ('strong_hire', 'Strong Hire'),
        ('hire', 'Hire'),
        ('neutral', 'Neutral'),
        ('reject', 'Reject'),
        ('strong_reject', 'Strong Reject'),
    ]
    
    # Basic Information
    interview = models.ForeignKey(Interview, on_delete=models.CASCADE, related_name='feedback')
    interviewer = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='interview_feedback')
    
    # Feedback Details
    overall_rating = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)])
    recommendation = models.CharField(max_length=20, choices=RECOMMENDATION_CHOICES)
    
    # Detailed Feedback
    technical_skills = models.TextField(blank=True, null=True)
    communication_skills = models.TextField(blank=True, null=True)
    problem_solving = models.TextField(blank=True, null=True)
    cultural_fit = models.TextField(blank=True, null=True)
    strengths = models.TextField(blank=True, null=True)
    weaknesses = models.TextField(blank=True, null=True)
    
    # Additional Comments
    comments = models.TextField(blank=True, null=True)
    
    # Timestamps
    submitted_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-submitted_at']
        verbose_name = 'Interview Feedback'
        verbose_name_plural = 'Interview Feedback'
        unique_together = ['interview', 'interviewer']
    
    def __str__(self):
        return f"{self.interview} - Feedback by {self.interviewer}"


class InterviewQuestion(models.Model):
    """
    Model to represent questions asked during interviews.
    """
    QUESTION_TYPE_CHOICES = [
        ('technical', 'Technical'),
        ('behavioral', 'Behavioral'),
        ('situational', 'Situational'),
        ('experience', 'Experience-based'),
        ('problem_solving', 'Problem Solving'),
        ('other', 'Other'),
    ]
    
    # Basic Information
    interview = models.ForeignKey(Interview, on_delete=models.CASCADE, related_name='questions')
    question = models.TextField()
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPE_CHOICES)
    
    # Response Information
    answer = models.TextField(blank=True, null=True)
    rating = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)], blank=True, null=True)
    
    # Notes
    notes = models.TextField(blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['created_at']
        verbose_name = 'Interview Question'
        verbose_name_plural = 'Interview Questions'
    
    def __str__(self):
        return f"{self.interview} - {self.question[:50]}..."
