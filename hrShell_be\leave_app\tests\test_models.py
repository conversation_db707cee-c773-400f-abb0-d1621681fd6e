from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from organization_app.models.organization import Organization
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from leave_app.models.leave_type import LeaveType
from leave_app.models.leave_policy import LeavePolicy
from leave_app.models.leave_balance import LeaveBalance
from leave_app.models.leave_request import LeaveRequest
from leave_app.models.leave_approval import LeaveApproval
from leave_app.models.holiday import Holiday
from leave_app.models.week_off import WeekOff
from datetime import date, timedelta


class LeaveTypeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test organization
        cls.organization = Organization.objects.create(
            name="Test Organization",
            registration_number="TEST123",
            business_type="private_limited"
        )
        
        # Create test leave type
        cls.leave_type = LeaveType.objects.create(
            name="Casual Leave",
            code="CL",
            description="Leave for personal matters",
            organization=cls.organization,
            is_paid=True,
            max_days_per_year=12,
            min_days_per_request=1,
            max_days_per_request=3,
            carry_forward_allowed=False,
            encashment_allowed=False,
            applicable_during_probation=True,
            probation_period_percentage=50,
            requires_documentation=False,
            status="active"
        )
    
    def test_leave_type_creation(self):
        self.assertEqual(self.leave_type.name, "Casual Leave")
        self.assertEqual(self.leave_type.code, "CL")
        self.assertEqual(self.leave_type.organization, self.organization)
        self.assertEqual(self.leave_type.max_days_per_year, 12)
        self.assertTrue(self.leave_type.is_paid)
        self.assertFalse(self.leave_type.carry_forward_allowed)
        self.assertEqual(self.leave_type.status, "active")
    
    def test_leave_type_str(self):
        expected_str = f"Casual Leave ({self.organization.name})"
        self.assertEqual(str(self.leave_type), expected_str)


class LeaveRequestModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test organization
        cls.organization = Organization.objects.create(
            name="Test Organization",
            registration_number="TEST123",
            business_type="private_limited"
        )
        
        # Create test department
        cls.department = Department.objects.create(
            name="Test Department",
            description="Test Department Description"
        )
        
        # Create test employees
        cls.employee = Employee.objects.create(
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="1234567890",
            department=cls.department,
            hire_date=date.today() - timedelta(days=365)
        )
        
        cls.manager = Employee.objects.create(
            first_name="Jane",
            last_name="Smith",
            email="<EMAIL>",
            phone="0987654321",
            department=cls.department,
            hire_date=date.today() - timedelta(days=730)
        )
        
        # Update department with manager
        cls.department.manager = cls.manager
        cls.department.save()
        
        # Create test leave type
        cls.leave_type = LeaveType.objects.create(
            name="Casual Leave",
            code="CL",
            description="Leave for personal matters",
            organization=cls.organization,
            is_paid=True,
            max_days_per_year=12,
            min_days_per_request=1,
            max_days_per_request=3,
            carry_forward_allowed=False,
            encashment_allowed=False,
            applicable_during_probation=True,
            probation_period_percentage=50,
            requires_documentation=False,
            status="active"
        )
        
        # Create test leave balance
        cls.leave_balance = LeaveBalance.objects.create(
            employee=cls.employee,
            leave_type=cls.leave_type,
            year=timezone.now().year,
            opening_balance=12.0
        )
        
        # Create test leave request
        cls.leave_request = LeaveRequest.objects.create(
            employee=cls.employee,
            leave_type=cls.leave_type,
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=12),
            reason="Personal matters",
            half_day=False,
            status="pending"
        )
    
    def test_leave_request_creation(self):
        self.assertEqual(self.leave_request.employee, self.employee)
        self.assertEqual(self.leave_request.leave_type, self.leave_type)
        self.assertEqual(self.leave_request.total_days, 3)  # 3 days (inclusive)
        self.assertEqual(self.leave_request.status, "pending")
    
    def test_leave_request_str(self):
        expected_str = f"{self.employee} - {self.leave_type.name} ({self.leave_request.start_date} to {self.leave_request.end_date})"
        self.assertEqual(str(self.leave_request), expected_str)
    
    def test_half_day_leave_request(self):
        half_day_request = LeaveRequest.objects.create(
            employee=self.employee,
            leave_type=self.leave_type,
            start_date=date.today() + timedelta(days=20),
            end_date=date.today() + timedelta(days=20),
            reason="Half day leave",
            half_day=True,
            first_half=True,
            status="pending"
        )
        self.assertEqual(half_day_request.total_days, 0.5)


# Add more test classes for other models as needed
