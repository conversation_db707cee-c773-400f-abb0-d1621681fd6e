from django.urls import path, re_path
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

# Create the schema view using drf-yasg
schema_view = get_schema_view(
    openapi.Info(
        title="HR Shell API",
        default_version='v1',
        description="""
# HR Shell API Documentation

Welcome to the HR Shell API documentation. This comprehensive API provides endpoints for managing all aspects of human resource operations in your organization.

## API Groups

The API endpoints are organized into the following logical groups:

### 🔐 Authentication
User authentication and authorization management
- User registration and login
- JWT token management
- User profile information

### 👥 Employee Management
Complete employee lifecycle management
- Employee details, documents, and skills tracking
- Job history and career progression
- Employee hikes and compensation changes

### 🏢 Organization Structure
Multi-level organization hierarchy
- Department and location management
- Designation and business unit setup
- Organization policies and documents

### 🏖️ Leave Management
Flexible leave types and policies
- Leave request and approval workflows
- Holiday and week-off management
- Leave balance tracking and reporting

### ⏰ Attendance Management
Time tracking and attendance monitoring
- Flexible working hours and schedules
- Attendance reports and analytics

### 💰 Payroll Management
Configurable salary structures
- Automated payroll processing
- Tax calculations and deductions
- Payslip generation and distribution

### 💼 Compensation & Benefits
Salary management and revisions
- Bonus and incentive programs
- Loan management and tracking
- Tax declaration and planning

### 🎯 Recruitment & Hiring
Job requisition and posting management
- Candidate application tracking
- Interview scheduling and feedback
- Offer management and onboarding initiation

### 📋 Onboarding & Offboarding
Structured onboarding workflows
- Task management and tracking
- Exit process management
- Feedback collection and analysis

### 📄 Document Management
Centralized document storage
- Template management
- Version control and access management

### 🤖 AI Assistant
Intelligent HR assistance
- Policy and procedure guidance
- Vector-based knowledge search
- Natural language query processing

### ⚙️ System
Health checks and system utilities
- API status monitoring
- System configuration

## Authentication

This API uses JWT (JSON Web Token) authentication. To access protected endpoints:

1. Register a new account or obtain credentials
2. Get a JWT token from the `/api/auth/login/` endpoint
3. Include the token in the Authorization header: `Authorization: Bearer <token>`

## API Features

- **RESTful Design**: All endpoints follow REST principles
- **Comprehensive Filtering**: Advanced filtering and search capabilities
- **Pagination**: Efficient data pagination for large datasets
- **Validation**: Robust input validation and error handling
- **Documentation**: Complete API documentation with examples
- **Security**: JWT-based authentication and authorization

## AI-Powered Features

The HR Shell includes advanced AI capabilities:

### Vector Search
- Intelligent document search using vector embeddings
- Semantic understanding of HR policies and procedures
- Context-aware responses to employee queries

### Natural Language Processing
- Process and understand employee questions in natural language
- Generate intelligent responses based on company policies
- Provide personalized HR assistance

These features help make HR knowledge more accessible and provide intelligent assistance to HR staff and employees.
        """,
        terms_of_service="https://www.example.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="MIT License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
)

# URL patterns for Swagger documentation
urlpatterns = [
    # Swagger JSON schema
    re_path(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),

    # Swagger UI
    path('swagger-ui/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),

    # ReDoc UI
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

    # API Schema endpoint for backward compatibility
    path('api/schema/', schema_view.without_ui(cache_timeout=0), name='schema'),
]
