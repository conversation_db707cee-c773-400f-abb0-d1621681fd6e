from django.db import models
from django.utils import timezone


class LeaveBalance(models.Model):
    """
    Leave Balance model for tracking employee leave balances
    """
    # Associations
    employee = models.ForeignKey(
        'employees_app.Employee',
        on_delete=models.CASCADE,
        related_name='leave_balances'
    )
    leave_type = models.ForeignKey(
        'leave_app.LeaveType',
        on_delete=models.CASCADE,
        related_name='balances'
    )
    
    # Year for which this balance applies
    year = models.PositiveIntegerField(default=timezone.now().year)
    
    # Balance details
    opening_balance = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0
    )
    accrued = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0
    )
    used = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0
    )
    adjusted = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        help_text="Manual adjustments to leave balance"
    )
    encashed = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-year', 'employee__first_name']
        verbose_name = "Leave Balance"
        verbose_name_plural = "Leave Balances"
        unique_together = ('employee', 'leave_type', 'year')
    
    def __str__(self):
        return f"{self.employee} - {self.leave_type.name} ({self.year})"
    
    @property
    def closing_balance(self):
        """
        Calculate the closing balance
        """
        return self.opening_balance + self.accrued - self.used + self.adjusted - self.encashed
