import django_filters
from django.db.models import Q
from payroll_app.models.salary_structure import SalaryStructure, SalaryComponent
from payroll_app.models.employee_salary import EmployeeSalary
from payroll_app.models.payroll import Payroll, PayrollItem
from payroll_app.models.payslip import Payslip
from payroll_app.models.loan import Loan, LoanInstallment
from payroll_app.models.bonus import Bonus
from payroll_app.models.tax import TaxSlab, EmployeeTaxDeclaration
from payroll_app.models.bank_transfer import BankTransfer


class SalaryComponentFilter(django_filters.FilterSet):
    name = django_filters.CharFilter(lookup_expr='icontains')
    code = django_filters.CharFilter(lookup_expr='icontains')
    component_type = django_filters.ChoiceFilter(choices=SalaryComponent.COMPONENT_TYPE_CHOICES)
    calculation_type = django_filters.ChoiceFilter(choices=SalaryComponent.CALCULATION_TYPE_CHOICES)
    is_taxable = django_filters.BooleanFilter()
    is_fixed = django_filters.BooleanFilter()
    is_active = django_filters.BooleanFilter()
    organization = django_filters.NumberFilter()
    
    class Meta:
        model = SalaryComponent
        fields = [
            'name', 'code', 'component_type', 'calculation_type',
            'is_taxable', 'is_fixed', 'is_active', 'organization'
        ]


class SalaryStructureFilter(django_filters.FilterSet):
    name = django_filters.CharFilter(lookup_expr='icontains')
    code = django_filters.CharFilter(lookup_expr='icontains')
    salary_type = django_filters.ChoiceFilter(choices=SalaryStructure.SALARY_TYPE_CHOICES)
    is_active = django_filters.BooleanFilter()
    organization = django_filters.NumberFilter()
    
    class Meta:
        model = SalaryStructure
        fields = ['name', 'code', 'salary_type', 'is_active', 'organization']


class EmployeeSalaryFilter(django_filters.FilterSet):
    employee = django_filters.NumberFilter()
    salary_structure = django_filters.NumberFilter()
    effective_from = django_filters.DateFilter()
    effective_from_gte = django_filters.DateFilter(field_name='effective_from', lookup_expr='gte')
    effective_from_lte = django_filters.DateFilter(field_name='effective_from', lookup_expr='lte')
    effective_to = django_filters.DateFilter()
    effective_to_gte = django_filters.DateFilter(field_name='effective_to', lookup_expr='gte')
    effective_to_lte = django_filters.DateFilter(field_name='effective_to', lookup_expr='lte')
    basic_salary_gte = django_filters.NumberFilter(field_name='basic_salary', lookup_expr='gte')
    basic_salary_lte = django_filters.NumberFilter(field_name='basic_salary', lookup_expr='lte')
    gross_salary_gte = django_filters.NumberFilter(field_name='gross_salary', lookup_expr='gte')
    gross_salary_lte = django_filters.NumberFilter(field_name='gross_salary', lookup_expr='lte')
    is_active = django_filters.BooleanFilter()
    
    class Meta:
        model = EmployeeSalary
        fields = [
            'employee', 'salary_structure', 'effective_from', 'effective_to',
            'basic_salary', 'gross_salary', 'is_active'
        ]


class PayrollFilter(django_filters.FilterSet):
    name = django_filters.CharFilter(lookup_expr='icontains')
    organization = django_filters.NumberFilter()
    start_date = django_filters.DateFilter()
    start_date_gte = django_filters.DateFilter(field_name='start_date', lookup_expr='gte')
    start_date_lte = django_filters.DateFilter(field_name='start_date', lookup_expr='lte')
    end_date = django_filters.DateFilter()
    end_date_gte = django_filters.DateFilter(field_name='end_date', lookup_expr='gte')
    end_date_lte = django_filters.DateFilter(field_name='end_date', lookup_expr='lte')
    payment_date = django_filters.DateFilter()
    payment_date_gte = django_filters.DateFilter(field_name='payment_date', lookup_expr='gte')
    payment_date_lte = django_filters.DateFilter(field_name='payment_date', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=Payroll.STATUS_CHOICES)
    created_by = django_filters.NumberFilter()
    approved_by = django_filters.NumberFilter()
    
    class Meta:
        model = Payroll
        fields = [
            'name', 'organization', 'start_date', 'end_date',
            'payment_date', 'status', 'created_by', 'approved_by'
        ]


class PayrollItemFilter(django_filters.FilterSet):
    payroll = django_filters.NumberFilter()
    employee = django_filters.NumberFilter()
    working_days_gte = django_filters.NumberFilter(field_name='working_days', lookup_expr='gte')
    working_days_lte = django_filters.NumberFilter(field_name='working_days', lookup_expr='lte')
    leave_days_gte = django_filters.NumberFilter(field_name='leave_days', lookup_expr='gte')
    leave_days_lte = django_filters.NumberFilter(field_name='leave_days', lookup_expr='lte')
    lop_days_gte = django_filters.NumberFilter(field_name='lop_days', lookup_expr='gte')
    lop_days_lte = django_filters.NumberFilter(field_name='lop_days', lookup_expr='lte')
    overtime_hours_gte = django_filters.NumberFilter(field_name='overtime_hours', lookup_expr='gte')
    overtime_hours_lte = django_filters.NumberFilter(field_name='overtime_hours', lookup_expr='lte')
    basic_salary_gte = django_filters.NumberFilter(field_name='basic_salary', lookup_expr='gte')
    basic_salary_lte = django_filters.NumberFilter(field_name='basic_salary', lookup_expr='lte')
    gross_earnings_gte = django_filters.NumberFilter(field_name='gross_earnings', lookup_expr='gte')
    gross_earnings_lte = django_filters.NumberFilter(field_name='gross_earnings', lookup_expr='lte')
    total_deductions_gte = django_filters.NumberFilter(field_name='total_deductions', lookup_expr='gte')
    total_deductions_lte = django_filters.NumberFilter(field_name='total_deductions', lookup_expr='lte')
    net_payable_gte = django_filters.NumberFilter(field_name='net_payable', lookup_expr='gte')
    net_payable_lte = django_filters.NumberFilter(field_name='net_payable', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=PayrollItem.STATUS_CHOICES)
    
    class Meta:
        model = PayrollItem
        fields = [
            'payroll', 'employee', 'working_days', 'leave_days', 'lop_days',
            'overtime_hours', 'basic_salary', 'gross_earnings', 'total_deductions',
            'net_payable', 'status'
        ]


class PayslipFilter(django_filters.FilterSet):
    payroll_item = django_filters.NumberFilter()
    employee = django_filters.NumberFilter()
    payslip_number = django_filters.CharFilter(lookup_expr='icontains')
    month = django_filters.NumberFilter()
    year = django_filters.NumberFilter()
    generation_date = django_filters.DateFilter()
    generation_date_gte = django_filters.DateFilter(field_name='generation_date', lookup_expr='gte')
    generation_date_lte = django_filters.DateFilter(field_name='generation_date', lookup_expr='lte')
    basic_salary_gte = django_filters.NumberFilter(field_name='basic_salary', lookup_expr='gte')
    basic_salary_lte = django_filters.NumberFilter(field_name='basic_salary', lookup_expr='lte')
    gross_earnings_gte = django_filters.NumberFilter(field_name='gross_earnings', lookup_expr='gte')
    gross_earnings_lte = django_filters.NumberFilter(field_name='gross_earnings', lookup_expr='lte')
    total_deductions_gte = django_filters.NumberFilter(field_name='total_deductions', lookup_expr='gte')
    total_deductions_lte = django_filters.NumberFilter(field_name='total_deductions', lookup_expr='lte')
    net_payable_gte = django_filters.NumberFilter(field_name='net_payable', lookup_expr='gte')
    net_payable_lte = django_filters.NumberFilter(field_name='net_payable', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=Payslip.STATUS_CHOICES)
    email_sent = django_filters.BooleanFilter()
    
    class Meta:
        model = Payslip
        fields = [
            'payroll_item', 'employee', 'payslip_number', 'month', 'year',
            'generation_date', 'basic_salary', 'gross_earnings', 'total_deductions',
            'net_payable', 'status', 'email_sent'
        ]


class LoanFilter(django_filters.FilterSet):
    employee = django_filters.NumberFilter()
    loan_type = django_filters.ChoiceFilter(choices=Loan.LOAN_TYPE_CHOICES)
    loan_amount_gte = django_filters.NumberFilter(field_name='loan_amount', lookup_expr='gte')
    loan_amount_lte = django_filters.NumberFilter(field_name='loan_amount', lookup_expr='lte')
    interest_rate_gte = django_filters.NumberFilter(field_name='interest_rate', lookup_expr='gte')
    interest_rate_lte = django_filters.NumberFilter(field_name='interest_rate', lookup_expr='lte')
    term_months_gte = django_filters.NumberFilter(field_name='term_months', lookup_expr='gte')
    term_months_lte = django_filters.NumberFilter(field_name='term_months', lookup_expr='lte')
    emi_amount_gte = django_filters.NumberFilter(field_name='emi_amount', lookup_expr='gte')
    emi_amount_lte = django_filters.NumberFilter(field_name='emi_amount', lookup_expr='lte')
    start_date = django_filters.DateFilter()
    start_date_gte = django_filters.DateFilter(field_name='start_date', lookup_expr='gte')
    start_date_lte = django_filters.DateFilter(field_name='start_date', lookup_expr='lte')
    end_date = django_filters.DateFilter()
    end_date_gte = django_filters.DateFilter(field_name='end_date', lookup_expr='gte')
    end_date_lte = django_filters.DateFilter(field_name='end_date', lookup_expr='lte')
    remaining_amount_gte = django_filters.NumberFilter(field_name='remaining_amount', lookup_expr='gte')
    remaining_amount_lte = django_filters.NumberFilter(field_name='remaining_amount', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=Loan.STATUS_CHOICES)
    approved_by = django_filters.NumberFilter()
    
    class Meta:
        model = Loan
        fields = [
            'employee', 'loan_type', 'loan_amount', 'interest_rate', 'term_months',
            'emi_amount', 'start_date', 'end_date', 'remaining_amount', 'status',
            'approved_by'
        ]


class LoanInstallmentFilter(django_filters.FilterSet):
    loan = django_filters.NumberFilter()
    installment_number = django_filters.NumberFilter()
    due_date = django_filters.DateFilter()
    due_date_gte = django_filters.DateFilter(field_name='due_date', lookup_expr='gte')
    due_date_lte = django_filters.DateFilter(field_name='due_date', lookup_expr='lte')
    amount_gte = django_filters.NumberFilter(field_name='amount', lookup_expr='gte')
    amount_lte = django_filters.NumberFilter(field_name='amount', lookup_expr='lte')
    principal_amount_gte = django_filters.NumberFilter(field_name='principal_amount', lookup_expr='gte')
    principal_amount_lte = django_filters.NumberFilter(field_name='principal_amount', lookup_expr='lte')
    interest_amount_gte = django_filters.NumberFilter(field_name='interest_amount', lookup_expr='gte')
    interest_amount_lte = django_filters.NumberFilter(field_name='interest_amount', lookup_expr='lte')
    paid_amount_gte = django_filters.NumberFilter(field_name='paid_amount', lookup_expr='gte')
    paid_amount_lte = django_filters.NumberFilter(field_name='paid_amount', lookup_expr='lte')
    payment_date = django_filters.DateFilter()
    payment_date_gte = django_filters.DateFilter(field_name='payment_date', lookup_expr='gte')
    payment_date_lte = django_filters.DateFilter(field_name='payment_date', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=LoanInstallment.STATUS_CHOICES)
    
    class Meta:
        model = LoanInstallment
        fields = [
            'loan', 'installment_number', 'due_date', 'amount', 'principal_amount',
            'interest_amount', 'paid_amount', 'payment_date', 'status'
        ]


class BonusFilter(django_filters.FilterSet):
    employee = django_filters.NumberFilter()
    bonus_type = django_filters.ChoiceFilter(choices=Bonus.BONUS_TYPE_CHOICES)
    amount_gte = django_filters.NumberFilter(field_name='amount', lookup_expr='gte')
    amount_lte = django_filters.NumberFilter(field_name='amount', lookup_expr='lte')
    payment_date = django_filters.DateFilter()
    payment_date_gte = django_filters.DateFilter(field_name='payment_date', lookup_expr='gte')
    payment_date_lte = django_filters.DateFilter(field_name='payment_date', lookup_expr='lte')
    reference_period_start = django_filters.DateFilter()
    reference_period_start_gte = django_filters.DateFilter(field_name='reference_period_start', lookup_expr='gte')
    reference_period_start_lte = django_filters.DateFilter(field_name='reference_period_start', lookup_expr='lte')
    reference_period_end = django_filters.DateFilter()
    reference_period_end_gte = django_filters.DateFilter(field_name='reference_period_end', lookup_expr='gte')
    reference_period_end_lte = django_filters.DateFilter(field_name='reference_period_end', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=Bonus.STATUS_CHOICES)
    is_taxable = django_filters.BooleanFilter()
    is_prorated = django_filters.BooleanFilter()
    approved_by = django_filters.NumberFilter()
    
    class Meta:
        model = Bonus
        fields = [
            'employee', 'bonus_type', 'amount', 'payment_date', 'reference_period_start',
            'reference_period_end', 'status', 'is_taxable', 'is_prorated', 'approved_by'
        ]


class TaxSlabFilter(django_filters.FilterSet):
    organization = django_filters.NumberFilter()
    financial_year = django_filters.CharFilter(lookup_expr='icontains')
    name = django_filters.CharFilter(lookup_expr='icontains')
    min_income_gte = django_filters.NumberFilter(field_name='min_income', lookup_expr='gte')
    min_income_lte = django_filters.NumberFilter(field_name='min_income', lookup_expr='lte')
    max_income_gte = django_filters.NumberFilter(field_name='max_income', lookup_expr='gte')
    max_income_lte = django_filters.NumberFilter(field_name='max_income', lookup_expr='lte')
    tax_rate_gte = django_filters.NumberFilter(field_name='tax_rate', lookup_expr='gte')
    tax_rate_lte = django_filters.NumberFilter(field_name='tax_rate', lookup_expr='lte')
    gender = django_filters.ChoiceFilter(choices=TaxSlab.GENDER_CHOICES)
    age_group = django_filters.ChoiceFilter(choices=TaxSlab.AGE_GROUP_CHOICES)
    is_active = django_filters.BooleanFilter()
    
    class Meta:
        model = TaxSlab
        fields = [
            'organization', 'financial_year', 'name', 'min_income', 'max_income',
            'tax_rate', 'gender', 'age_group', 'is_active'
        ]


class EmployeeTaxDeclarationFilter(django_filters.FilterSet):
    employee = django_filters.NumberFilter()
    financial_year = django_filters.CharFilter(lookup_expr='icontains')
    declaration_type = django_filters.ChoiceFilter(choices=EmployeeTaxDeclaration.DECLARATION_TYPE_CHOICES)
    declaration_name = django_filters.CharFilter(lookup_expr='icontains')
    declared_amount_gte = django_filters.NumberFilter(field_name='declared_amount', lookup_expr='gte')
    declared_amount_lte = django_filters.NumberFilter(field_name='declared_amount', lookup_expr='lte')
    verified_amount_gte = django_filters.NumberFilter(field_name='verified_amount', lookup_expr='gte')
    verified_amount_lte = django_filters.NumberFilter(field_name='verified_amount', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=EmployeeTaxDeclaration.STATUS_CHOICES)
    verified_by = django_filters.NumberFilter()
    verification_date = django_filters.DateFilter()
    verification_date_gte = django_filters.DateFilter(field_name='verification_date', lookup_expr='gte')
    verification_date_lte = django_filters.DateFilter(field_name='verification_date', lookup_expr='lte')
    
    class Meta:
        model = EmployeeTaxDeclaration
        fields = [
            'employee', 'financial_year', 'declaration_type', 'declaration_name',
            'declared_amount', 'verified_amount', 'status', 'verified_by',
            'verification_date'
        ]


class BankTransferFilter(django_filters.FilterSet):
    payroll = django_filters.NumberFilter()
    organization = django_filters.NumberFilter()
    reference_number = django_filters.CharFilter(lookup_expr='icontains')
    transfer_date = django_filters.DateFilter()
    transfer_date_gte = django_filters.DateFilter(field_name='transfer_date', lookup_expr='gte')
    transfer_date_lte = django_filters.DateFilter(field_name='transfer_date', lookup_expr='lte')
    bank_name = django_filters.CharFilter(lookup_expr='icontains')
    account_number = django_filters.CharFilter(lookup_expr='icontains')
    transfer_type = django_filters.ChoiceFilter(choices=BankTransfer.TRANSFER_TYPE_CHOICES)
    total_amount_gte = django_filters.NumberFilter(field_name='total_amount', lookup_expr='gte')
    total_amount_lte = django_filters.NumberFilter(field_name='total_amount', lookup_expr='lte')
    total_employees_gte = django_filters.NumberFilter(field_name='total_employees', lookup_expr='gte')
    total_employees_lte = django_filters.NumberFilter(field_name='total_employees', lookup_expr='lte')
    status = django_filters.ChoiceFilter(choices=BankTransfer.STATUS_CHOICES)
    
    class Meta:
        model = BankTransfer
        fields = [
            'payroll', 'organization', 'reference_number', 'transfer_date',
            'bank_name', 'account_number', 'transfer_type', 'total_amount',
            'total_employees', 'status'
        ]
