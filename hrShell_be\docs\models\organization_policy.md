# OrganizationPolicy Model

The OrganizationPolicy model represents policies and their associations in an organization.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the policy | Yes |
| `organization` | ForeignKey | Organization this policy belongs to | Yes |
| `description` | Text | Description of the policy | No |
| `policy_type` | String | Type of policy (choices: leave, attendance, shift, payroll, travel, expense, general) | Yes (default: general) |
| `content` | Text | Content of the policy | No |
| `applicable_to_all` | Boolean | Whether the policy applies to all employees | Yes (default: True) |
| `effective_from` | Date | Date from which the policy is effective | No |
| `effective_to` | Date | Date until which the policy is effective | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this policy belongs to |
| `locations` | Location | The locations this policy applies to |
| `departments` | Department | The departments this policy applies to |
| `business_units` | BusinessUnit | The business units this policy applies to |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name of the policy and its type |

## Example

```python
policy = OrganizationPolicy.objects.create(
    name="Annual Leave Policy",
    organization=acme_corp,
    description="Policy for annual leave",
    policy_type="leave",
    content="Employees are entitled to 20 days of annual leave...",
    applicable_to_all=True,
    effective_from="2023-01-01"
)
```
