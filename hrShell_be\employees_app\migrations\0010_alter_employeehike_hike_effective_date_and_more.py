# Generated by Django 5.1.5 on 2025-04-27 18:18

import django.core.validators
import django.db.models.deletion
import employees_app.models.employee_hike
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees_app', '0009_alter_employee_hire_date'),
    ]

    operations = [
        migrations.AlterField(
            model_name='employeehike',
            name='hike_effective_date',
            field=models.DateField(default=employees_app.models.employee_hike.get_current_date),
        ),
        migrations.CreateModel(
            name='EmployeeDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.TextField(help_text="Employee's residential address")),
                ('contact_number', models.CharField(help_text="Employee's contact phone number", max_length=20, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('emergency_contact_name', models.CharField(blank=True, help_text='Name of emergency contact person', max_length=100, null=True)),
                ('emergency_contact_number', models.CharField(blank=True, help_text='Phone number of emergency contact person', max_length=20, null=True, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('email', models.EmailField(help_text="Employee's personal email address", max_length=254)),
                ('experience', models.TextField(help_text="Employee's work experience")),
                ('technical_skills', models.TextField(help_text="Employee's technical skills")),
                ('qualifications', models.TextField(help_text="Employee's educational qualifications")),
                ('date_of_birth', models.DateField(blank=True, help_text="Employee's date of birth", null=True)),
                ('marital_status', models.CharField(choices=[('single', 'Single'), ('married', 'Married'), ('divorced', 'Divorced'), ('widowed', 'Widowed'), ('other', 'Other')], default='single', help_text="Employee's marital status", max_length=10)),
                ('blood_group', models.CharField(blank=True, help_text="Employee's blood group", max_length=5, null=True)),
                ('hobbies', models.TextField(blank=True, help_text="Employee's hobbies and interests", null=True)),
                ('languages_known', models.TextField(blank=True, help_text='Languages known by the employee', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='Date and time when the record was created')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='Date and time when the record was last updated')),
                ('department', models.ForeignKey(help_text='Department the employee belongs to', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_details', to='employees_app.department')),
                ('employee', models.OneToOneField(help_text='The employee this detail belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='detail', to='employees_app.employee')),
                ('reporting_manager', models.ForeignKey(help_text="Employee's reporting manager", null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reporting_employees', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Employee Detail',
                'verbose_name_plural': 'Employee Details',
                'ordering': ['employee__first_name', 'employee__last_name'],
            },
        ),
    ]
