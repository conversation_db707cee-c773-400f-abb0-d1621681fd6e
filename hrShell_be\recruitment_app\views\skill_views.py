from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from recruitment_app.models.skill import Skill
from recruitment_app.serializers.skill_serializer import SkillSerializer
from recruitment_app.filters.recruitment_filters import SkillFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class SkillViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for skills.
    """
    queryset = Skill.objects.all()
    serializer_class = SkillSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = SkillFilter
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'skill_type', 'created_at']
    ordering = ['name']
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get all active skills.
        """
        skills = Skill.objects.filter(is_active=True)
        page = self.paginate_queryset(skills)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(skills, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all skills for a specific organization.
        """
        skills = Skill.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(skills)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(skills, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def skill_type(self, request, skill_type=None):
        """
        Get all skills of a specific type.
        """
        skills = Skill.objects.filter(skill_type=skill_type)
        page = self.paginate_queryset(skills)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(skills, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def parent(self, request, parent_pk=None):
        """
        Get all child skills for a specific parent skill.
        """
        skills = Skill.objects.filter(parent_skill_id=parent_pk)
        page = self.paginate_queryset(skills)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(skills, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def children(self, request, pk=None):
        """
        Get all child skills for this skill.
        """
        skill = self.get_object()
        children = Skill.objects.filter(parent_skill=skill)
        
        page = self.paginate_queryset(children)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(children, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """
        Activate a skill.
        """
        skill = self.get_object()
        
        if skill.is_active:
            return Response(
                {"detail": "Skill is already active."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        skill.is_active = True
        skill.save()
        
        serializer = self.get_serializer(skill)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """
        Deactivate a skill.
        """
        skill = self.get_object()
        
        if not skill.is_active:
            return Response(
                {"detail": "Skill is already inactive."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        skill.is_active = False
        skill.save()
        
        serializer = self.get_serializer(skill)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def set_parent(self, request, pk=None):
        """
        Set the parent skill for this skill.
        """
        skill = self.get_object()
        
        # Validate required fields
        if 'parent_skill' not in request.data:
            return Response(
                {"detail": "Field 'parent_skill' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        parent_id = request.data.get('parent_skill')
        
        # Check if parent is the same as the skill
        if str(skill.id) == str(parent_id):
            return Response(
                {"detail": "A skill cannot be its own parent."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if parent exists
        try:
            parent_skill = Skill.objects.get(id=parent_id)
            
            # Check for circular reference
            current_parent = parent_skill.parent_skill
            while current_parent:
                if current_parent.id == skill.id:
                    return Response(
                        {"detail": "Circular reference detected. This would create a loop in the skill hierarchy."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                current_parent = current_parent.parent_skill
            
            skill.parent_skill = parent_skill
            skill.save()
            
            serializer = self.get_serializer(skill)
            return Response(serializer.data)
            
        except Skill.DoesNotExist:
            return Response(
                {"detail": "Parent skill not found."},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['post'])
    def remove_parent(self, request, pk=None):
        """
        Remove the parent skill for this skill.
        """
        skill = self.get_object()
        
        if not skill.parent_skill:
            return Response(
                {"detail": "Skill does not have a parent."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        skill.parent_skill = None
        skill.save()
        
        serializer = self.get_serializer(skill)
        return Response(serializer.data)
