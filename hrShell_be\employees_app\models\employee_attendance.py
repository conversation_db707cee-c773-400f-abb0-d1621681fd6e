from django.db import models
from datetime import datetime


class AttendanceStatus(models.TextChoices):
    PRESENT = 'present', 'Present'
    ABSENT = 'absent', 'Absent'
    HALF_DAY = 'half_day', 'Half Day'
    LEAVE = 'leave', 'Leave'


class EmployeeAttendance(models.Model):
    """
    Employee attendance model for tracking check-ins and check-outs
    """
    employee = models.ForeignKey(
        'Employee', 
        on_delete=models.CASCADE, 
        related_name='attendances'
    )
    date = models.DateField()
    check_in = models.TimeField(null=True, blank=True)
    check_out = models.TimeField(null=True, blank=True)
    total_hour = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        null=True, 
        blank=True
    )
    status = models.CharField(
        max_length=10,
        choices=AttendanceStatus.choices,
        default=AttendanceStatus.PRESENT
    )
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['employee', 'date']
        ordering = ['-date']
        verbose_name = "Employee Attendance"
        verbose_name_plural = "Employee Attendances"
    
    def __str__(self):
        return f"{self.employee} - {self.date}"
    
    def save(self, *args, **kwargs):
        # Calculate total hours if both check-in and check-out are provided
        if self.check_in and self.check_out:
            check_in_datetime = datetime.combine(self.date, self.check_in)
            check_out_datetime = datetime.combine(self.date, self.check_out)
            duration = check_out_datetime - check_in_datetime
            self.total_hour = duration.total_seconds() / 3600  # Convert to hours
        super().save(*args, **kwargs)
