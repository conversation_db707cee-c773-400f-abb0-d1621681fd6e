# Module Integration Guide

This document provides detailed instructions for integrating new modules with the existing HR Shell system.

## Table of Contents

1. [Overview](#overview)
2. [Module Structure](#module-structure)
3. [Integration Steps](#integration-steps)
4. [Database Integration](#database-integration)
5. [API Integration](#api-integration)
6. [Frontend Integration](#frontend-integration)
7. [Testing Integration](#testing-integration)
8. [Documentation Integration](#documentation-integration)

## Overview

The HR Shell system is designed with a modular architecture, allowing new modules to be added and integrated with existing functionality. This guide explains how to create and integrate new modules with the system.

## Module Structure

Each module in the HR Shell system follows a standard structure:

```
module_name_app/
├── __init__.py
├── admin.py
├── apps.py
├── migrations/
├── models.py
├── serializers.py
├── tests/
│   ├── __init__.py
│   ├── test_models.py
│   ├── test_serializers.py
│   └── test_views.py
├── urls.py
└── views.py
```

### Key Components

- **models.py**: Contains the database models for the module
- **serializers.py**: Contains the serializers for the models
- **views.py**: Contains the API views for the module
- **urls.py**: Contains the URL patterns for the module
- **tests/**: Contains the tests for the module

## Integration Steps

Follow these steps to integrate a new module with the HR Shell system:

### 1. Create the Module Structure

```bash
mkdir -p hrShell_be/new_module_app/{migrations,tests}
touch hrShell_be/new_module_app/__init__.py
touch hrShell_be/new_module_app/admin.py
touch hrShell_be/new_module_app/apps.py
touch hrShell_be/new_module_app/models.py
touch hrShell_be/new_module_app/serializers.py
touch hrShell_be/new_module_app/urls.py
touch hrShell_be/new_module_app/views.py
touch hrShell_be/new_module_app/tests/__init__.py
touch hrShell_be/new_module_app/tests/test_models.py
touch hrShell_be/new_module_app/tests/test_serializers.py
touch hrShell_be/new_module_app/tests/test_views.py
```

### 2. Create the Module App Configuration

Edit `apps.py`:

```python
from django.apps import AppConfig

class NewModuleAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'new_module_app'
    verbose_name = 'New Module'
```

### 3. Register the Module in Settings

Edit `hrShell_be/settings.py` to add the module to `INSTALLED_APPS`:

```python
INSTALLED_APPS = [
    # ...
    'new_module_app',
]
```

### 4. Create Database Models

Edit `models.py` to define the database models for the module:

```python
from django.db import models
from django.utils.translation import gettext_lazy as _

class NewModel(models.Model):
    name = models.CharField(_('Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('New Model')
        verbose_name_plural = _('New Models')
        ordering = ['-created_at']

    def __str__(self):
        return self.name
```

### 5. Create Serializers

Edit `serializers.py` to define the serializers for the models:

```python
from rest_framework import serializers
from .models import NewModel

class NewModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = NewModel
        fields = '__all__'
```

### 6. Create Views

Edit `views.py` to define the API views for the module:

```python
from rest_framework import viewsets
from .models import NewModel
from .serializers import NewModelSerializer

class NewModelViewSet(viewsets.ModelViewSet):
    queryset = NewModel.objects.all()
    serializer_class = NewModelSerializer
```

### 7. Create URL Patterns

Edit `urls.py` to define the URL patterns for the module:

```python
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import NewModelViewSet

router = DefaultRouter()
router.register(r'new-models', NewModelViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
```

### 8. Register the Module URLs

Edit `common/urls.py` to include the module's URLs:

```python
urlpatterns = [
    # ...
    path('api/v1/', include('new_module_app.urls')),
]
```

### 9. Create Migrations

Generate and apply database migrations:

```bash
python manage.py makemigrations new_module_app
python manage.py migrate
```

### 10. Register Models in Admin

Edit `admin.py` to register the models in the Django admin:

```python
from django.contrib import admin
from .models import NewModel

@admin.register(NewModel)
class NewModelAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at', 'updated_at')
    search_fields = ('name', 'description')
    list_filter = ('created_at', 'updated_at')
```

## Database Integration

When integrating a new module with the existing database:

### 1. Identify Relationships

Determine how the new module's models relate to existing models:

- One-to-One relationships
- One-to-Many relationships
- Many-to-Many relationships

### 2. Define Foreign Keys

Add foreign keys to establish relationships with existing models:

```python
from django.db import models
from employees_app.models import Employee

class EmployeeTraining(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='trainings')
    # Other fields...
```

### 3. Create Migrations

Generate and apply migrations for the relationships:

```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. Update Serializers

Update serializers to include related data:

```python
from rest_framework import serializers
from employees_app.serializers import EmployeeSerializer
from .models import EmployeeTraining

class EmployeeTrainingSerializer(serializers.ModelSerializer):
    employee = EmployeeSerializer(read_only=True)
    employee_id = serializers.PrimaryKeyRelatedField(
        write_only=True,
        queryset=Employee.objects.all(),
        source='employee'
    )

    class Meta:
        model = EmployeeTraining
        fields = '__all__'
```

## API Integration

When integrating a new module's API with the existing API:

### 1. Follow REST Conventions

Ensure that your API endpoints follow REST conventions:

- Use plural nouns for resource collections
- Use HTTP methods appropriately (GET, POST, PUT, PATCH, DELETE)
- Use nested URLs for related resources

### 2. Add Swagger Documentation

Update the Swagger documentation to include the new endpoints:

1. Open `common/swagger.py`
2. Add the new endpoints to the appropriate tag group in the `get_all_paths` method
3. Add sample payloads in the `get_sample_payload` and `get_sample_response` methods

### 3. Update API Endpoints Documentation

Update the API endpoints documentation in `docs/api_endpoints.md` to include the new endpoints.

## Testing Integration

When integrating tests for a new module:

### 1. Write Model Tests

Create tests for the models in `tests/test_models.py`:

```python
from django.test import TestCase
from ..models import NewModel

class NewModelTestCase(TestCase):
    def setUp(self):
        self.model = NewModel.objects.create(
            name='Test Model',
            description='Test Description'
        )

    def test_model_creation(self):
        self.assertEqual(self.model.name, 'Test Model')
        self.assertEqual(self.model.description, 'Test Description')
```

### 2. Write Serializer Tests

Create tests for the serializers in `tests/test_serializers.py`:

```python
from django.test import TestCase
from ..models import NewModel
from ..serializers import NewModelSerializer

class NewModelSerializerTestCase(TestCase):
    def setUp(self):
        self.model = NewModel.objects.create(
            name='Test Model',
            description='Test Description'
        )
        self.serializer = NewModelSerializer(instance=self.model)

    def test_serializer_contains_expected_fields(self):
        data = self.serializer.data
        self.assertEqual(set(data.keys()), set(['id', 'name', 'description', 'created_at', 'updated_at']))
```

### 3. Write View Tests

Create tests for the views in `tests/test_views.py`:

```python
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from ..models import NewModel

class NewModelViewSetTestCase(APITestCase):
    def setUp(self):
        self.model = NewModel.objects.create(
            name='Test Model',
            description='Test Description'
        )
        self.url = reverse('newmodel-list')

    def test_list_models(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
```

### 4. Run Tests

Run the tests to ensure they pass:

```bash
python manage.py test new_module_app
```

## Documentation Integration

When integrating documentation for a new module:

### 1. Create Module Documentation

Create a markdown file in the `docs/` directory:

```bash
touch hrShell_be/docs/NEW_MODULE.md
```

### 2. Document the Module

Include the following information in the module documentation:

- Purpose of the module
- Key components and their relationships
- Database schema
- API endpoints
- Usage examples
- Configuration options

### 3. Update API Documentation

Update the API documentation to include the new module's endpoints:

- Update Swagger documentation
- Update API endpoints documentation
- Create or update Postman collections

### 4. Update README

Update the main README to mention the new module:

```markdown
## Modules

- Employee Management: Manage employee records and information
- Organization Structure: Manage organization hierarchy and structure
- Leave Management: Manage employee leave requests and approvals
- New Module: [Brief description of the new module]
```

---

By following this guide, you can ensure that new modules are properly integrated with the existing HR Shell system, maintaining consistency and quality across the codebase.
