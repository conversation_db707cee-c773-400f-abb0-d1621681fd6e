from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from leave_app.models.holiday import Holiday, HolidayType
from leave_app.serializers.holiday_serializer import HolidaySerializer
from leave_app.filters.leave_filters import Holiday<PERSON><PERSON>er


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class HolidayViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Leave Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for managing holidays
    
    list:
    Return a list of all holidays
    
    create:
    Create a new holiday
    
    retrieve:
    Return the given holiday
    
    update:
    Update the given holiday
    
    partial_update:
    Partially update the given holiday
    
    destroy:
    Delete the given holiday
    
    Additional actions:
    - organization_holidays: Get holidays for a specific organization
    - location_holidays: Get holidays for a specific location
    - year_holidays: Get holidays for a specific year
    - upcoming_holidays: Get upcoming holidays
    - recurring_holidays: Get recurring holidays
    """
    queryset = Holiday.objects.all()
    serializer_class = HolidaySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = HolidayFilter
    search_fields = ['name', 'description']
    ordering_fields = ['date', 'name']
    ordering = ['date']
    
    @action(detail=False, methods=['get'], url_path='organization/(?P<organization_id>[^/.]+)')
    def organization_holidays(self, request, organization_id=None):
        """
        Get holidays for a specific organization
        """
        holidays = self.queryset.filter(organization_id=organization_id)
        page = self.paginate_queryset(holidays)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(holidays, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='location/(?P<location_id>[^/.]+)')
    def location_holidays(self, request, location_id=None):
        """
        Get holidays for a specific location
        """
        holidays = self.queryset.filter(
            Q(location_id=location_id) | Q(location__isnull=True)
        )
        page = self.paginate_queryset(holidays)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(holidays, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='year/(?P<year>[^/.]+)')
    def year_holidays(self, request, year=None):
        """
        Get holidays for a specific year
        """
        try:
            year = int(year)
        except ValueError:
            return Response(
                {"detail": "Invalid year format"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get holidays for the specified year
        holidays = self.queryset.filter(date__year=year)
        
        # Also get recurring holidays from previous years
        recurring_holidays = self.queryset.filter(
            is_recurring=True,
            date__year__lt=year
        )
        
        # For recurring holidays, adjust the date to the current year
        for holiday in recurring_holidays:
            holiday.date = holiday.date.replace(year=year)
        
        # Combine both querysets
        holidays = list(holidays) + list(recurring_holidays)
        
        # Sort by date
        holidays.sort(key=lambda x: x.date)
        
        page = self.paginate_queryset(holidays)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(holidays, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def upcoming_holidays(self, request):
        """
        Get upcoming holidays
        """
        today = timezone.now().date()
        
        # Get upcoming holidays for the current year
        holidays = self.queryset.filter(date__gte=today)
        
        # Also get recurring holidays from previous years that are upcoming this year
        current_year = today.year
        recurring_holidays = self.queryset.filter(
            is_recurring=True,
            date__year__lt=current_year
        )
        
        # For recurring holidays, adjust the date to the current year
        upcoming_recurring = []
        for holiday in recurring_holidays:
            current_year_date = holiday.date.replace(year=current_year)
            if current_year_date >= today:
                holiday.date = current_year_date
                upcoming_recurring.append(holiday)
        
        # Combine both querysets
        holidays = list(holidays) + upcoming_recurring
        
        # Sort by date
        holidays.sort(key=lambda x: x.date)
        
        page = self.paginate_queryset(holidays)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(holidays, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def recurring_holidays(self, request):
        """
        Get recurring holidays
        """
        holidays = self.queryset.filter(is_recurring=True)
        page = self.paginate_queryset(holidays)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(holidays, many=True)
        return Response(serializer.data)
