from django.contrib import admin
from recruitment_app.models.job_requisition import JobRequisition
from recruitment_app.models.job_posting import JobPosting
from recruitment_app.models.candidate import Candidate, CandidateSkill, CandidateEducation, CandidateExperience
from recruitment_app.models.application import Application, ApplicationStageHistory
from recruitment_app.models.interview import Interview, InterviewFeedback, InterviewQuestion
from recruitment_app.models.offer import Offer
from recruitment_app.models.onboarding import Onboarding, OnboardingTask
from recruitment_app.models.recruitment_source import RecruitmentSource
from recruitment_app.models.skill import Skill


@admin.register(JobRequisition)
class JobRequisitionAdmin(admin.ModelAdmin):
    list_display = ('code', 'title', 'organization', 'department', 'designation', 'location', 'status', 'requested_date')
    list_filter = ('status', 'organization', 'department', 'designation', 'location', 'employment_type', 'experience_level', 'priority')
    search_fields = ('code', 'title', 'description', 'requirements', 'responsibilities', 'qualifications', 'skills_required')
    date_hierarchy = 'requested_date'


@admin.register(JobPosting)
class JobPostingAdmin(admin.ModelAdmin):
    list_display = ('title', 'job_requisition', 'status', 'visibility', 'published_date', 'expiry_date', 'application_count')
    list_filter = ('status', 'visibility', 'is_remote', 'allows_remote', 'is_featured', 'published_date')
    search_fields = ('title', 'description', 'requirements', 'responsibilities', 'qualifications', 'benefits')
    date_hierarchy = 'published_date'
    filter_horizontal = ('sources',)
    prepopulated_fields = {'slug': ('title',)}


class CandidateSkillInline(admin.TabularInline):
    model = CandidateSkill
    extra = 1


class CandidateEducationInline(admin.TabularInline):
    model = CandidateEducation
    extra = 1


class CandidateExperienceInline(admin.TabularInline):
    model = CandidateExperience
    extra = 1


@admin.register(Candidate)
class CandidateAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'email', 'phone', 'current_employer', 'current_designation', 'total_experience', 'status')
    list_filter = ('status', 'gender', 'organization', 'source')
    search_fields = ('first_name', 'last_name', 'email', 'phone', 'headline', 'summary', 'current_employer', 'current_designation', 'tags')
    date_hierarchy = 'created_at'
    inlines = [CandidateSkillInline, CandidateEducationInline, CandidateExperienceInline]


class ApplicationStageHistoryInline(admin.TabularInline):
    model = ApplicationStageHistory
    extra = 1
    readonly_fields = ('changed_at',)


@admin.register(Application)
class ApplicationAdmin(admin.ModelAdmin):
    list_display = ('candidate', 'job_posting', 'status', 'current_stage', 'application_date', 'is_shortlisted', 'rating')
    list_filter = ('status', 'current_stage', 'is_shortlisted', 'source')
    search_fields = ('candidate__first_name', 'candidate__last_name', 'candidate__email', 'job_posting__title', 'notes', 'internal_feedback')
    date_hierarchy = 'application_date'
    inlines = [ApplicationStageHistoryInline]


class InterviewFeedbackInline(admin.TabularInline):
    model = InterviewFeedback
    extra = 1
    readonly_fields = ('submitted_at',)


class InterviewQuestionInline(admin.TabularInline):
    model = InterviewQuestion
    extra = 1


@admin.register(Interview)
class InterviewAdmin(admin.ModelAdmin):
    list_display = ('application', 'round', 'interview_type', 'scheduled_date', 'scheduled_start_time', 'status')
    list_filter = ('status', 'round', 'interview_type', 'scheduled_date')
    search_fields = ('application__candidate__first_name', 'application__candidate__last_name', 'application__job_posting__title', 'notes', 'instructions')
    date_hierarchy = 'scheduled_date'
    filter_horizontal = ('interviewers',)
    inlines = [InterviewFeedbackInline, InterviewQuestionInline]


@admin.register(Offer)
class OfferAdmin(admin.ModelAdmin):
    list_display = ('application', 'position', 'offer_date', 'expiry_date', 'joining_date', 'base_salary', 'status')
    list_filter = ('status', 'offer_date', 'expiry_date', 'joining_date')
    search_fields = ('application__candidate__first_name', 'application__candidate__last_name', 'position', 'department', 'location')
    date_hierarchy = 'offer_date'


class OnboardingTaskInline(admin.TabularInline):
    model = OnboardingTask
    extra = 1


@admin.register(Onboarding)
class OnboardingAdmin(admin.ModelAdmin):
    list_display = ('candidate', 'start_date', 'end_date', 'status', 'hr_buddy', 'manager')
    list_filter = ('status', 'start_date', 'documents_submitted', 'documents_verified', 'equipment_assigned', 'system_access_provided', 'orientation_completed', 'training_completed')
    search_fields = ('candidate__first_name', 'candidate__last_name', 'notes')
    date_hierarchy = 'start_date'
    inlines = [OnboardingTaskInline]


@admin.register(RecruitmentSource)
class RecruitmentSourceAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'organization', 'source_type', 'website', 'is_active', 'integration_active')
    list_filter = ('source_type', 'is_active', 'integration_active', 'organization')
    search_fields = ('name', 'code', 'description', 'website', 'contact_person', 'contact_email')


@admin.register(Skill)
class SkillAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'organization', 'skill_type', 'parent_skill', 'is_active')
    list_filter = ('skill_type', 'is_active', 'organization')
    search_fields = ('name', 'code', 'description')
