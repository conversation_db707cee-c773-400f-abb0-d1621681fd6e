# Models

This section provides detailed information about the data models used in the HR Management API.

## Overview

The HR Management API uses the following main models:

### Employee Management
- [Employee](employee.md): Represents an employee in the organization
- [Department](department.md): Represents a department in the organization
- [EmployeeHike](employee_hike.md): Represents a salary hike for an employee

### Organization Management
- [Organization](organization.md): Represents a company in the system
- [Location](location.md): Represents a branch or office of an organization
- [BusinessUnit](business_unit.md): Represents a division or business unit in an organization
- [Designation](designation.md): Represents a job title or role in an organization
- [OrganizationPolicy](organization_policy.md): Represents policies and their associations
- [OrganizationDocument](organization_document.md): Represents documents related to an organization

### Leave Management
- [LeaveType](leave_type.md): Represents different types of leave
- [LeavePolicy](leave_policy.md): Represents leave policies with rules for different departments, locations, etc.
- [LeaveBalance](leave_balance.md): Represents employee leave balances for each leave type and year
- [LeaveRequest](leave_request.md): Represents leave applications submitted by employees
- [LeaveApproval](leave_approval.md): Represents multi-level approval records for leave requests
- [Holiday](holiday.md): Represents holiday information for different locations
- [WeekOff](week_off.md): Represents weekly off days for different departments and locations

## Database Schema

### Employee Management Schema

The following diagram shows the relationships between the employee management models:

```
+----------------+       +----------------+       +----------------+
|    Employee    |       |   Department   |       | EmployeeHike   |
+----------------+       +----------------+       +----------------+
| id             |       | id             |       | id             |
| first_name     |       | name           |       | employee_id    |
| last_name      |       | description    |       | hike_percentage|
| email          |       | manager_id     |       | effective_date |
| phone          |       | status         |       | hike_reason    |
| position       |       | created_at     |       | department_id  |
| department_id  +-------+ updated_at     |       | previous_salary|
| hire_date      |       |                |       | new_salary     |
| gender         |       |                |       | approved_by_id |
| status         |       |                |       |                |
| profile_picture|       |                |       |                |
| resume         |       |                |       |                |
| contract       |       |                |       |                |
| created_at     |       |                |       |                |
| updated_at     |       |                |       |                |
+----------------+       +----------------+       +----------------+
```

### Organization Management Schema

The following diagram shows the relationships between the organization management models:

```
+----------------+       +----------------+       +----------------+
|  Organization  |       |    Location    |       | BusinessUnit   |
+----------------+       +----------------+       +----------------+
| id             |       | id             |       | id             |
| name           +-------+ organization_id|       | name           |
| logo           |       | name           |       | organization_id|
| reg_number     |       | address_line1  |       | description    |
| gst_number     |       | address_line2  |       | code           |
| pan_number     |       | city           |       | parent_id      |
| ein_number     |       | state          |       | head_id        |
| business_type  |       | country        |       | location_id    |
| estab_date     |       | postal_code    |       | status         |
| industry       |       | phone          |       | created_at     |
| currency       |       | email          |       | updated_at     |
| timezone       |       | latitude       |       |                |
| fiscal_year    |       | longitude      |       |                |
| website        |       | working_hours  |       |                |
| email          |       | is_headquarters|       |                |
| phone          |       | status         |       |                |
| status         |       | created_at     |       |                |
| custom_fields  |       | updated_at     |       |                |
| created_at     |       |                |       |                |
| updated_at     |       |                |       |                |
+----------------+       +----------------+       +----------------+
        |                        |                        |
        |                        |                        |
        v                        v                        v
+----------------+       +----------------+       +----------------+
|  Designation   |       |    Policy      |       |   Document     |
+----------------+       +----------------+       +----------------+
| id             |       | id             |       | id             |
| title          |       | name           |       | title          |
| organization_id|       | organization_id|       | organization_id|
| description    |       | description    |       | description    |
| level          |       | policy_type    |       | document_type  |
| department_id  |       | content        |       | file           |
| pay_grade      |       | applicable_all |       | version        |
| min_salary     |       | effective_from |       | effective_date |
| max_salary     |       | effective_to   |       | expiry_date    |
| created_at     |       | created_at     |       | business_unit  |
| updated_at     |       | updated_at     |       | created_at     |
+----------------+       +----------------+       | updated_at     |
                                                  +----------------+
```

### Leave Management Schema

For a detailed view of the leave management schema, see [Leave Management Schema](leave_management_schema.md).

## Enumerations

The API uses the following enumerations:

### Employee Management Enumerations

#### Gender
- `male`: Male
- `female`: Female
- `other`: Other

#### Status
- `active`: Active
- `inactive`: Inactive

#### DepartmentStatus
- `active`: Active
- `inactive`: Inactive

### Organization Management Enumerations

#### OrganizationStatus
- `active`: Active
- `inactive`: Inactive

#### BusinessType
- `llc`: LLC
- `private_limited`: Private Limited
- `public_limited`: Public Limited
- `partnership`: Partnership
- `sole_proprietorship`: Sole Proprietorship
- `corporation`: Corporation
- `other`: Other

#### CurrencyChoices
- `usd`: USD
- `eur`: EUR
- `gbp`: GBP
- `inr`: INR
- `jpy`: JPY
- `cad`: CAD
- `aud`: AUD
- `cny`: CNY

#### LocationStatus
- `active`: Active
- `inactive`: Inactive

#### DesignationLevel
- `junior`: Junior
- `mid`: Mid
- `senior`: Senior
- `lead`: Lead
- `manager`: Manager
- `director`: Director
- `executive`: Executive

#### BusinessUnitStatus
- `active`: Active
- `inactive`: Inactive

#### PolicyType
- `leave`: Leave
- `attendance`: Attendance
- `shift`: Shift
- `payroll`: Payroll
- `travel`: Travel
- `expense`: Expense
- `general`: General

#### DocumentType
- `policy`: Policy
- `procedure`: Procedure
- `certificate`: Certificate
- `legal`: Legal
- `registration`: Registration
- `compliance`: Compliance
- `other`: Other

### Leave Management Enumerations

#### LeaveTypeStatus
- `active`: Active
- `inactive`: Inactive

#### AccrualMethod
- `yearly`: Yearly
- `monthly`: Monthly
- `quarterly`: Quarterly
- `biannually`: Biannually

#### AccrualFrequency
- `start_of_period`: Start of Period
- `end_of_period`: End of Period
- `prorated`: Prorated

#### LeaveRequestStatus
- `pending`: Pending
- `approved`: Approved
- `rejected`: Rejected
- `cancelled`: Cancelled

#### ApprovalStatus
- `pending`: Pending
- `approved`: Approved
- `rejected`: Rejected
- `skipped`: Skipped

#### HolidayType
- `national`: National
- `regional`: Regional
- `religious`: Religious
- `company`: Company

#### DayOfWeek
- `0`: Monday
- `1`: Tuesday
- `2`: Wednesday
- `3`: Thursday
- `4`: Friday
- `5`: Saturday
- `6`: Sunday
