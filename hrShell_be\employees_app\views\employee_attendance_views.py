from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Avg, Sum, Count
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from employees_app.models.employee_attendance import EmployeeAttendance
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from employees_app.serializers.employee_attendance_serializer import EmployeeAttendanceSerializer
from employees_app.filters import EmployeeAttendanceFilter
from datetime import datetime, timedelta


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi


class EmployeeAttendanceViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Attendance Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing EmployeeAttendance instances.

    Additional actions:
    - employee_attendance: Get attendance records for a specific employee
    - department_attendance: Get attendance records for a specific department
    - date_range_attendance: Get attendance records within a date range
    - today_attendance: Get attendance records for today
    - attendance_statistics: Get statistics about attendance
    """
    queryset = EmployeeAttendance.objects.all()
    serializer_class = EmployeeAttendanceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = EmployeeAttendanceFilter
    search_fields = ['employee__first_name', 'employee__last_name']
    ordering_fields = ['date', 'created_at']

    @swagger_auto_schema(tags=['Attendance Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Attendance Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_attendance(self, request, employee_id=None):
        """
        Get all attendance records for a specific employee
        """
        try:
            employee = Employee.objects.get(pk=employee_id)
        except Employee.DoesNotExist:
            return Response(
                {"detail": "Employee not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        attendance_records = self.queryset.filter(employee=employee)
        page = self.paginate_queryset(attendance_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(attendance_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_attendance(self, request, department_id=None):
        """
        Get all attendance records for employees in a specific department
        """
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return Response(
                {"detail": "Department not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all employees in the department
        employees = Employee.objects.filter(department=department)

        # Get attendance records for these employees
        attendance_records = self.queryset.filter(employee__in=employees)
        page = self.paginate_queryset(attendance_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(attendance_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def date_range_attendance(self, request):
        """
        Get all attendance records within a date range
        """
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)

        if not start_date or not end_date:
            return Response(
                {"detail": "Both start_date and end_date are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"detail": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )

        attendance_records = self.queryset.filter(
            date__gte=start_date,
            date__lte=end_date
        )
        page = self.paginate_queryset(attendance_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(attendance_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def today_attendance(self, request):
        """
        Get all attendance records for today
        """
        today = timezone.now().date()

        attendance_records = self.queryset.filter(date=today)
        page = self.paginate_queryset(attendance_records)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(attendance_records, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def attendance_statistics(self, request):
        """
        Get statistics about attendance
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Filter by date range if provided
        if start_date and end_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(date__gte=start_date, date__lte=end_date)
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Calculate statistics
        total_records = queryset.count()
        present_count = queryset.filter(status='present').count()
        absent_count = queryset.filter(status='absent').count()
        half_day_count = queryset.filter(status='half_day').count()
        leave_count = queryset.filter(status='leave').count()

        # Calculate average working hours for present employees
        avg_hours = queryset.filter(status='present').aggregate(Avg('total_hour'))['total_hour__avg'] or 0

        # Get statistics by department
        departments = Department.objects.all()
        department_stats = []

        for dept in departments:
            employees = Employee.objects.filter(department=dept)
            dept_records = queryset.filter(employee__in=employees)

            if dept_records.exists():
                department_stats.append({
                    'department_id': dept.id,
                    'department_name': dept.name,
                    'total_records': dept_records.count(),
                    'present_count': dept_records.filter(status='present').count(),
                    'absent_count': dept_records.filter(status='absent').count(),
                    'avg_hours': dept_records.filter(status='present').aggregate(Avg('total_hour'))['total_hour__avg'] or 0
                })

        # Compile statistics
        stats = {
            'total_records': total_records,
            'present_count': present_count,
            'absent_count': absent_count,
            'half_day_count': half_day_count,
            'leave_count': leave_count,
            'attendance_rate': (present_count / total_records * 100) if total_records > 0 else 0,
            'average_working_hours': avg_hours,
            'department_statistics': department_stats
        }

        return Response(stats)
