from rest_framework import serializers
from employees_app.models.skill_offering import SkillOffering


class SkillOfferingSerializer(serializers.ModelSerializer):
    """
    Serializer for the SkillOffering model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    department_name = serializers.ReadOnlyField(source='department.name')
    skill_level_display = serializers.ReadOnlyField(source='get_skill_level_display')
    actual_skill_type_display = serializers.ReadOnlyField(source='get_actual_skill_type_display')

    class Meta:
        model = SkillOffering
        fields = [
            'id', 'employee', 'employee_name', 'skill_name',
            'skill_level', 'skill_level_display', 'years_of_experience',
            'is_primary_skill', 'department', 'department_name',
            'actual_skill_type', 'actual_skill_type_display'
        ]

    def validate(self, data):
        """
        Validate that years_of_experience is positive
        """
        if 'years_of_experience' in data and data['years_of_experience'] < 0:
            raise serializers.ValidationError("Years of experience cannot be negative")

        return data
