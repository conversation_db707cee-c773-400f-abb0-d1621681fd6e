# Organization Module Schema

This document provides a visual representation of the organization module's database schema and the relationships between its models.

## Entity Relationship Diagram

```mermaid
erDiagram
    Organization ||--o{ Location : "has many"
    Organization ||--o{ BusinessUnit : "has many"
    Organization ||--o{ Designation : "has many"
    Organization ||--o{ OrganizationPolicy : "has many"
    Organization ||--o{ OrganizationDocument : "has many"
    
    Location }o--o{ OrganizationPolicy : "associated with"
    BusinessUnit }o--o{ OrganizationPolicy : "associated with"
    
    BusinessUnit ||--o{ BusinessUnit : "parent-child"
    BusinessUnit }o--|| Location : "primary location"
    BusinessUnit }o--|| Employee : "headed by"
    
    Department }o--o{ OrganizationPolicy : "associated with"
    Department }o--o{ Designation : "has many"
    Department }o--|| BusinessUnit : "belongs to"
    
    Employee }o--|| Department : "belongs to"
    
    OrganizationDocument }o--|| BusinessUnit : "related to"
    
    Organization {
        int id PK
        string name
        image logo
        string registration_number
        string gst_number
        string pan_number
        string ein_number
        enum business_type
        date establishment_date
        string industry_sector
        enum default_currency
        string default_timezone
        int fiscal_year_start_month
        int fiscal_year_start_day
        url website
        email email
        string phone
        enum status
        json custom_fields
        datetime created_at
        datetime updated_at
    }
    
    Location {
        int id PK
        int organization_id FK
        string name
        string address_line1
        string address_line2
        string city
        string state
        string country
        string postal_code
        string phone
        email email
        decimal latitude
        decimal longitude
        time working_hours_start
        time working_hours_end
        string working_days
        boolean is_headquarters
        enum status
        json custom_fields
        datetime created_at
        datetime updated_at
    }
    
    BusinessUnit {
        int id PK
        int organization_id FK
        string name
        text description
        string code
        int parent_id FK
        int head_id FK
        int primary_location_id FK
        enum status
        datetime created_at
        datetime updated_at
    }
    
    Designation {
        int id PK
        int organization_id FK
        string title
        text description
        enum level
        int department_id FK
        string pay_grade
        decimal min_salary
        decimal max_salary
        datetime created_at
        datetime updated_at
    }
    
    OrganizationPolicy {
        int id PK
        int organization_id FK
        string name
        text description
        enum policy_type
        text content
        boolean applicable_to_all
        date effective_from
        date effective_to
        datetime created_at
        datetime updated_at
    }
    
    OrganizationDocument {
        int id PK
        int organization_id FK
        string title
        text description
        enum document_type
        file file
        string version
        date effective_date
        date expiry_date
        int business_unit_id FK
        datetime created_at
        datetime updated_at
    }
    
    Department {
        int id PK
        string name
        text description
        int manager_id FK
        enum status
        int business_unit_id FK
        datetime created_at
        datetime updated_at
    }
    
    Employee {
        int id PK
        string first_name
        string last_name
        email email
        string phone
        string position
        int department_id FK
        date hire_date
        enum gender
        enum status
        image profile_picture
        file resume
        file contract
        datetime created_at
        datetime updated_at
    }
```

## Model Relationships

### Organization
- Has many Locations
- Has many BusinessUnits
- Has many Designations
- Has many OrganizationPolicies
- Has many OrganizationDocuments

### Location
- Belongs to an Organization
- Is the primary location for many BusinessUnits
- Can be associated with many OrganizationPolicies

### BusinessUnit
- Belongs to an Organization
- Can have a parent BusinessUnit
- Can have many child BusinessUnits
- Has a primary Location
- Is headed by an Employee
- Has many Departments
- Can be associated with many OrganizationPolicies
- Has many OrganizationDocuments

### Designation
- Belongs to an Organization
- Can belong to a Department

### Department
- Can belong to a BusinessUnit
- Has many Designations
- Can be associated with many OrganizationPolicies
- Has many Employees

### OrganizationPolicy
- Belongs to an Organization
- Can be associated with many Locations
- Can be associated with many Departments
- Can be associated with many BusinessUnits

### OrganizationDocument
- Belongs to an Organization
- Can be related to a BusinessUnit

### Employee
- Belongs to a Department
- Can head a BusinessUnit
