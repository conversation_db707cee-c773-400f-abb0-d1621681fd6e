# Generated by Django 5.1.7 on 2025-05-10 10:13

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employees_app', '0012_employeehike_status_alter_employeehike_approved_by'),
        ('organization_app', '0002_department'),
    ]

    operations = [
        migrations.CreateModel(
            name='Application',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('application_date', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('new', 'New'), ('screening', 'Screening'), ('shortlisted', 'Shortlisted'), ('interview', 'Interview'), ('assessment', 'Assessment'), ('offer', 'Offer'), ('hired', 'Hired'), ('rejected', 'Rejected'), ('withdrawn', 'Withdrawn'), ('on_hold', 'On Hold')], default='new', max_length=20)),
                ('current_stage', models.CharField(choices=[('application', 'Application'), ('resume_screening', 'Resume Screening'), ('phone_screening', 'Phone Screening'), ('assessment_test', 'Assessment Test'), ('first_interview', 'First Interview'), ('second_interview', 'Second Interview'), ('final_interview', 'Final Interview'), ('reference_check', 'Reference Check'), ('background_check', 'Background Check'), ('offer_stage', 'Offer Stage'), ('onboarding', 'Onboarding')], default='application', max_length=20)),
                ('cover_letter', models.TextField(blank=True, null=True)),
                ('resume', models.FileField(blank=True, null=True, upload_to='application_resumes/%Y/%m/')),
                ('is_shortlisted', models.BooleanField(default=False)),
                ('rating', models.PositiveIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('skills_match_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('expected_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('negotiated_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('available_from', models.DateField(blank=True, null=True)),
                ('notice_period', models.PositiveIntegerField(blank=True, help_text='Notice period in days', null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('rejection_stage', models.CharField(blank=True, choices=[('application', 'Application'), ('resume_screening', 'Resume Screening'), ('phone_screening', 'Phone Screening'), ('assessment_test', 'Assessment Test'), ('first_interview', 'First Interview'), ('second_interview', 'Second Interview'), ('final_interview', 'Final Interview'), ('reference_check', 'Reference Check'), ('background_check', 'Background Check'), ('offer_stage', 'Offer Stage'), ('onboarding', 'Onboarding')], max_length=20, null=True)),
                ('rejection_date', models.DateTimeField(blank=True, null=True)),
                ('withdrawal_reason', models.TextField(blank=True, null=True)),
                ('withdrawal_date', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('internal_feedback', models.TextField(blank=True, null=True)),
                ('last_status_change', models.DateTimeField(auto_now_add=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_applications', to='employees_app.employee')),
                ('rejected_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='applications_rejected', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Application',
                'verbose_name_plural': 'Applications',
                'ordering': ['-application_date'],
            },
        ),
        migrations.CreateModel(
            name='ApplicationStageHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_stage', models.CharField(blank=True, choices=[('application', 'Application'), ('resume_screening', 'Resume Screening'), ('phone_screening', 'Phone Screening'), ('assessment_test', 'Assessment Test'), ('first_interview', 'First Interview'), ('second_interview', 'Second Interview'), ('final_interview', 'Final Interview'), ('reference_check', 'Reference Check'), ('background_check', 'Background Check'), ('offer_stage', 'Offer Stage'), ('onboarding', 'Onboarding')], max_length=20, null=True)),
                ('to_stage', models.CharField(choices=[('application', 'Application'), ('resume_screening', 'Resume Screening'), ('phone_screening', 'Phone Screening'), ('assessment_test', 'Assessment Test'), ('first_interview', 'First Interview'), ('second_interview', 'Second Interview'), ('final_interview', 'Final Interview'), ('reference_check', 'Reference Check'), ('background_check', 'Background Check'), ('offer_stage', 'Offer Stage'), ('onboarding', 'Onboarding')], max_length=20)),
                ('from_status', models.CharField(blank=True, choices=[('new', 'New'), ('screening', 'Screening'), ('shortlisted', 'Shortlisted'), ('interview', 'Interview'), ('assessment', 'Assessment'), ('offer', 'Offer'), ('hired', 'Hired'), ('rejected', 'Rejected'), ('withdrawn', 'Withdrawn'), ('on_hold', 'On Hold')], max_length=20, null=True)),
                ('to_status', models.CharField(choices=[('new', 'New'), ('screening', 'Screening'), ('shortlisted', 'Shortlisted'), ('interview', 'Interview'), ('assessment', 'Assessment'), ('offer', 'Offer'), ('hired', 'Hired'), ('rejected', 'Rejected'), ('withdrawn', 'Withdrawn'), ('on_hold', 'On Hold')], max_length=20)),
                ('changed_at', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('application', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stage_history', to='recruitment_app.application')),
                ('changed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='application_stage_changes', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Application Stage History',
                'verbose_name_plural': 'Application Stage History',
                'ordering': ['-changed_at'],
            },
        ),
        migrations.CreateModel(
            name='Candidate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other'), ('prefer_not_to_say', 'Prefer Not to Say')], max_length=20, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('state', models.CharField(blank=True, max_length=100, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('headline', models.CharField(blank=True, max_length=255, null=True)),
                ('summary', models.TextField(blank=True, null=True)),
                ('total_experience', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('current_employer', models.CharField(blank=True, max_length=255, null=True)),
                ('current_designation', models.CharField(blank=True, max_length=255, null=True)),
                ('current_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('expected_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('notice_period', models.PositiveIntegerField(blank=True, help_text='Notice period in days', null=True)),
                ('resume', models.FileField(blank=True, null=True, upload_to='resumes/%Y/%m/')),
                ('cover_letter', models.FileField(blank=True, null=True, upload_to='cover_letters/%Y/%m/')),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='candidate_photos/%Y/%m/')),
                ('highest_qualification', models.CharField(blank=True, max_length=255, null=True)),
                ('referred_by', models.CharField(blank=True, max_length=255, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('blacklisted', 'Blacklisted'), ('hired', 'Hired')], default='active', max_length=20)),
                ('rating', models.PositiveIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('linkedin_profile', models.URLField(blank=True, null=True)),
                ('github_profile', models.URLField(blank=True, null=True)),
                ('portfolio_website', models.URLField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('tags', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='candidates', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Candidate',
                'verbose_name_plural': 'Candidates',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='application',
            name='candidate',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='recruitment_app.candidate'),
        ),
        migrations.CreateModel(
            name='CandidateEducation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('institution', models.CharField(max_length=255)),
                ('degree', models.CharField(max_length=255)),
                ('field_of_study', models.CharField(max_length=255)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('is_current', models.BooleanField(default=False)),
                ('grade', models.CharField(blank=True, max_length=50, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='education', to='recruitment_app.candidate')),
            ],
            options={
                'verbose_name': 'Candidate Education',
                'verbose_name_plural': 'Candidate Education',
                'ordering': ['-end_date', '-start_date'],
            },
        ),
        migrations.CreateModel(
            name='CandidateExperience',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company', models.CharField(max_length=255)),
                ('title', models.CharField(max_length=255)),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('is_current', models.BooleanField(default=False)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='experience', to='recruitment_app.candidate')),
            ],
            options={
                'verbose_name': 'Candidate Experience',
                'verbose_name_plural': 'Candidate Experience',
                'ordering': ['-end_date', '-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Interview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('round', models.PositiveIntegerField(choices=[(1, 'First Round'), (2, 'Second Round'), (3, 'Third Round'), (4, 'Fourth Round'), (5, 'Final Round')], default=1)),
                ('interview_type', models.CharField(choices=[('phone', 'Phone'), ('video', 'Video'), ('in_person', 'In Person'), ('technical', 'Technical'), ('hr', 'HR'), ('panel', 'Panel'), ('assessment', 'Assessment')], max_length=20)),
                ('scheduled_date', models.DateField()),
                ('scheduled_start_time', models.TimeField()),
                ('scheduled_end_time', models.TimeField()),
                ('timezone', models.CharField(default='UTC', max_length=50)),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('meeting_link', models.URLField(blank=True, null=True)),
                ('meeting_id', models.CharField(blank=True, max_length=100, null=True)),
                ('meeting_password', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rescheduled', 'Rescheduled'), ('no_show', 'No Show')], default='scheduled', max_length=20)),
                ('actual_start_time', models.DateTimeField(blank=True, null=True)),
                ('actual_end_time', models.DateTimeField(blank=True, null=True)),
                ('rescheduled_from', models.DateTimeField(blank=True, null=True)),
                ('rescheduled_reason', models.TextField(blank=True, null=True)),
                ('cancellation_reason', models.TextField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('instructions', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('application', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interviews', to='recruitment_app.application')),
                ('cancelled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='interviews_cancelled', to='employees_app.employee')),
                ('interviewers', models.ManyToManyField(related_name='interviews_to_conduct', to='employees_app.employee')),
                ('scheduled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='interviews_scheduled', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Interview',
                'verbose_name_plural': 'Interviews',
                'ordering': ['scheduled_date', 'scheduled_start_time'],
            },
        ),
        migrations.CreateModel(
            name='InterviewQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.TextField()),
                ('question_type', models.CharField(choices=[('technical', 'Technical'), ('behavioral', 'Behavioral'), ('situational', 'Situational'), ('experience', 'Experience-based'), ('problem_solving', 'Problem Solving'), ('other', 'Other')], max_length=20)),
                ('answer', models.TextField(blank=True, null=True)),
                ('rating', models.PositiveIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('interview', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='recruitment_app.interview')),
            ],
            options={
                'verbose_name': 'Interview Question',
                'verbose_name_plural': 'Interview Questions',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='JobPosting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('slug', models.SlugField(max_length=255, unique=True)),
                ('description', models.TextField()),
                ('requirements', models.TextField()),
                ('responsibilities', models.TextField()),
                ('qualifications', models.TextField()),
                ('benefits', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('expired', 'Expired'), ('closed', 'Closed')], default='draft', max_length=20)),
                ('visibility', models.CharField(choices=[('public', 'Public'), ('internal', 'Internal Only'), ('private', 'Private/Referral Only')], default='public', max_length=20)),
                ('is_remote', models.BooleanField(default=False)),
                ('allows_remote', models.BooleanField(default=False)),
                ('is_featured', models.BooleanField(default=False)),
                ('published_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('display_salary', models.BooleanField(default=False)),
                ('salary_min', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('salary_max', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('salary_currency', models.CharField(default='USD', max_length=3)),
                ('salary_period', models.CharField(default='yearly', max_length=20)),
                ('meta_title', models.CharField(blank=True, max_length=255, null=True)),
                ('meta_description', models.TextField(blank=True, null=True)),
                ('meta_keywords', models.CharField(blank=True, max_length=255, null=True)),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('application_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='job_postings_created', to='employees_app.employee')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='job_postings_updated', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Job Posting',
                'verbose_name_plural': 'Job Postings',
                'ordering': ['-published_date', 'title'],
            },
        ),
        migrations.AddField(
            model_name='application',
            name='job_posting',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='recruitment_app.jobposting'),
        ),
        migrations.CreateModel(
            name='JobRequisition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('employment_type', models.CharField(choices=[('full_time', 'Full Time'), ('part_time', 'Part Time'), ('contract', 'Contract'), ('internship', 'Internship'), ('temporary', 'Temporary')], max_length=20)),
                ('experience_level', models.CharField(choices=[('entry', 'Entry Level'), ('junior', 'Junior'), ('mid', 'Mid Level'), ('senior', 'Senior'), ('lead', 'Lead'), ('manager', 'Manager'), ('director', 'Director'), ('executive', 'Executive')], max_length=20)),
                ('min_experience', models.PositiveIntegerField(default=0)),
                ('max_experience', models.PositiveIntegerField(blank=True, null=True)),
                ('min_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('max_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('number_of_openings', models.PositiveIntegerField(default=1)),
                ('description', models.TextField()),
                ('requirements', models.TextField()),
                ('responsibilities', models.TextField()),
                ('qualifications', models.TextField()),
                ('skills_required', models.TextField()),
                ('benefits', models.TextField(blank=True, null=True)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('requested_date', models.DateField(auto_now_add=True)),
                ('target_hiring_date', models.DateField()),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('on_hold', 'On Hold'), ('cancelled', 'Cancelled'), ('closed', 'Closed')], default='draft', max_length=20)),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('is_replacement', models.BooleanField(default=False)),
                ('budget_approved', models.BooleanField(default=False)),
                ('budget_code', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='requisitions_approved', to='employees_app.employee')),
                ('business_unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_requisitions', to='organization_app.businessunit')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_requisitions', to='organization_app.department')),
                ('designation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_requisitions', to='organization_app.designation')),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_requisitions', to='organization_app.location')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_requisitions', to='organization_app.organization')),
                ('replacing_employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replacement_requisitions', to='employees_app.employee')),
                ('requested_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='requisitions_requested', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Job Requisition',
                'verbose_name_plural': 'Job Requisitions',
                'ordering': ['-requested_date', 'status'],
            },
        ),
        migrations.AddField(
            model_name='jobposting',
            name='job_requisition',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='job_postings', to='recruitment_app.jobrequisition'),
        ),
        migrations.CreateModel(
            name='Offer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('offer_date', models.DateField()),
                ('expiry_date', models.DateField()),
                ('position', models.CharField(max_length=255)),
                ('department', models.CharField(max_length=255)),
                ('location', models.CharField(max_length=255)),
                ('employment_type', models.CharField(max_length=50)),
                ('base_salary', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('bonus', models.DecimalField(decimal_places=2, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('stock_options', models.DecimalField(decimal_places=2, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('benefits', models.TextField(blank=True, null=True)),
                ('joining_date', models.DateField()),
                ('probation_period', models.PositiveIntegerField(default=3, help_text='Probation period in months')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Approval'), ('approved', 'Approved'), ('sent', 'Sent'), ('accepted', 'Accepted'), ('negotiating', 'Negotiating'), ('declined', 'Declined'), ('expired', 'Expired'), ('withdrawn', 'Withdrawn')], default='draft', max_length=20)),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('offer_letter', models.FileField(blank=True, null=True, upload_to='offer_letters/%Y/%m/')),
                ('response_date', models.DateField(blank=True, null=True)),
                ('negotiation_details', models.TextField(blank=True, null=True)),
                ('decline_reason', models.TextField(blank=True, null=True)),
                ('withdrawal_reason', models.TextField(blank=True, null=True)),
                ('withdrawal_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('application', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='offer', to='recruitment_app.application')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='offers_approved', to='employees_app.employee')),
                ('withdrawn_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='offers_withdrawn', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Offer',
                'verbose_name_plural': 'Offers',
                'ordering': ['-offer_date'],
            },
        ),
        migrations.CreateModel(
            name='Onboarding',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('documents_submitted', models.BooleanField(default=False)),
                ('documents_verified', models.BooleanField(default=False)),
                ('equipment_assigned', models.BooleanField(default=False)),
                ('system_access_provided', models.BooleanField(default=False)),
                ('orientation_completed', models.BooleanField(default=False)),
                ('training_completed', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding', to='recruitment_app.candidate')),
                ('hr_buddy', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='hr_onboardings', to='employees_app.employee')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='manager_onboardings', to='employees_app.employee')),
                ('offer', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding', to='recruitment_app.offer')),
            ],
            options={
                'verbose_name': 'Onboarding',
                'verbose_name_plural': 'Onboardings',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='OnboardingTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('category', models.CharField(choices=[('documentation', 'Documentation'), ('equipment', 'Equipment'), ('access', 'System Access'), ('training', 'Training'), ('introduction', 'Introduction'), ('other', 'Other')], max_length=20)),
                ('due_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('completed_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_onboarding_tasks', to='employees_app.employee')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_onboarding_tasks', to='employees_app.employee')),
                ('onboarding', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='recruitment_app.onboarding')),
            ],
            options={
                'verbose_name': 'Onboarding Task',
                'verbose_name_plural': 'Onboarding Tasks',
                'ordering': ['due_date', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='RecruitmentSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('source_type', models.CharField(choices=[('career_page', 'Career Page'), ('job_board', 'Job Board'), ('social_media', 'Social Media'), ('referral', 'Employee Referral'), ('agency', 'Recruitment Agency'), ('university', 'University/College'), ('event', 'Job Fair/Event'), ('direct', 'Direct Application'), ('other', 'Other')], max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('api_key', models.CharField(blank=True, max_length=255, null=True)),
                ('api_endpoint', models.URLField(blank=True, null=True)),
                ('integration_active', models.BooleanField(default=False)),
                ('cost_per_post', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('cost_per_hire', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recruitment_sources', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Recruitment Source',
                'verbose_name_plural': 'Recruitment Sources',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='jobposting',
            name='sources',
            field=models.ManyToManyField(blank=True, related_name='job_postings', to='recruitment_app.recruitmentsource'),
        ),
        migrations.AddField(
            model_name='candidate',
            name='source',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='candidates', to='recruitment_app.recruitmentsource'),
        ),
        migrations.AddField(
            model_name='application',
            name='source',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='applications', to='recruitment_app.recruitmentsource'),
        ),
        migrations.CreateModel(
            name='Skill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('skill_type', models.CharField(choices=[('technical', 'Technical'), ('soft', 'Soft Skill'), ('language', 'Language'), ('certification', 'Certification'), ('other', 'Other')], max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='skills', to='organization_app.organization')),
                ('parent_skill', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child_skills', to='recruitment_app.skill')),
            ],
            options={
                'verbose_name': 'Skill',
                'verbose_name_plural': 'Skills',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CandidateSkill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('proficiency', models.PositiveIntegerField(choices=[(1, 'Beginner'), (2, 'Elementary'), (3, 'Intermediate'), (4, 'Advanced'), (5, 'Expert')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('years_of_experience', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('is_primary', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='candidate_skills', to='recruitment_app.candidate')),
                ('skill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='candidate_skills', to='recruitment_app.skill')),
            ],
            options={
                'verbose_name': 'Candidate Skill',
                'verbose_name_plural': 'Candidate Skills',
                'ordering': ['-proficiency', '-years_of_experience'],
                'unique_together': {('candidate', 'skill')},
            },
        ),
        migrations.AddField(
            model_name='candidate',
            name='skills',
            field=models.ManyToManyField(related_name='candidates', through='recruitment_app.CandidateSkill', to='recruitment_app.skill'),
        ),
        migrations.CreateModel(
            name='InterviewFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_rating', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('recommendation', models.CharField(choices=[('strong_hire', 'Strong Hire'), ('hire', 'Hire'), ('neutral', 'Neutral'), ('reject', 'Reject'), ('strong_reject', 'Strong Reject')], max_length=20)),
                ('technical_skills', models.TextField(blank=True, null=True)),
                ('communication_skills', models.TextField(blank=True, null=True)),
                ('problem_solving', models.TextField(blank=True, null=True)),
                ('cultural_fit', models.TextField(blank=True, null=True)),
                ('strengths', models.TextField(blank=True, null=True)),
                ('weaknesses', models.TextField(blank=True, null=True)),
                ('comments', models.TextField(blank=True, null=True)),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('interview', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='recruitment_app.interview')),
                ('interviewer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interview_feedback', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Interview Feedback',
                'verbose_name_plural': 'Interview Feedback',
                'ordering': ['-submitted_at'],
                'unique_together': {('interview', 'interviewer')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='application',
            unique_together={('job_posting', 'candidate')},
        ),
    ]
