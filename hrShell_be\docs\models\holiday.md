# Holiday Model

The Holiday model represents holiday information for different locations.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the holiday | Yes |
| `date` | Date | Date of the holiday | Yes |
| `description` | Text | Description of the holiday | No |
| `organization` | ForeignKey | Organization this holiday belongs to | Yes |
| `location` | ForeignKey | Specific location this holiday applies to | No |
| `holiday_type` | String | Type of holiday (choices: national, regional, religious, company) | Yes (default: company) |
| `is_recurring` | Boolean | Whether this holiday recurs every year | Yes (default: False) |
| `is_half_day` | Boolean | Whether this is a half-day holiday | Yes (default: False) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this holiday belongs to |
| `location` | Location | The specific location this holiday applies to (optional) |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name, date, and location of the holiday |

## Example

```python
holiday = Holiday.objects.create(
    name="New Year's Day",
    date="2023-01-01",
    description="New Year's Day celebration",
    organization=acme_corp,
    holiday_type="national",
    is_recurring=True,
    is_half_day=False
)
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/holidays/` | GET | List all holidays |
| `/api/v1/holidays/` | POST | Create a new holiday |
| `/api/v1/holidays/{id}/` | GET | Retrieve a specific holiday |
| `/api/v1/holidays/{id}/` | PUT | Update a specific holiday |
| `/api/v1/holidays/{id}/` | DELETE | Delete a specific holiday |
| `/api/v1/holidays/organization/{organization_id}/` | GET | List holidays for a specific organization |
| `/api/v1/holidays/location/{location_id}/` | GET | List holidays for a specific location |
| `/api/v1/holidays/year/{year}/` | GET | List holidays for a specific year |
| `/api/v1/holidays/upcoming-holidays/` | GET | List upcoming holidays |
| `/api/v1/holidays/recurring-holidays/` | GET | List recurring holidays |

## Notes

- If `location` is not specified, the holiday applies to all locations in the organization.
- If `is_recurring` is True, the holiday will be considered for all years, not just the year specified in the `date` field.
- The `year/{year}/` endpoint will include both holidays specifically for that year and recurring holidays from previous years adjusted to the specified year.
