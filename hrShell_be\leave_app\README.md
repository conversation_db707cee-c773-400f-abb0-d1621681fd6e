# Leave Management Module

The Leave Management Module is a comprehensive solution for managing employee leaves, leave policies, holidays, and week offs in an organization.

## Features

- **Leave Types**: Define different types of leave (e.g., Casual, Sick, Annual)
- **Leave Policies**: Create policies for different departments, locations, and designations
- **Leave Balances**: Track employee leave balances for each leave type
- **Leave Requests**: Process leave applications with multi-level approval
- **Holidays**: Manage organization and location-specific holidays
- **Week Offs**: Configure weekly off days for different departments and locations

## Models

- **LeaveType**: Represents different types of leave
- **LeavePolicy**: Represents leave policies with rules for different departments, locations, etc.
- **LeaveBalance**: Represents employee leave balances for each leave type and year
- **LeaveRequest**: Represents leave applications submitted by employees
- **LeaveApproval**: Represents multi-level approval records for leave requests
- **Holiday**: Represents holiday information for different locations
- **WeekOff**: Represents weekly off days for different departments and locations

## API Endpoints

### Leave Types

- `GET /api/v1/leave-types/`: List all leave types
- `POST /api/v1/leave-types/`: Create a new leave type
- `GET /api/v1/leave-types/{id}/`: Retrieve a specific leave type
- `PUT /api/v1/leave-types/{id}/`: Update a specific leave type
- `DELETE /api/v1/leave-types/{id}/`: Delete a specific leave type
- `GET /api/v1/leave-types/active-leave-types/`: List all active leave types
- `GET /api/v1/leave-types/inactive-leave-types/`: List all inactive leave types
- `GET /api/v1/leave-types/paid-leave-types/`: List all paid leave types
- `GET /api/v1/leave-types/unpaid-leave-types/`: List all unpaid leave types

### Leave Policies

- `GET /api/v1/leave-policies/`: List all leave policies
- `POST /api/v1/leave-policies/`: Create a new leave policy
- `GET /api/v1/leave-policies/{id}/`: Retrieve a specific leave policy
- `PUT /api/v1/leave-policies/{id}/`: Update a specific leave policy
- `DELETE /api/v1/leave-policies/{id}/`: Delete a specific leave policy
- `GET /api/v1/leave-policies/organization/{organization_id}/`: List leave policies for a specific organization
- `GET /api/v1/leave-policies/department/{department_id}/`: List leave policies for a specific department
- `GET /api/v1/leave-policies/location/{location_id}/`: List leave policies for a specific location
- `GET /api/v1/leave-policies/designation/{designation_id}/`: List leave policies for a specific designation
- `GET /api/v1/leave-policies/business-unit/{business_unit_id}/`: List leave policies for a specific business unit
- `GET /api/v1/leave-policies/employee-type/{employee_type}/`: List leave policies for a specific employee type

### Leave Balances

- `GET /api/v1/leave-balances/`: List all leave balances
- `POST /api/v1/leave-balances/`: Create a new leave balance
- `GET /api/v1/leave-balances/{id}/`: Retrieve a specific leave balance
- `PUT /api/v1/leave-balances/{id}/`: Update a specific leave balance
- `DELETE /api/v1/leave-balances/{id}/`: Delete a specific leave balance
- `GET /api/v1/leave-balances/employee/{employee_id}/`: List leave balances for a specific employee
- `GET /api/v1/leave-balances/employee/{employee_id}/year/{year}/`: List leave balances for a specific employee and year
- `GET /api/v1/leave-balances/current-year-balances/`: List leave balances for the current year
- `POST /api/v1/leave-balances/{id}/adjust-balance/`: Adjust a leave balance

### Leave Requests

- `GET /api/v1/leave-requests/`: List all leave requests
- `POST /api/v1/leave-requests/`: Create a new leave request
- `GET /api/v1/leave-requests/{id}/`: Retrieve a specific leave request
- `PUT /api/v1/leave-requests/{id}/`: Update a specific leave request
- `DELETE /api/v1/leave-requests/{id}/`: Delete a specific leave request
- `GET /api/v1/leave-requests/employee/{employee_id}/`: List leave requests for a specific employee
- `GET /api/v1/leave-requests/pending-requests/`: List all pending leave requests
- `GET /api/v1/leave-requests/approved-requests/`: List all approved leave requests
- `GET /api/v1/leave-requests/rejected-requests/`: List all rejected leave requests
- `GET /api/v1/leave-requests/cancelled-requests/`: List all cancelled leave requests
- `POST /api/v1/leave-requests/{id}/approve-request/`: Approve a leave request
- `POST /api/v1/leave-requests/{id}/reject-request/`: Reject a leave request
- `POST /api/v1/leave-requests/{id}/cancel-request/`: Cancel a leave request
- `GET /api/v1/leave-requests/date-range-requests/`: List leave requests within a date range

### Leave Approvals

- `GET /api/v1/leave-approvals/`: List all leave approvals
- `POST /api/v1/leave-approvals/`: Create a new leave approval
- `GET /api/v1/leave-approvals/{id}/`: Retrieve a specific leave approval
- `PUT /api/v1/leave-approvals/{id}/`: Update a specific leave approval
- `DELETE /api/v1/leave-approvals/{id}/`: Delete a specific leave approval
- `GET /api/v1/leave-approvals/approver/{approver_id}/`: List leave approvals for a specific approver
- `GET /api/v1/leave-approvals/pending-approvals/`: List all pending leave approvals
- `POST /api/v1/leave-approvals/{id}/approve/`: Approve a leave approval
- `POST /api/v1/leave-approvals/{id}/reject/`: Reject a leave approval
- `POST /api/v1/leave-approvals/{id}/skip/`: Skip a leave approval

### Holidays

- `GET /api/v1/holidays/`: List all holidays
- `POST /api/v1/holidays/`: Create a new holiday
- `GET /api/v1/holidays/{id}/`: Retrieve a specific holiday
- `PUT /api/v1/holidays/{id}/`: Update a specific holiday
- `DELETE /api/v1/holidays/{id}/`: Delete a specific holiday
- `GET /api/v1/holidays/organization/{organization_id}/`: List holidays for a specific organization
- `GET /api/v1/holidays/location/{location_id}/`: List holidays for a specific location
- `GET /api/v1/holidays/year/{year}/`: List holidays for a specific year
- `GET /api/v1/holidays/upcoming-holidays/`: List upcoming holidays
- `GET /api/v1/holidays/recurring-holidays/`: List recurring holidays

### Week Offs

- `GET /api/v1/week-offs/`: List all week offs
- `POST /api/v1/week-offs/`: Create a new week off
- `GET /api/v1/week-offs/{id}/`: Retrieve a specific week off
- `PUT /api/v1/week-offs/{id}/`: Update a specific week off
- `DELETE /api/v1/week-offs/{id}/`: Delete a specific week off
- `GET /api/v1/week-offs/organization/{organization_id}/`: List week offs for a specific organization
- `GET /api/v1/week-offs/location/{location_id}/`: List week offs for a specific location
- `GET /api/v1/week-offs/department/{department_id}/`: List week offs for a specific department
- `GET /api/v1/week-offs/employee/{employee_id}/`: List week offs for a specific employee

## Documentation

For detailed documentation, please refer to:

- [Leave Management Guide](../docs/guides/leave_management_guide.md)
- [Leave Management API Reference](../docs/api/leave_management_api.md)
- [Leave Management Schema](../docs/models/leave_management_schema.md)
- [Leave Management Visual Schema](../docs/models/leave_management_schema_visual.md)

## Model Documentation

- [LeaveType](../docs/models/leave_type.md)
- [LeavePolicy](../docs/models/leave_policy.md)
- [LeaveBalance](../docs/models/leave_balance.md)
- [LeaveRequest](../docs/models/leave_request.md)
- [LeaveApproval](../docs/models/leave_approval.md)
- [Holiday](../docs/models/holiday.md)
- [WeekOff](../docs/models/week_off.md)
