from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from django.db.models import Count, Q
from django_filters.rest_framework import DjangoFilterBackend
from employees_app.models.employee_documents import EmployeeDocument
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from employees_app.serializers.employee_document_serializer import EmployeeDocumentSerializer
from employees_app.filters import EmployeeDocumentFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi


class EmployeeDocumentViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Document Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing EmployeeDocument instances.

    Additional actions:
    - employee_documents: Get documents for a specific employee
    - department_documents: Get documents for employees in a specific department
    - document_statistics: Get statistics about documents
    - missing_documents: Get employees with missing documents
    """
    queryset = EmployeeDocument.objects.all()
    serializer_class = EmployeeDocumentSerializer
    permission_classes = []  # Temporarily disabled for testing
    # parser_classes = (MultiPartParser, FormParser)  # Temporarily commented for Swagger
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_class = EmployeeDocumentFilter
    search_fields = ['employee__first_name', 'employee__last_name']

    @swagger_auto_schema(
        tags=['Document Management'],
        operation_description="List all employee documents with file size information",
        operation_summary="List Employee Documents",
        responses={
            200: openapi.Response(
                description="List of employee documents",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'count': openapi.Schema(type=openapi.TYPE_INTEGER),
                        'next': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                        'previous': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                        'results': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                    'employee': openapi.Schema(type=openapi.TYPE_INTEGER),
                                    'employee_name': openapi.Schema(type=openapi.TYPE_STRING),
                                    'document_type': openapi.Schema(type=openapi.TYPE_STRING),
                                    'document_type_display': openapi.Schema(type=openapi.TYPE_STRING),
                                    'file': openapi.Schema(type=openapi.TYPE_STRING),
                                    'file_url': openapi.Schema(type=openapi.TYPE_STRING),
                                    'file_size': openapi.Schema(type=openapi.TYPE_INTEGER, description='File size in bytes'),
                                    'file_size_formatted': openapi.Schema(type=openapi.TYPE_STRING, description='Human-readable file size (e.g., "1.5 MB")'),
                                    'document_name': openapi.Schema(type=openapi.TYPE_STRING),
                                    'description': openapi.Schema(type=openapi.TYPE_STRING),
                                    'upload_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                                    'is_verified': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                    'created_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
                                    'updated_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time')
                                }
                            )
                        )
                    }
                )
            )
        }
    )
    def list(self, request, *args, **kwargs):
        """List all employee documents with file size information"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def create(self, request, *args, **kwargs):
        """Create a new employee document"""
        # Debug: Print request data
        print("Request data:", request.data)
        print("Request files:", request.FILES)

        # Ensure we have the required fields
        if 'employee' not in request.data:
            return Response(
                {"error": "Employee ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if 'document_type' not in request.data:
            return Response(
                {"error": "Document type is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Handle file upload
        file_obj = request.FILES.get('file')
        if not file_obj:
            return Response(
                {"error": "File is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create the document instance
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # Save the document with the file
            document = serializer.save()

            # Ensure the file is properly saved
            if file_obj:
                document.file = file_obj
                document.save()

            # Return the created document with proper context for file URLs
            response_serializer = self.get_serializer(document, context={'request': request})
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(tags=['Document Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Document Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_documents(self, request, employee_id=None):
        """
        Get documents for a specific employee
        """
        try:
            employee = Employee.objects.get(pk=employee_id)
        except Employee.DoesNotExist:
            return Response(
                {"detail": "Employee not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            documents = self.queryset.get(employee=employee)
            serializer = self.get_serializer(documents)
            return Response(serializer.data)
        except EmployeeDocument.DoesNotExist:
            return Response(
                {"detail": "Employee documents not found"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_documents(self, request, department_id=None):
        """
        Get documents for employees in a specific department
        """
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return Response(
                {"detail": "Department not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get all employees in the department
        employees = Employee.objects.filter(department=department)

        # Get documents for these employees
        documents = self.queryset.filter(employee__in=employees)
        page = self.paginate_queryset(documents)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(documents, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def document_statistics(self, request):
        """
        Get statistics about documents
        """
        # Get query parameters
        department_id = request.query_params.get('department_id', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Calculate statistics
        total_records = queryset.count()

        # Count documents by type
        resume_count = queryset.exclude(resume='').count()
        contract_count = queryset.exclude(contract='').count()
        offer_letter_count = queryset.exclude(signed_offer_letter='').count()
        experience_cert_count = queryset.exclude(experience_certificates='').count()
        qualification_cert_count = queryset.exclude(qualification_certificates='').count()

        # Get statistics by department
        departments = Department.objects.all()
        department_stats = []

        for dept in departments:
            employees = Employee.objects.filter(department=dept)
            dept_records = queryset.filter(employee__in=employees)

            if dept_records.exists():
                department_stats.append({
                    'department_id': dept.id,
                    'department_name': dept.name,
                    'total_records': dept_records.count(),
                    'resume_count': dept_records.exclude(resume='').count(),
                    'contract_count': dept_records.exclude(contract='').count(),
                    'offer_letter_count': dept_records.exclude(signed_offer_letter='').count()
                })

        # Compile statistics
        stats = {
            'total_records': total_records,
            'document_counts': {
                'resume': resume_count,
                'contract': contract_count,
                'signed_offer_letter': offer_letter_count,
                'experience_certificates': experience_cert_count,
                'qualification_certificates': qualification_cert_count
            },
            'completion_rates': {
                'resume': (resume_count / total_records * 100) if total_records > 0 else 0,
                'contract': (contract_count / total_records * 100) if total_records > 0 else 0,
                'signed_offer_letter': (offer_letter_count / total_records * 100) if total_records > 0 else 0,
                'experience_certificates': (experience_cert_count / total_records * 100) if total_records > 0 else 0,
                'qualification_certificates': (qualification_cert_count / total_records * 100) if total_records > 0 else 0
            },
            'department_statistics': department_stats
        }

        return Response(stats)

    @action(detail=False, methods=['get'])
    def missing_documents(self, request):
        """
        Get employees with missing documents
        """
        # Get query parameters
        document_type = request.query_params.get('document_type', None)
        department_id = request.query_params.get('department_id', None)

        # Base queryset
        queryset = self.queryset

        # Filter by department if provided
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                employees = Employee.objects.filter(department=department)
                queryset = queryset.filter(employee__in=employees)
            except Department.DoesNotExist:
                return Response(
                    {"detail": "Department not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        # Filter by document type if provided
        if document_type:
            if document_type not in ['resume', 'contract', 'signed_offer_letter', 'experience_certificates', 'qualification_certificates']:
                return Response(
                    {"detail": "Invalid document type"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            missing_documents = queryset.filter(**{document_type: ''})
        else:
            # Get employees missing any document
            missing_documents = queryset.filter(
                Q(resume='') |
                Q(contract='') |
                Q(signed_offer_letter='') |
                Q(experience_certificates='') |
                Q(qualification_certificates='')
            )

        page = self.paginate_queryset(missing_documents)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(missing_documents, many=True)
        return Response(serializer.data)
