from rest_framework import serializers
from leave_app.models.leave_balance import LeaveBalance


class LeaveBalanceSerializer(serializers.ModelSerializer):
    """
    Serializer for the LeaveBalance model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    leave_type_name = serializers.ReadOnlyField(source='leave_type.name')
    closing_balance = serializers.ReadOnlyField()
    
    class Meta:
        model = LeaveBalance
        fields = [
            'id', 'employee', 'employee_name', 'leave_type', 'leave_type_name',
            'year', 'opening_balance', 'accrued', 'used', 'adjusted', 'encashed',
            'closing_balance', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate that the employee and leave_type combination is unique for the year
        """
        employee = data.get('employee')
        leave_type = data.get('leave_type')
        year = data.get('year')
        
        # Check if we're updating an existing instance
        instance = self.instance
        if instance:
            # If we're not changing employee, leave_type, or year, no need to check
            if (instance.employee == employee and 
                instance.leave_type == leave_type and 
                instance.year == year):
                return data
        
        # Check if a balance already exists for this employee, leave_type, and year
        if LeaveBalance.objects.filter(
            employee=employee,
            leave_type=leave_type,
            year=year
        ).exists():
            raise serializers.ValidationError(
                "A leave balance already exists for this employee, leave type, and year"
            )
        
        return data
