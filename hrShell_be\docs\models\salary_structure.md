# Salary Structure Models

## SalaryComponent

The SalaryComponent model defines individual salary components like Basic, HRA, Allowances, etc.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the component | Yes |
| `code` | String | Unique code for the component | Yes |
| `description` | Text | Description of the component | No |
| `component_type` | String | Type of component (earning/deduction) | Yes |
| `calculation_type` | String | How the component is calculated (fixed/percentage/formula) | Yes |
| `value` | Decimal | Value of the component (amount or percentage) | Yes |
| `formula` | Text | Custom formula for calculation | No |
| `is_taxable` | Boolean | Whether the component is taxable | Yes (default: True) |
| `is_fixed` | Boolean | Whether the component is fixed | Yes (default: True) |
| `organization` | ForeignKey | Organization this component belongs to | Yes |
| `is_active` | Boolean | Whether the component is active | Yes (default: True) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Component Type Choices
- `earning`: Earning component (e.g., Basic, HRA)
- `deduction`: Deduction component (e.g., PF, TDS)

#### Calculation Type Choices
- `fixed`: Fixed amount
- `percentage`: Percentage of Basic
- `percentage_gross`: Percentage of Gross
- `formula`: Custom formula

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this component belongs to |
| `salary_structures` | SalaryStructure | The salary structures this component is part of (M2M) |
| `employee_values` | EmployeeSalaryComponent | The employee-specific values for this component |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name and code of the component |

### Example

```python
basic_pay = SalaryComponent.objects.create(
    name="Basic Pay",
    code="BASIC",
    description="Basic salary component",
    component_type="earning",
    calculation_type="fixed",
    value=50000.00,
    is_taxable=True,
    is_fixed=True,
    organization=acme_corp
)

hra = SalaryComponent.objects.create(
    name="House Rent Allowance",
    code="HRA",
    description="House rent allowance",
    component_type="earning",
    calculation_type="percentage",
    value=40.00,  # 40% of Basic
    is_taxable=False,
    is_fixed=True,
    organization=acme_corp
)
```

## SalaryStructure

The SalaryStructure model defines salary structures that can be assigned to employees.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the structure | Yes |
| `code` | String | Unique code for the structure | Yes |
| `description` | Text | Description of the structure | No |
| `organization` | ForeignKey | Organization this structure belongs to | Yes |
| `salary_type` | String | Type of salary (monthly/hourly/daily) | Yes (default: monthly) |
| `components` | ManyToManyField | Components in this structure | Yes |
| `is_active` | Boolean | Whether the structure is active | Yes (default: True) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Salary Type Choices
- `monthly`: Monthly salary
- `hourly`: Hourly wage
- `daily`: Daily wage

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this structure belongs to |
| `components` | SalaryComponent | The components in this structure (M2M) |
| `employee_salaries` | EmployeeSalary | The employee salaries using this structure |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name and code of the structure |

### Example

```python
standard_structure = SalaryStructure.objects.create(
    name="Standard Salary Structure",
    code="STD",
    description="Standard salary structure for employees",
    organization=acme_corp,
    salary_type="monthly"
)

# Add components to the structure
standard_structure.components.add(basic_pay, hra)
```

## API Endpoints

### SalaryComponent Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/salary-components/` | GET | List all salary components |
| `/api/v1/salary-components/` | POST | Create a new salary component |
| `/api/v1/salary-components/{id}/` | GET | Retrieve a specific salary component |
| `/api/v1/salary-components/{id}/` | PUT | Update a specific salary component |
| `/api/v1/salary-components/{id}/` | DELETE | Delete a specific salary component |
| `/api/v1/salary-components/earnings/` | GET | List all earning components |
| `/api/v1/salary-components/deductions/` | GET | List all deduction components |
| `/api/v1/salary-components/active/` | GET | List all active components |
| `/api/v1/salary-components/taxable/` | GET | List all taxable components |
| `/api/v1/salary-components/organization/{organization_id}/` | GET | List all components for a specific organization |

### SalaryStructure Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/salary-structures/` | GET | List all salary structures |
| `/api/v1/salary-structures/` | POST | Create a new salary structure |
| `/api/v1/salary-structures/{id}/` | GET | Retrieve a specific salary structure |
| `/api/v1/salary-structures/{id}/` | PUT | Update a specific salary structure |
| `/api/v1/salary-structures/{id}/` | DELETE | Delete a specific salary structure |
| `/api/v1/salary-structures/active/` | GET | List all active structures |
| `/api/v1/salary-structures/organization/{organization_id}/` | GET | List all structures for a specific organization |
| `/api/v1/salary-structures/{id}/components/` | GET | List all components for a specific structure |
