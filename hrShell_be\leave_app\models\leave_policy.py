from django.db import models
from enum import Enum


class AccrualMethod(Enum):
    YEARLY = 'yearly'
    MONTHLY = 'monthly'
    QUARTERLY = 'quarterly'
    BIANNUALLY = 'biannually'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class AccrualFrequency(Enum):
    START_OF_PERIOD = 'start_of_period'
    END_OF_PERIOD = 'end_of_period'
    PRORATED = 'prorated'
    
    @classmethod
    def choices(cls):
        return [(key.value, key.name) for key in cls]


class LeavePolicy(models.Model):
    """
    Leave Policy model for storing leave policies
    """
    # Basic information
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    
    # Associations
    organization = models.ForeignKey(
        'organization_app.Organization',
        on_delete=models.CASCADE,
        related_name='leave_policies'
    )
    leave_type = models.ForeignKey(
        'leave_app.LeaveType',
        on_delete=models.CASCADE,
        related_name='policies'
    )
    
    # Specific associations (optional)
    department = models.ForeignKey(
        'employees_app.Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='leave_policies'
    )
    location = models.ForeignKey(
        'organization_app.Location',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='leave_policies'
    )
    designation = models.ForeignKey(
        'organization_app.Designation',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='leave_policies'
    )
    business_unit = models.ForeignKey(
        'organization_app.BusinessUnit',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='leave_policies'
    )
    
    # Employee type (e.g., Full-time, Part-time, Intern)
    employee_type = models.CharField(max_length=50, blank=True, null=True)
    
    # Accrual settings
    accrual_method = models.CharField(
        max_length=20,
        choices=AccrualMethod.choices(),
        default=AccrualMethod.YEARLY.value
    )
    accrual_frequency = models.CharField(
        max_length=20,
        choices=AccrualFrequency.choices(),
        default=AccrualFrequency.START_OF_PERIOD.value
    )
    max_accrual = models.PositiveIntegerField(default=0)
    
    # Carry forward settings
    carry_forward_limit = models.PositiveIntegerField(default=0)
    carry_forward_expiry_months = models.PositiveIntegerField(
        default=0,
        help_text="Number of months after which carried forward leaves expire (0 = never)"
    )
    
    # Encashment settings
    encashment_limit = models.PositiveIntegerField(default=0)
    
    # Probation settings
    probation_period_days = models.PositiveIntegerField(default=90)
    
    # Sandwich rule (if weekend/holiday falls between leave days)
    apply_sandwich_rule = models.BooleanField(
        default=False,
        help_text="If True, weekends/holidays between leave days are counted as leave"
    )
    
    # Approval settings
    requires_approval = models.BooleanField(default=True)
    auto_approve_after_days = models.PositiveIntegerField(
        default=0,
        help_text="Number of days after which leave is auto-approved (0 = never)"
    )
    
    # Advance application settings
    min_days_before_application = models.PositiveIntegerField(
        default=0,
        help_text="Minimum days before leave date to apply"
    )
    max_days_before_application = models.PositiveIntegerField(
        default=0,
        help_text="Maximum days before leave date to apply (0 = no limit)"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = "Leave Policy"
        verbose_name_plural = "Leave Policies"
        unique_together = ('organization', 'leave_type', 'department', 'location', 'designation', 'business_unit', 'employee_type')
    
    def __str__(self):
        return f"{self.name} - {self.leave_type.name} ({self.organization.name})"
