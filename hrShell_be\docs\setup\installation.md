# Installation

This guide will help you install and set up the HR Management API.

## Prerequisites

Before installing the HR Management API, make sure you have the following prerequisites:

- Python 3.8 or higher
- PostgreSQL 12 or higher
- pip (Python package manager)
- virtualenv (recommended)

## Step 1: Clone the Repository

```bash
git clone https://github.com/yourusername/hrShell_be.git
cd hrShell_be
```

## Step 2: Create a Virtual Environment

```bash
python -m venv hrShellEnv
```

## Step 3: Activate the Virtual Environment

### On Windows:

```bash
hrShellEnv\Scripts\activate
```

### On macOS and Linux:

```bash
source hrShellEnv/bin/activate
```

## Step 4: Install Dependencies

```bash
pip install -r requirements.txt
```

If you don't have a requirements.txt file, install the required packages manually:

```bash
pip install django djangorestframework djangorestframework-simplejwt django-filter pillow psycopg2-binary
```

## Step 5: Set Up the Database

### Create a PostgreSQL Database

```bash
createdb shell_dwh
```

Or use a PostgreSQL client to create the database.

### Configure Database Settings

Edit the `hrShell_be/settings.py` file to configure your database settings:

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'shell_dwh',
        'USER': 'your_database_user',
        'PASSWORD': 'your_database_password',
        'HOST': '127.0.0.1',
        'PORT': '5432',
    }
}
```

## Step 6: Run Migrations

```bash
python manage.py makemigrations
python manage.py migrate
```

## Step 7: Create a Superuser

```bash
python manage.py createsuperuser
```

Follow the prompts to create a superuser account.

## Step 8: Run the Development Server

```bash
python manage.py runserver
```

The API will be available at http://127.0.0.1:8000/.

## Next Steps

- [Configure the API](configuration.md)
- [Run the API in production](running.md)
- [Make your first API call](../guides/first-api-call.md)
