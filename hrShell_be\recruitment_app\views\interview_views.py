from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from recruitment_app.models.interview import Interview, InterviewFeedback, InterviewQuestion
from recruitment_app.models.application import Application, ApplicationStageHistory
from recruitment_app.serializers.interview_serializer import Interview<PERSON>erializer, InterviewFeedbackSerializer, InterviewQuestionSerializer
from recruitment_app.filters.recruitment_filters import <PERSON><PERSON><PERSON><PERSON>, InterviewFeedbackFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class InterviewViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for interviews.
    """
    queryset = Interview.objects.all()
    serializer_class = InterviewSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = InterviewFilter
    search_fields = ['application__candidate__first_name', 'application__candidate__last_name', 'application__job_posting__title', 'notes', 'instructions']
    ordering_fields = ['scheduled_date', 'scheduled_start_time', 'status', 'round', 'created_at']
    ordering = ['scheduled_date', 'scheduled_start_time']
    
    def perform_create(self, serializer):
        """
        Create a new interview and update the application status.
        """
        interview = serializer.save()
        
        # Update application status
        application = interview.application
        application.status = 'interview'
        application.current_stage = 'first_interview' if interview.round == 1 else f"{Interview.ROUND_CHOICES[interview.round-1][1].lower().replace(' ', '_')}"
        application.last_status_change = timezone.now()
        application.save()
        
        # Create stage history
        ApplicationStageHistory.objects.create(
            application=application,
            from_stage=application.current_stage,
            to_stage=application.current_stage,
            from_status=application.status,
            to_status='interview',
            changed_by=self.request.user.employee if hasattr(self.request.user, 'employee') else None,
            notes=f"Interview scheduled for {interview.scheduled_date} at {interview.scheduled_start_time}"
        )
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all interviews with a specific status.
        """
        interviews = Interview.objects.filter(status=status_value)
        page = self.paginate_queryset(interviews)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(interviews, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def application(self, request, application_pk=None):
        """
        Get all interviews for a specific application.
        """
        interviews = Interview.objects.filter(application_id=application_pk)
        page = self.paginate_queryset(interviews)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(interviews, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def interviewer(self, request, interviewer_pk=None):
        """
        Get all interviews for a specific interviewer.
        """
        interviews = Interview.objects.filter(interviewers__id=interviewer_pk)
        page = self.paginate_queryset(interviews)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(interviews, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """
        Get all upcoming interviews.
        """
        today = timezone.now().date()
        interviews = Interview.objects.filter(scheduled_date__gte=today, status='scheduled')
        page = self.paginate_queryset(interviews)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(interviews, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def feedback(self, request, pk=None):
        """
        Get all feedback for a specific interview.
        """
        interview = self.get_object()
        feedback = InterviewFeedback.objects.filter(interview=interview)
        
        serializer = InterviewFeedbackSerializer(feedback, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def questions(self, request, pk=None):
        """
        Get all questions for a specific interview.
        """
        interview = self.get_object()
        questions = InterviewQuestion.objects.filter(interview=interview)
        
        serializer = InterviewQuestionSerializer(questions, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def add_question(self, request, pk=None):
        """
        Add a question to an interview.
        """
        interview = self.get_object()
        
        # Validate required fields
        required_fields = ['question', 'question_type']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {"detail": f"Field '{field}' is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Create question
        question_data = {
            'interview': interview.id,
            'question': request.data.get('question'),
            'question_type': request.data.get('question_type'),
            'answer': request.data.get('answer'),
            'rating': request.data.get('rating'),
            'notes': request.data.get('notes')
        }
        
        serializer = InterviewQuestionSerializer(data=question_data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """
        Mark an interview as completed.
        """
        interview = self.get_object()
        
        if interview.status != 'scheduled':
            return Response(
                {"detail": "Only scheduled interviews can be marked as completed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update interview
        interview.status = 'completed'
        interview.actual_start_time = request.data.get('actual_start_time', timezone.now())
        interview.actual_end_time = request.data.get('actual_end_time', timezone.now())
        interview.save()
        
        serializer = self.get_serializer(interview)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel an interview.
        """
        interview = self.get_object()
        
        if interview.status not in ['scheduled', 'rescheduled']:
            return Response(
                {"detail": "Only scheduled or rescheduled interviews can be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update interview
        interview.status = 'cancelled'
        interview.cancellation_reason = request.data.get('cancellation_reason')
        interview.cancelled_by_id = request.data.get('cancelled_by')
        interview.save()
        
        serializer = self.get_serializer(interview)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reschedule(self, request, pk=None):
        """
        Reschedule an interview.
        """
        interview = self.get_object()
        
        if interview.status not in ['scheduled', 'rescheduled']:
            return Response(
                {"detail": "Only scheduled or rescheduled interviews can be rescheduled."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate required fields
        required_fields = ['scheduled_date', 'scheduled_start_time', 'scheduled_end_time']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {"detail": f"Field '{field}' is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Update interview
        interview.rescheduled_from = timezone.now()
        interview.rescheduled_reason = request.data.get('rescheduled_reason')
        interview.scheduled_date = request.data.get('scheduled_date')
        interview.scheduled_start_time = request.data.get('scheduled_start_time')
        interview.scheduled_end_time = request.data.get('scheduled_end_time')
        interview.status = 'rescheduled'
        interview.save()
        
        serializer = self.get_serializer(interview)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def no_show(self, request, pk=None):
        """
        Mark an interview as no show.
        """
        interview = self.get_object()
        
        if interview.status not in ['scheduled', 'rescheduled']:
            return Response(
                {"detail": "Only scheduled or rescheduled interviews can be marked as no show."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update interview
        interview.status = 'no_show'
        interview.save()
        
        serializer = self.get_serializer(interview)
        return Response(serializer.data)


class InterviewFeedbackViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for interview feedback.
    """
    queryset = InterviewFeedback.objects.all()
    serializer_class = InterviewFeedbackSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = InterviewFeedbackFilter
    search_fields = ['technical_skills', 'communication_skills', 'problem_solving', 'cultural_fit', 'strengths', 'weaknesses', 'comments']
    ordering_fields = ['overall_rating', 'recommendation', 'submitted_at']
    ordering = ['-submitted_at']
    
    def perform_create(self, serializer):
        """
        Create a new interview feedback.
        """
        serializer.save()
