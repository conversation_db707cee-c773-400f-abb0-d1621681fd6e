# Location Model

The Location model represents a branch or office of an organization.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the location | Yes |
| `organization` | ForeignKey | Organization this location belongs to | Yes |
| `address_line1` | String | First line of address | Yes |
| `address_line2` | String | Second line of address | No |
| `city` | String | City | Yes |
| `state` | String | State/Province | Yes |
| `country` | String | Country | Yes |
| `postal_code` | String | Postal/ZIP code | Yes |
| `phone` | String | Contact phone number | No |
| `email` | Email | Contact email | No |
| `latitude` | Decimal | Latitude coordinate for maps | No |
| `longitude` | Decimal | Longitude coordinate for maps | No |
| `working_hours_start` | Time | Start of working hours | No |
| `working_hours_end` | Time | End of working hours | No |
| `working_days` | String | Comma-separated list of working days | No |
| `is_headquarters` | Boolean | Whether this is the headquarters | Yes (default: False) |
| `status` | String | Status of the location (choices: active, inactive) | Yes (default: active) |
| `custom_fields` | JSON | Custom fields for flexibility | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this location belongs to |
| `business_units` | BusinessUnit | The business units at this location |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name of the location and organization |

## Example

```python
location = Location.objects.create(
    name="Headquarters",
    organization=acme_corp,
    address_line1="123 Main St",
    city="San Francisco",
    state="CA",
    country="USA",
    postal_code="94105",
    is_headquarters=True,
    status="active"
)
```
