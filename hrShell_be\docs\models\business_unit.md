# BusinessUnit Model

The BusinessUnit model represents a division or business unit in an organization.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the business unit | Yes |
| `organization` | ForeignKey | Organization this business unit belongs to | Yes |
| `description` | Text | Description of the business unit | No |
| `code` | String | Code for the business unit | No |
| `parent` | ForeignKey | Parent business unit | No |
| `head` | ForeignKey | Employee who heads this business unit | No |
| `primary_location` | ForeignKey | Primary location of this business unit | No |
| `status` | String | Status of the business unit (choices: active, inactive) | Yes (default: active) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this business unit belongs to |
| `parent` | BusinessUnit | The parent business unit |
| `children` | BusinessUnit | The child business units |
| `head` | Employee | The employee who heads this business unit |
| `primary_location` | Location | The primary location of this business unit |
| `departments` | Department | The departments in this business unit |
| `documents` | OrganizationDocument | The documents related to this business unit |

## Properties

| Property | Type | Description |
|----------|------|-------------|
| `department_count` | Integer | Number of departments in this business unit |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name of the business unit and organization |

## Example

```python
business_unit = BusinessUnit.objects.create(
    name="Product Development",
    organization=acme_corp,
    description="Responsible for product development",
    code="PD",
    head=john_doe,
    primary_location=headquarters,
    status="active"
)
```
