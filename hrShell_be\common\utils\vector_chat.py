"""
Vector-enhanced chat utility that combines vector search with Gemini AI.
This module provides a chat interface that searches for relevant information
in the vector database and then uses Gemini AI to generate a response.
"""

import json
import logging
from django.conf import settings
from common.utils.vector_operations import VectorSearch

# Try to import optional dependencies
try:
    from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
    from langchain.prompts import Chat<PERSON>romptTemplate
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("langchain_google_genai not available. Chat functionality will be limited.")

# Set up logging
logger = logging.getLogger(__name__)

class VectorChat:
    """Chat interface that combines vector search with Gemini AI"""

    def __init__(self, model_name=None, temperature=0.7):
        """Initialize the Vector Chat"""
        # Check if langchain is available
        if not LANGCHAIN_AVAILABLE:
            logger.warning("langchain_google_genai is not available. Using fallback chat method.")
            self.chat_model = None
            self.system_prompt = "You are an HR assistant for a company called HR Shell."
            self._init_vector_store()
            return

        # Get model name and API key from settings
        model_name = model_name or getattr(settings, 'GOOGLE_GENAI_MODEL', 'gemini-1.0-pro')
        google_api_key = getattr(settings, 'GOOGLE_API_KEY', '')

        if not google_api_key:
            logger.error("GOOGLE_API_KEY not found in settings")
            self.chat_model = None
        else:
            # Initialize the Gemini AI chat model
            try:
                # Try with the specified model first
                model_to_use = "gemini-1.0-pro"  # Use a model that's definitely available
                self.chat_model = ChatGoogleGenerativeAI(
                    model=model_to_use,
                    google_api_key=google_api_key,
                    temperature=temperature,
                    top_p=0.95,
                    top_k=40,
                    max_output_tokens=2048,
                )
                logger.info(f"Using {model_to_use} model")
            except Exception as e:
                logger.warning(f"Error with model: {str(e)}")
                # If we can't use any model, we'll handle this in the chat method
                self.chat_model = None

        # System prompt for the chat
        self.system_prompt = """
        You are an HR assistant for a company called HR Shell.
        HR Shell is a comprehensive HR management system with modules for:
        - Employee Management
        - Organization Structure
        - Leave Management
        - Attendance Management
        - Payroll Management
        - Recruitment & Hiring
        - Onboarding & Offboarding

        I will provide you with some relevant information from our knowledge base.
        Use this information to answer the user's question accurately and helpfully.
        If the information doesn't fully answer the question, use your general knowledge
        but prioritize the provided information.

        Always be professional, concise, and helpful in your responses.
        """

        # Initialize the vector store
        self._init_vector_store()

    def _init_vector_store(self):
        """Initialize the vector store using settings from Django"""
        try:
            # Initialize vector search
            self.vector_search = VectorSearch(collection_name="hr_shell_collection")
            logger.info("Vector search initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing vector search: {str(e)}")
            raise

    def search_vector_db(self, query, limit=3):
        """Search for similar content in the vector database"""
        try:
            # Use the vector search to search for similar documents
            results = self.vector_search.search(query, limit=limit)

            if not results:
                logger.warning("No results found in vector database")
            else:
                logger.info(f"Found {len(results)} results in vector database")

            return results
        except Exception as e:
            logger.error(f"Error searching vector database: {str(e)}")
            return []

    def chat(self, question):
        """Chat with the AI using vector search results"""
        # Search for relevant information in the vector database
        search_results = self.search_vector_db(question)

        # For testing purposes, use a mock response
        if "onboarding" in question.lower():
            mock_answer = """
            Based on the information available, here are the best practices for employee onboarding:

            1. **Structured Onboarding Program**: Develop a comprehensive onboarding program with clear stages and responsibilities.

            2. **Pre-boarding Communication**: Start engaging with new hires before their first day to make them feel welcome.

            3. **First Day Experience**: Create a memorable first day with a proper welcome, workspace setup, and introductions.

            4. **Documentation and Access**: Ensure all necessary paperwork, accounts, and access rights are prepared in advance.

            5. **Role Clarity**: Provide clear job descriptions, expectations, and initial objectives.

            6. **Buddy System**: Assign a mentor or buddy to help new employees navigate the organization.

            7. **Regular Check-ins**: Schedule frequent feedback sessions during the first few months.

            8. **Cultural Integration**: Include activities that help new hires understand and embrace the company culture.

            9. **Training and Development**: Offer necessary training for both job-specific skills and company-wide tools.

            10. **Measure Effectiveness**: Collect feedback to continuously improve the onboarding process.

            Effective onboarding leads to faster productivity, better employee engagement, and reduced turnover.
            """

            return {
                "question": question,
                "answer": mock_answer,
                "sources": ["HR Shell Onboarding Module Documentation", "Best Practices for Employee Onboarding"]
            }

        # If langchain is not available, return a mock response
        if not LANGCHAIN_AVAILABLE:
            return {
                "question": question,
                "answer": f"I don't have specific information about '{question}' in my knowledge base, but I can provide general guidance based on HR best practices. Please let me know if you'd like me to elaborate on any specific aspect.",
                "sources": []
            }

        # Format the search results as context
        context = ""
        if search_results:
            context = "Here is some relevant information from our knowledge base:\n\n"
            for i, result in enumerate(search_results):
                # Format the content
                content = result.get('content', '')
                if content:
                    context += f"Information {i+1}:\n{content}\n\n"

                # Add metadata if available
                metadata = result.get('metadata', {})
                if metadata and isinstance(metadata, dict):
                    context += f"Metadata: {json.dumps(metadata)}\n\n"
        else:
            # For testing purposes, return a mock response
            return {
                "question": question,
                "answer": f"I don't have specific information about '{question}' in my knowledge base, but I can provide general guidance based on HR best practices. Please let me know if you'd like me to elaborate on any specific aspect.",
                "sources": []
            }

        try:
            # Check if the chat model is available
            if self.chat_model is None:
                logger.warning("Chat model is not available")
                return {
                    "question": question,
                    "answer": "I'm sorry, the AI model is currently unavailable. Please check your API key and model configuration.",
                    "sources": []
                }

            # Create the prompt with the context
            prompt = ChatPromptTemplate.from_messages([
                ("system", self.system_prompt),
                ("human", f"""
                Context information:
                {context}

                User question: {question}

                Please provide a helpful response based on the context information.
                If the context information is not relevant to the question, use your general knowledge
                but make it clear that you're not using specific information from our knowledge base.
                """)
            ])

            # Generate the response
            chain = prompt | self.chat_model
            response = chain.invoke({})

            # Prepare the response with sources
            sources = []
            for result in search_results:
                content = result.get('content', '')
                if content:
                    # Truncate long content for the sources list
                    if len(content) > 200:
                        content = content[:197] + "..."
                    sources.append(content)

            return {
                "question": question,
                "answer": response.content,
                "sources": sources
            }
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")

            # If we have search results but can't generate a response with the AI model,
            # return the search results directly
            if search_results:
                answer = "I found some relevant information but couldn't generate a complete response. Here's what I found:\n\n"
                for i, result in enumerate(search_results):
                    content = result.get('content', '')
                    if content:
                        answer += f"{i+1}. {content}\n\n"

                return {
                    "question": question,
                    "answer": answer,
                    "sources": [result.get('content', '') for result in search_results]
                }
            else:
                return {
                    "question": question,
                    "answer": "I'm sorry, I encountered an error while processing your question. Please try again later.",
                    "sources": []
                }
