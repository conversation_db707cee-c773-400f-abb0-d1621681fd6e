# Tax Models

## TaxSlab

The TaxSlab model defines tax slabs for income tax calculation.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `organization` | ForeignKey | Organization this tax slab belongs to | Yes |
| `financial_year` | String | Financial year (e.g., 2023-24) | Yes |
| `name` | String | Name of the tax slab | Yes |
| `min_income` | Decimal | Minimum income for this slab | Yes |
| `max_income` | Decimal | Maximum income for this slab | No |
| `tax_rate` | Decimal | Tax rate (%) | Yes |
| `surcharge_rate` | Decimal | Surcharge rate (%) | Yes (default: 0) |
| `cess_rate` | Decimal | Cess rate (%) | Yes (default: 0) |
| `gender` | String | Gender this slab applies to | Yes (default: all) |
| `age_group` | String | Age group this slab applies to | Yes (default: all) |
| `is_active` | Boolean | Whether this slab is active | Yes (default: True) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Gender Choices
- `all`: All
- `male`: Male
- `female`: Female
- `other`: Other

#### Age Group Choices
- `all`: All Ages
- `below_60`: Below 60 Years
- `60_to_80`: 60 to 80 Years
- `above_80`: Above 80 Years

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this tax slab belongs to |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the financial year, income range, and tax rate |

### Example

```python
tax_slab = TaxSlab.objects.create(
    organization=acme_corp,
    financial_year="2023-24",
    name="Slab 1",
    min_income=0.00,
    max_income=250000.00,
    tax_rate=0.00,
    surcharge_rate=0.00,
    cess_rate=0.00,
    gender="all",
    age_group="all",
    is_active=True
)

tax_slab2 = TaxSlab.objects.create(
    organization=acme_corp,
    financial_year="2023-24",
    name="Slab 2",
    min_income=250000.01,
    max_income=500000.00,
    tax_rate=5.00,
    surcharge_rate=0.00,
    cess_rate=4.00,
    gender="all",
    age_group="all",
    is_active=True
)
```

## EmployeeTaxDeclaration

The EmployeeTaxDeclaration model stores employee tax declarations and exemptions.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `employee` | ForeignKey | Employee this declaration belongs to | Yes |
| `financial_year` | String | Financial year (e.g., 2023-24) | Yes |
| `declaration_type` | String | Type of declaration | Yes |
| `declaration_name` | String | Name of the declaration | Yes |
| `declared_amount` | Decimal | Declared amount | Yes |
| `proof_document` | FileField | Proof document | No |
| `verified_amount` | Decimal | Verified amount | No |
| `status` | String | Status of the declaration | Yes (default: declared) |
| `remarks` | Text | Any remarks about this declaration | No |
| `verified_by` | ForeignKey | Employee who verified the declaration | No |
| `verification_date` | Date | Date of verification | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Declaration Type Choices
- `section_80c`: Section 80C (e.g., PF, LIC, ELSS)
- `section_80d`: Section 80D (e.g., Medical Insurance)
- `section_80e`: Section 80E (e.g., Education Loan Interest)
- `section_80g`: Section 80G (e.g., Donations)
- `section_24`: Section 24 (e.g., Home Loan Interest)
- `hra`: HRA Exemption
- `lta`: LTA Exemption
- `other`: Other Exemption

#### Status Choices
- `declared`: Declared
- `submitted`: Proof Submitted
- `verified`: Verified
- `rejected`: Rejected

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `employee` | Employee | The employee this declaration belongs to |
| `verified_by` | Employee | The employee who verified the declaration |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, declaration type, and declared amount |

### Example

```python
tax_declaration = EmployeeTaxDeclaration.objects.create(
    employee=john_doe,
    financial_year="2023-24",
    declaration_type="section_80c",
    declaration_name="PF Contribution",
    declared_amount=150000.00,
    status="declared"
)

tax_declaration2 = EmployeeTaxDeclaration.objects.create(
    employee=john_doe,
    financial_year="2023-24",
    declaration_type="section_80d",
    declaration_name="Medical Insurance Premium",
    declared_amount=25000.00,
    status="declared"
)
```

## API Endpoints

### TaxSlab Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/tax-slabs/` | GET | List all tax slabs |
| `/api/v1/tax-slabs/` | POST | Create a new tax slab |
| `/api/v1/tax-slabs/{id}/` | GET | Retrieve a specific tax slab |
| `/api/v1/tax-slabs/{id}/` | PUT | Update a specific tax slab |
| `/api/v1/tax-slabs/{id}/` | DELETE | Delete a specific tax slab |
| `/api/v1/tax-slabs/active/` | GET | List all active tax slabs |
| `/api/v1/tax-slabs/organization/{organization_id}/` | GET | List all tax slabs for a specific organization |
| `/api/v1/tax-slabs/financial-year/` | GET | List all tax slabs for a specific financial year |
| `/api/v1/tax-slabs/gender/{gender}/` | GET | List all tax slabs for a specific gender |
| `/api/v1/tax-slabs/age-group/{age_group}/` | GET | List all tax slabs for a specific age group |

### EmployeeTaxDeclaration Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/tax-declarations/` | GET | List all tax declarations |
| `/api/v1/tax-declarations/` | POST | Create a new tax declaration |
| `/api/v1/tax-declarations/{id}/` | GET | Retrieve a specific tax declaration |
| `/api/v1/tax-declarations/{id}/` | PUT | Update a specific tax declaration |
| `/api/v1/tax-declarations/{id}/` | DELETE | Delete a specific tax declaration |
| `/api/v1/tax-declarations/status/{status}/` | GET | List all tax declarations with a specific status |
| `/api/v1/tax-declarations/employee/{employee_id}/` | GET | List all tax declarations for a specific employee |
| `/api/v1/tax-declarations/financial-year/` | GET | List all tax declarations for a specific financial year |
| `/api/v1/tax-declarations/declaration-type/{declaration_type}/` | GET | List all tax declarations of a specific type |
| `/api/v1/tax-declarations/{id}/verify/` | POST | Verify a specific tax declaration |
| `/api/v1/tax-declarations/{id}/reject/` | POST | Reject a specific tax declaration |

## Tax Calculation Process

1. **Tax Declaration**: Employees declare their investments and exemptions
2. **Proof Submission**: Employees submit proofs for their declarations
3. **Verification**: HR/Finance verifies the declarations and proofs
4. **Tax Calculation**: System calculates tax based on verified declarations and applicable tax slabs
5. **Tax Deduction**: Tax is deducted from employee's salary each month

## Tax Calculation Formula

The tax calculation formula is as follows:

```
Gross Income = Basic Salary + Allowances + Bonuses + Other Earnings
Taxable Income = Gross Income - Exemptions - Deductions
Tax = Calculate tax on Taxable Income as per applicable tax slabs
Surcharge = Tax * Surcharge Rate (if applicable)
Cess = (Tax + Surcharge) * Cess Rate
Total Tax = Tax + Surcharge + Cess
```

## Tax Exemptions and Deductions

### Section 80C
- Maximum limit: ₹1,50,000
- Includes: PF, LIC, ELSS, PPF, NSC, etc.

### Section 80D
- Maximum limit: ₹25,000 (self and family), ₹50,000 (senior citizens)
- Includes: Medical Insurance Premium

### Section 80E
- No maximum limit
- Includes: Education Loan Interest

### Section 80G
- Varies based on the type of donation
- Includes: Donations to charitable organizations

### Section 24
- Maximum limit: ₹2,00,000
- Includes: Home Loan Interest

### HRA Exemption
- Minimum of:
  - Actual HRA received
  - 50% of (Basic Salary + DA) for metro cities, 40% for non-metro cities
  - Actual rent paid - 10% of (Basic Salary + DA)

### LTA Exemption
- Actual amount spent on travel within India
- Limited to 2 journeys in a block of 4 years
