from rest_framework import serializers
from onboarding_offboarding_app.models.document import Document, DocumentTemplate
from employees_app.serializers.employee_serializer import EmployeeSerializer
from organization_app.serializers.organization_serializer import OrganizationSerializer


class DocumentTemplateSerializer(serializers.ModelSerializer):
    """
    Serializer for the DocumentTemplate model.
    """
    organization_details = OrganizationSerializer(source='organization', read_only=True)
    
    class Meta:
        model = DocumentTemplate
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']


class DocumentSerializer(serializers.ModelSerializer):
    """
    Serializer for the Document model.
    """
    employee_details = EmployeeSerializer(source='employee', read_only=True)
    template_details = DocumentTemplateSerializer(source='template', read_only=True)
    verified_by_details = EmployeeSerializer(source='verified_by', read_only=True)
    
    class Meta:
        model = Document
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate the document data.
        """
        # Validate document file is provided if status is submitted
        document_file = data.get('document_file')
        status = data.get('status')
        
        if status == 'submitted' and not document_file and not self.instance.document_file:
            raise serializers.ValidationError("Document file is required when status is submitted.")
        
        return data
