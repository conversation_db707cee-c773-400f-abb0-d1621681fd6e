from django.db import models
from django.core.validators import MinValueValidator
from employees_app.models.employee import Employee
from payroll_app.models.payroll import PayrollItem


class Payslip(models.Model):
    """
    Model to represent a payslip generated for an employee.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('generated', 'Generated'),
        ('emailed', 'Emailed'),
        ('printed', 'Printed'),
    ]
    
    payroll_item = models.OneToOneField(PayrollItem, on_delete=models.CASCADE, related_name='payslip')
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='payslips')
    payslip_number = models.CharField(max_length=50, unique=True)
    month = models.PositiveIntegerField(validators=[MinValueValidator(1), MinValueValidator(12)])
    year = models.PositiveIntegerField()
    generation_date = models.DateField()
    basic_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    gross_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_deductions = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    net_payable = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    pdf_file = models.FileField(upload_to='payslips/%Y/%m/', null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    email_sent = models.BooleanField(default=False)
    email_sent_at = models.DateTimeField(null=True, blank=True)
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-year', '-month', 'employee__first_name', 'employee__last_name']
        verbose_name = 'Payslip'
        verbose_name_plural = 'Payslips'
        unique_together = ['employee', 'month', 'year']
    
    def __str__(self):
        return f"{self.employee} - {self.month}/{self.year} ({self.payslip_number})"
