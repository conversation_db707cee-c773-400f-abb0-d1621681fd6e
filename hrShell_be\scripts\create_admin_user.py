#!/usr/bin/env python3
"""
Script to create an admin user for testing purposes.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hrShell_be.settings')
django.setup()

from django.contrib.auth.models import User

def create_admin_user():
    """Create an admin user for testing."""
    username = 'admin'
    email = '<EMAIL>'
    password = 'admin123'
    
    # Check if user already exists
    if User.objects.filter(username=username).exists():
        print(f"✅ Admin user '{username}' already exists")
        user = User.objects.get(username=username)
    else:
        # Create the admin user
        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password
        )
        print(f"✅ Created admin user '{username}' with password '{password}'")
    
    print(f"\n📋 Admin User Details:")
    print(f"  Username: {user.username}")
    print(f"  Email: {user.email}")
    print(f"  Is Staff: {user.is_staff}")
    print(f"  Is Superuser: {user.is_superuser}")
    
    print(f"\n🔑 Login Credentials:")
    print(f"  Username: {username}")
    print(f"  Password: {password}")
    
    print(f"\n🌐 Login URL:")
    print(f"  POST http://127.0.0.1:8000/api/auth/login/")
    print(f"  Body: {{'username': '{username}', 'password': '{password}'}}")

if __name__ == "__main__":
    create_admin_user()
