from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
import csv
import io
from payroll_app.models.bank_transfer import BankTransfer
from payroll_app.models.payroll import Payroll, PayrollItem
from payroll_app.serializers.bank_transfer_serializer import BankTransferSerializer
from payroll_app.filters.payroll_filters import BankTransferFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class BankTransferViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Payroll Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for bank transfers.
    """
    queryset = BankTransfer.objects.all()
    serializer_class = BankTransferSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = BankTransferFilter
    search_fields = ['reference_number', 'bank_name', 'account_number']
    ordering_fields = ['transfer_date', 'total_amount', 'status', 'created_at']
    ordering = ['-transfer_date']
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all bank transfers with a specific status.
        """
        transfers = BankTransfer.objects.filter(status=status_value)
        page = self.paginate_queryset(transfers)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(transfers, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all bank transfers for a specific organization.
        """
        transfers = BankTransfer.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(transfers)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(transfers, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def payroll(self, request, payroll_pk=None):
        """
        Get all bank transfers for a specific payroll.
        """
        transfers = BankTransfer.objects.filter(payroll_id=payroll_pk)
        page = self.paginate_queryset(transfers)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(transfers, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def generate_file(self, request, pk=None):
        """
        Generate a bank transfer file for a specific bank transfer.
        """
        bank_transfer = self.get_object()
        
        if bank_transfer.status != 'draft':
            return Response(
                {"detail": "Bank transfer file can only be generated for draft transfers."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the payroll
        payroll = bank_transfer.payroll
        if not payroll:
            return Response(
                {"detail": "No payroll associated with this bank transfer."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get all approved payroll items
        payroll_items = PayrollItem.objects.filter(
            payroll=payroll,
            status='approved'
        )
        
        if not payroll_items:
            return Response(
                {"detail": "No approved payroll items found for this payroll."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Generate CSV file
        csv_file = self._generate_csv(payroll_items)
        
        # Update bank transfer
        bank_transfer.status = 'generated'
        bank_transfer.total_employees = payroll_items.count()
        bank_transfer.total_amount = sum(item.net_payable for item in payroll_items)
        bank_transfer.save()
        
        # In a real implementation, you would save the CSV file to a FileField
        # For now, we'll just return a success message
        
        serializer = self.get_serializer(bank_transfer)
        return Response({
            "detail": "Bank transfer file generated successfully.",
            "bank_transfer": serializer.data
        })
    
    @action(detail=True, methods=['post'])
    def mark_sent(self, request, pk=None):
        """
        Mark a bank transfer as sent to the bank.
        """
        bank_transfer = self.get_object()
        
        if bank_transfer.status != 'generated':
            return Response(
                {"detail": "Only generated bank transfers can be marked as sent."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update bank transfer
        bank_transfer.status = 'sent'
        bank_transfer.save()
        
        serializer = self.get_serializer(bank_transfer)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_processed(self, request, pk=None):
        """
        Mark a bank transfer as processed by the bank.
        """
        bank_transfer = self.get_object()
        
        if bank_transfer.status != 'sent':
            return Response(
                {"detail": "Only sent bank transfers can be marked as processed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update bank transfer
        bank_transfer.status = 'processed'
        bank_transfer.save()
        
        serializer = self.get_serializer(bank_transfer)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_completed(self, request, pk=None):
        """
        Mark a bank transfer as completed.
        """
        bank_transfer = self.get_object()
        
        if bank_transfer.status != 'processed':
            return Response(
                {"detail": "Only processed bank transfers can be marked as completed."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update bank transfer
        bank_transfer.status = 'completed'
        bank_transfer.save()
        
        # Update payroll items
        if bank_transfer.payroll:
            PayrollItem.objects.filter(
                payroll=bank_transfer.payroll,
                status='approved'
            ).update(status='paid')
        
        serializer = self.get_serializer(bank_transfer)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_failed(self, request, pk=None):
        """
        Mark a bank transfer as failed.
        """
        bank_transfer = self.get_object()
        
        if bank_transfer.status in ['completed', 'failed']:
            return Response(
                {"detail": "Completed or failed bank transfers cannot be marked as failed again."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update bank transfer
        bank_transfer.status = 'failed'
        
        # Set the remarks if provided
        remarks = request.data.get('remarks')
        if remarks:
            bank_transfer.remarks = remarks
        
        bank_transfer.save()
        
        serializer = self.get_serializer(bank_transfer)
        return Response(serializer.data)
    
    def _generate_csv(self, payroll_items):
        """
        Generate a CSV file for bank transfer.
        """
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Employee ID', 'Employee Name', 'Account Number', 'Bank Name',
            'IFSC Code', 'Amount', 'Reference Number'
        ])
        
        # Write data
        for item in payroll_items:
            employee = item.employee
            
            # In a real implementation, you would get these details from the employee's bank details
            account_number = '**********'  # Placeholder
            bank_name = 'Sample Bank'  # Placeholder
            ifsc_code = 'SBIN0001234'  # Placeholder
            
            writer.writerow([
                employee.id,
                f"{employee.first_name} {employee.last_name}",
                account_number,
                bank_name,
                ifsc_code,
                item.net_payable,
                f"SAL-{item.payroll.payment_date.strftime('%Y%m')}-{employee.id}"
            ])
        
        return output.getvalue()
