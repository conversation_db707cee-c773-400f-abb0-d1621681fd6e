from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from payroll_app.models.payroll import Payroll, PayrollItem, PayrollItemComponent
from payroll_app.models.employee_salary import EmployeeSalary
from payroll_app.serializers.payroll_serializer import PayrollSerializer, PayrollItemSerializer, PayrollDetailSerializer
from payroll_app.filters.payroll_filters import PayrollFilter, PayrollItemFilter
from employees_app.models.employee import Employee
from leave_app.models.leave_request import LeaveRequest


class PayrollViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Payroll Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for payrolls.
    """
    queryset = Payroll.objects.all()
    serializer_class = PayrollSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = PayrollFilter
    search_fields = ['name']
    ordering_fields = ['name', 'start_date', 'end_date', 'payment_date', 'status', 'created_at']
    ordering = ['-payment_date']

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return PayrollDetailSerializer
        return PayrollSerializer

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all payrolls with a specific status.
        """
        payrolls = Payroll.objects.filter(status=status_value)
        page = self.paginate_queryset(payrolls)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(payrolls, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all payrolls for a specific organization.
        """
        payrolls = Payroll.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(payrolls)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(payrolls, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        """
        Get all payroll items for a specific payroll.
        """
        payroll = self.get_object()
        items = payroll.items.all()
        page = self.paginate_queryset(items)
        if page is not None:
            serializer = PayrollItemSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = PayrollItemSerializer(items, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def generate_items(self, request, pk=None):
        """
        Generate payroll items for all active employees.
        """
        payroll = self.get_object()

        if payroll.status != 'draft':
            return Response(
                {"detail": "Payroll items can only be generated for draft payrolls."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get all active employees
        employees = Employee.objects.filter(status='active')
        items_created = 0

        for employee in employees:
            # Check if a payroll item already exists for this employee
            if PayrollItem.objects.filter(payroll=payroll, employee=employee).exists():
                continue

            # Get the employee's active salary
            try:
                salary = EmployeeSalary.objects.filter(
                    employee=employee,
                    is_active=True
                ).latest('effective_from')
            except EmployeeSalary.DoesNotExist:
                continue

            # Calculate working days, leave days, etc.
            working_days, leave_days, lop_days = self._calculate_attendance(
                employee, payroll.start_date, payroll.end_date
            )

            # Create the payroll item
            payroll_item = PayrollItem.objects.create(
                payroll=payroll,
                employee=employee,
                working_days=working_days,
                leave_days=leave_days,
                lop_days=lop_days,
                basic_salary=salary.basic_salary,
                gross_earnings=salary.gross_salary,
                net_payable=salary.net_salary,
                status='draft'
            )

            # Create the payroll item components
            self._create_payroll_item_components(payroll_item, salary)

            items_created += 1

        # Update payroll totals
        self._update_payroll_totals(payroll)

        return Response({
            "detail": f"Generated {items_created} payroll items.",
            "items_created": items_created
        })

    @action(detail=True, methods=['post'])
    def calculate(self, request, pk=None):
        """
        Calculate all payroll items for this payroll.
        """
        payroll = self.get_object()

        if payroll.status not in ['draft', 'processing']:
            return Response(
                {"detail": "Payroll can only be calculated when in draft or processing status."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update payroll status
        payroll.status = 'processing'
        payroll.save()

        # Calculate each payroll item
        items = payroll.items.all()
        for item in items:
            self._calculate_payroll_item(item)

        # Update payroll totals
        self._update_payroll_totals(payroll)

        return Response({
            "detail": f"Calculated {items.count()} payroll items.",
            "items_calculated": items.count()
        })

    @action(detail=True, methods=['post'])
    def finalize(self, request, pk=None):
        """
        Finalize the payroll and mark it as completed.
        """
        payroll = self.get_object()

        if payroll.status != 'processing':
            return Response(
                {"detail": "Payroll can only be finalized when in processing status."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update payroll status
        payroll.status = 'completed'
        payroll.save()

        # Update all payroll items to approved status
        payroll.items.all().update(status='approved')

        return Response({
            "detail": "Payroll finalized successfully."
        })

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """
        Cancel the payroll.
        """
        payroll = self.get_object()

        if payroll.status == 'completed':
            return Response(
                {"detail": "Completed payrolls cannot be cancelled."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update payroll status
        payroll.status = 'cancelled'
        payroll.save()

        # Update all payroll items to cancelled status
        payroll.items.all().update(status='cancelled')

        return Response({
            "detail": "Payroll cancelled successfully."
        })

    def _calculate_attendance(self, employee, start_date, end_date):
        """
        Calculate working days, leave days, and LOP days for an employee.
        """
        # This is a simplified calculation
        # In a real implementation, you would integrate with attendance and leave modules

        # Calculate total days in the period
        total_days = (end_date - start_date).days + 1

        # Get approved leaves in the period
        leaves = LeaveRequest.objects.filter(
            employee=employee,
            status='approved',
            start_date__lte=end_date,
            end_date__gte=start_date
        )

        leave_days = sum(leave.total_days for leave in leaves)

        # Get LOP (Leave Without Pay) days
        lop_days = 0
        for leave in leaves:
            if not leave.leave_type.is_paid:
                lop_days += leave.total_days

        # Calculate working days
        working_days = total_days - leave_days

        return working_days, leave_days, lop_days

    def _create_payroll_item_components(self, payroll_item, salary):
        """
        Create components for a payroll item based on the employee's salary.
        """
        # Get all salary components
        salary_components = salary.components.all()

        for salary_component in salary_components:
            component = salary_component.salary_component

            PayrollItemComponent.objects.create(
                payroll_item=payroll_item,
                name=component.name,
                component_type=component.component_type,
                amount=salary_component.value,
                is_taxable=component.is_taxable
            )

    def _calculate_payroll_item(self, payroll_item):
        """
        Calculate a payroll item's earnings, deductions, and net payable.
        """
        # Calculate gross earnings
        earnings = payroll_item.components.filter(component_type='earning')
        gross_earnings = sum(earning.amount for earning in earnings)

        # Calculate total deductions
        deductions = payroll_item.components.filter(component_type='deduction')
        total_deductions = sum(deduction.amount for deduction in deductions)

        # Calculate net payable
        net_payable = gross_earnings - total_deductions

        # Update the payroll item
        payroll_item.gross_earnings = gross_earnings
        payroll_item.total_deductions = total_deductions
        payroll_item.net_payable = net_payable
        payroll_item.status = 'calculated'
        payroll_item.save()

    def _update_payroll_totals(self, payroll):
        """
        Update the payroll's total earnings, deductions, and net payable.
        """
        totals = payroll.items.aggregate(
            total_earnings=Sum('gross_earnings'),
            total_deductions=Sum('total_deductions'),
            net_payable=Sum('net_payable')
        )

        payroll.total_earnings = totals['total_earnings'] or 0
        payroll.total_deductions = totals['total_deductions'] or 0
        payroll.net_payable = totals['net_payable'] or 0
        payroll.save()


class PayrollItemViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Payroll Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Payroll Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for payroll items.
    """
    queryset = PayrollItem.objects.all()
    serializer_class = PayrollItemSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = PayrollItemFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email']
    ordering_fields = ['employee__first_name', 'gross_earnings', 'net_payable', 'status', 'created_at']
    ordering = ['employee__first_name', 'employee__last_name']

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all payroll items with a specific status.
        """
        items = PayrollItem.objects.filter(status=status_value)
        page = self.paginate_queryset(items)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(items, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all payroll items for a specific employee.
        """
        items = PayrollItem.objects.filter(employee_id=employee_pk)
        page = self.paginate_queryset(items)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(items, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def components(self, request, pk=None):
        """
        Get all components for a specific payroll item.
        """
        item = self.get_object()
        components = item.components.all()
        serializer = PayrollItemComponentSerializer(components, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def calculate(self, request, pk=None):
        """
        Calculate a specific payroll item.
        """
        item = self.get_object()

        if item.payroll.status not in ['draft', 'processing']:
            return Response(
                {"detail": "Payroll item can only be calculated when payroll is in draft or processing status."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Calculate earnings
        earnings = item.components.filter(component_type='earning')
        gross_earnings = sum(earning.amount for earning in earnings)

        # Calculate deductions
        deductions = item.components.filter(component_type='deduction')
        total_deductions = sum(deduction.amount for deduction in deductions)

        # Calculate net payable
        net_payable = gross_earnings - total_deductions

        # Update the payroll item
        item.gross_earnings = gross_earnings
        item.total_deductions = total_deductions
        item.net_payable = net_payable
        item.status = 'calculated'
        item.save()

        # Update the payroll totals
        self._update_payroll_totals(item.payroll)

        serializer = self.get_serializer(item)
        return Response(serializer.data)

    def _update_payroll_totals(self, payroll):
        """
        Update the payroll's total earnings, deductions, and net payable.
        """
        totals = payroll.items.aggregate(
            total_earnings=Sum('gross_earnings'),
            total_deductions=Sum('total_deductions'),
            net_payable=Sum('net_payable')
        )

        payroll.total_earnings = totals['total_earnings'] or 0
        payroll.total_deductions = totals['total_deductions'] or 0
        payroll.net_payable = totals['net_payable'] or 0
        payroll.save()
