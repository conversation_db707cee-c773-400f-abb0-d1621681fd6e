from rest_framework import serializers
from recruitment_app.models.candidate import Candidate, CandidateSkill, CandidateEducation, CandidateExperience
from recruitment_app.serializers.skill_serializer import SkillSerializer
from recruitment_app.serializers.recruitment_source_serializer import RecruitmentSourceSerializer
from organization_app.serializers.organization_serializer import OrganizationSerializer


class CandidateSkillSerializer(serializers.ModelSerializer):
    """
    Serializer for the CandidateSkill model.
    """
    skill_details = SkillSerializer(source='skill', read_only=True)
    
    class Meta:
        model = CandidateSkill
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']


class CandidateEducationSerializer(serializers.ModelSerializer):
    """
    Serializer for the CandidateEducation model.
    """
    class Meta:
        model = CandidateEducation
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']


class CandidateExperienceSerializer(serializers.ModelSerializer):
    """
    Serializer for the CandidateExperience model.
    """
    class Meta:
        model = CandidateExperience
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']


class CandidateSerializer(serializers.ModelSerializer):
    """
    Serializer for the Candidate model.
    """
    organization_details = OrganizationSerializer(source='organization', read_only=True)
    source_details = RecruitmentSourceSerializer(source='source', read_only=True)
    skills = serializers.SerializerMethodField()
    education = CandidateEducationSerializer(many=True, read_only=True)
    experience = CandidateExperienceSerializer(many=True, read_only=True)
    
    class Meta:
        model = Candidate
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def get_skills(self, obj):
        """
        Get the candidate's skills.
        """
        candidate_skills = CandidateSkill.objects.filter(candidate=obj)
        return CandidateSkillSerializer(candidate_skills, many=True).data
    
    def create(self, validated_data):
        """
        Create a new candidate with skills, education, and experience.
        """
        skills_data = self.context.get('request').data.get('skills', [])
        education_data = self.context.get('request').data.get('education', [])
        experience_data = self.context.get('request').data.get('experience', [])
        
        candidate = Candidate.objects.create(**validated_data)
        
        # Create skills
        for skill_data in skills_data:
            CandidateSkill.objects.create(candidate=candidate, **skill_data)
        
        # Create education
        for edu_data in education_data:
            CandidateEducation.objects.create(candidate=candidate, **edu_data)
        
        # Create experience
        for exp_data in experience_data:
            CandidateExperience.objects.create(candidate=candidate, **exp_data)
        
        return candidate
    
    def update(self, instance, validated_data):
        """
        Update a candidate with skills, education, and experience.
        """
        skills_data = self.context.get('request').data.get('skills', [])
        education_data = self.context.get('request').data.get('education', [])
        experience_data = self.context.get('request').data.get('experience', [])
        
        # Update candidate
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update skills
        if skills_data:
            # Delete existing skills
            CandidateSkill.objects.filter(candidate=instance).delete()
            
            # Create new skills
            for skill_data in skills_data:
                CandidateSkill.objects.create(candidate=instance, **skill_data)
        
        # Update education
        if education_data:
            # Delete existing education
            CandidateEducation.objects.filter(candidate=instance).delete()
            
            # Create new education
            for edu_data in education_data:
                CandidateEducation.objects.create(candidate=instance, **edu_data)
        
        # Update experience
        if experience_data:
            # Delete existing experience
            CandidateExperience.objects.filter(candidate=instance).delete()
            
            # Create new experience
            for exp_data in experience_data:
                CandidateExperience.objects.create(candidate=instance, **exp_data)
        
        return instance
