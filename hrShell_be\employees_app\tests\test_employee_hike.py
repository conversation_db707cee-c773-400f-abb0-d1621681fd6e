from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth.models import User
from employees_app.models.employee import Employee
from employees_app.models.department import Department
from employees_app.models.employee_hike import EmployeeHike
from decimal import Decimal


class EmployeeHikeModelTest(TestCase):
    def setUp(self):
        # Create a department
        self.department = Department.objects.create(
            name="Engineering",
            description="Software development department"
        )
        
        # Create employees
        self.employee = Employee.objects.create(
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            email="<EMAIL>",
            position="Software Engineer",
            department=self.department
        )
        
        self.manager = Employee.objects.create(
            first_name="<PERSON>",
            last_name="<PERSON>",
            email="<EMAIL>",
            position="Engineering Manager",
            department=self.department
        )
        
        # Create an employee hike
        self.hike = EmployeeHike.objects.create(
            employee=self.employee,
            hike_percentage=Decimal('10.00'),
            hike_reason="Annual performance review",
            department=self.department,
            previous_salary=Decimal('50000.00'),
            new_salary=Decimal('55000.00'),
            approved_by=self.manager
        )
    
    def test_employee_hike_creation(self):
        """Test that an employee hike can be created"""
        self.assertEqual(EmployeeHike.objects.count(), 1)
        self.assertEqual(self.hike.employee, self.employee)
        self.assertEqual(self.hike.hike_percentage, Decimal('10.00'))
        self.assertEqual(self.hike.previous_salary, Decimal('50000.00'))
        self.assertEqual(self.hike.new_salary, Decimal('55000.00'))
        self.assertEqual(self.hike.approved_by, self.manager)
    
    def test_employee_hike_string_representation(self):
        """Test the string representation of an employee hike"""
        expected_string = f"{self.employee} - 10.00% hike on {self.hike.hike_effective_date}"
        self.assertEqual(str(self.hike), expected_string)


class EmployeeHikeAPITest(APITestCase):
    def setUp(self):
        # Create a user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword',
            is_staff=True
        )
        
        # Create a department
        self.department = Department.objects.create(
            name="Engineering",
            description="Software development department"
        )
        
        # Create employees
        self.employee = Employee.objects.create(
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            position="Software Engineer",
            department=self.department
        )
        
        self.manager = Employee.objects.create(
            first_name="Jane",
            last_name="Smith",
            email="<EMAIL>",
            position="Engineering Manager",
            department=self.department
        )
        
        # Create an employee hike
        self.hike = EmployeeHike.objects.create(
            employee=self.employee,
            hike_percentage=Decimal('10.00'),
            hike_reason="Annual performance review",
            department=self.department,
            previous_salary=Decimal('50000.00'),
            new_salary=Decimal('55000.00'),
            approved_by=self.manager
        )
        
        # URLs
        self.list_url = reverse('employees:employeehike-list')
        self.detail_url = reverse('employees:employeehike-detail', args=[self.hike.id])
        
        # Authenticate
        self.client.force_authenticate(user=self.user)
    
    def test_list_employee_hikes(self):
        """Test that we can list employee hikes"""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_retrieve_employee_hike(self):
        """Test that we can retrieve an employee hike"""
        response = self.client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['employee_name'], 'John Doe')
        self.assertEqual(response.data['hike_percentage'], '10.00')
    
    def test_create_employee_hike(self):
        """Test that we can create an employee hike"""
        data = {
            'employee': self.employee.id,
            'hike_percentage': '15.00',
            'hike_reason': 'Promotion',
            'department': self.department.id,
            'previous_salary': '55000.00',
            'new_salary': '63250.00',
            'approved_by': self.manager.id
        }
        response = self.client.post(self.list_url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(EmployeeHike.objects.count(), 2)
        
    def test_update_employee_hike(self):
        """Test that we can update an employee hike"""
        data = {
            'employee': self.employee.id,
            'hike_percentage': '12.00',
            'hike_reason': 'Updated reason',
            'department': self.department.id,
            'previous_salary': '50000.00',
            'new_salary': '56000.00',
            'approved_by': self.manager.id
        }
        response = self.client.put(self.detail_url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.hike.refresh_from_db()
        self.assertEqual(self.hike.hike_percentage, Decimal('12.00'))
        self.assertEqual(self.hike.hike_reason, 'Updated reason')
        
    def test_delete_employee_hike(self):
        """Test that we can delete an employee hike"""
        response = self.client.delete(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(EmployeeHike.objects.count(), 0)
