# OrganizationDocument Model

The OrganizationDocument model represents documents related to an organization.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `title` | String | Title of the document | Yes |
| `organization` | ForeignKey | Organization this document belongs to | Yes |
| `description` | Text | Description of the document | No |
| `document_type` | String | Type of document (choices: policy, procedure, certificate, legal, registration, compliance, other) | Yes (default: other) |
| `file` | File | The document file | Yes |
| `version` | String | Version of the document | No |
| `effective_date` | Date | Date from which the document is effective | No |
| `expiry_date` | Date | Date until which the document is valid | No |
| `business_unit` | ForeignKey | Business unit this document is related to | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this document belongs to |
| `business_unit` | BusinessUnit | The business unit this document is related to |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the title of the document and its type |

## Example

```python
document = OrganizationDocument.objects.create(
    title="Certificate of Incorporation",
    organization=acme_corp,
    description="Certificate of incorporation of the company",
    document_type="certificate",
    file=certificate_file,
    version="1.0",
    effective_date="2000-01-01"
)
```
