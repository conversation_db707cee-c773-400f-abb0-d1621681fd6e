from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from organization_app.views.organization_views import OrganizationViewSet
from organization_app.views.location_views import LocationViewSet
from organization_app.views.designation_views import DesignationViewSet
from organization_app.views.business_unit_views import BusinessUnitViewSet
from organization_app.views.organization_policy_views import OrganizationPolicyViewSet
from organization_app.views.organization_document_views import OrganizationDocumentViewSet

app_name = 'organization'

router = DefaultRouter()
router.register(r'organizations', OrganizationViewSet)
router.register(r'locations', LocationViewSet)
router.register(r'designations', DesignationViewSet)
router.register(r'business-units', BusinessUnitViewSet)
router.register(r'policies', OrganizationPolicyViewSet)
router.register(r'documents', OrganizationDocumentViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
