"""
Views for AI Assistant functionality.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from common.utils.vector_chat import VectorChat
import logging

logger = logging.getLogger(__name__)

@swagger_auto_schema(
    tags=['AI Assistant'],
    operation_description="vectorchat management endpoints"
)
class VectorChatView(APIView):
    """
    API view for vector-enhanced chat functionality.
    This view combines vector search with Gemini AI to provide more accurate responses.
    """
    permission_classes = []  # Allow unauthenticated access for testing

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.vector_chat = None
        try:
            self.vector_chat = VectorChat(
                model_name=getattr(settings, 'GOOGLE_GENAI_MODEL', 'gemini-pro'),
                temperature=0.7
            )
            logger.info("VectorChat initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Vector Chat: {str(e)}")

    @swagger_auto_schema(
        operation_description="Chat with AI assistant using vector search for enhanced responses",
        operation_summary="AI Chat Assistant",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['question'],
            properties={
                'question': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='The question to ask the AI assistant',
                    example='What is the company leave policy?'
                )
            }
        ),
        responses={
            200: openapi.Response(
                description="AI response with relevant information",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'answer': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='AI generated response'
                        ),
                        'sources': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_STRING),
                            description='Source documents used for the response'
                        )
                    }
                )
            ),
            400: openapi.Response(description="Bad request - question is required"),
            500: openapi.Response(description="Internal server error"),
            503: openapi.Response(description="Service unavailable - AI service not configured")
        },
        tags=['AI Assistant']
    )
    def post(self, request, *args, **kwargs):
        """
        Handle POST requests to chat with the AI using vector search.
        """
        if not self.vector_chat:
            return Response(
                {"error": "Vector Chat is not available. Check API key configuration."},
                status=status.HTTP_503_SERVICE_UNAVAILABLE
            )

        question = request.data.get('question')
        if not question:
            return Response(
                {"error": "Question is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            response = self.vector_chat.chat(question)
            return Response(response)
        except Exception as e:
            logger.error(f"Error in Vector Chat: {str(e)}")
            return Response(
                {"error": "Failed to process your question"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
