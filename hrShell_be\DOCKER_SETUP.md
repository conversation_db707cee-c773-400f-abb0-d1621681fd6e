# Docker Setup for HR Shell

This document provides instructions for setting up the PostgreSQL database with pgvector extension using Docker.

## Prerequisites

- [Docker](https://www.docker.com/products/docker-desktop/) installed on your machine
- [Docker Compose](https://docs.docker.com/compose/install/) installed on your machine

## Getting Started

### 1. Start the PostgreSQL Container

Run the following command from the project root directory:

```bash
docker-compose up -d
```

This will:
- Pull the pgvector/pgvector:pg17 image if not already available
- Create a container named hrshell-postgres
- Set up PostgreSQL with the pgvector extension
- Create a persistent volume for your data
- Run initialization scripts to set up the database

### 2. Verify the Container is Running

```bash
docker ps
```

You should see the hrshell-postgres container running.

### 3. Connect to the Database

You can connect to the database using the following credentials:

- Host: localhost
- Port: 5432
- Database: shell_dwh
- Username: postgres
- Password: admin

Using psql:

```bash
psql -h localhost -p 5432 -U postgres -d shell_dwh
```

### 4. Verify pgvector Extension

To verify that the pgvector extension is installed and working:

```sql
SELECT * FROM vector_test LIMIT 1;
```

You should see a sample vector that was created during initialization.

### 5. Update Django Settings

The Django application is already configured to connect to this database. The settings are in:
- `.env` file
- `settings.py` file

### 6. Stopping the Container

To stop the container:

```bash
docker-compose down
```

To stop the container and remove the volume (this will delete all data):

```bash
docker-compose down -v
```

## Troubleshooting

### Port Conflict

If you already have PostgreSQL running on port 5432, you can change the port in the docker-compose.yml file:

```yaml
ports:
  - "5433:5432"  # Change 5433 to any available port
```

Then update the DB_PORT in your .env file accordingly.

### Connection Issues

If you're having trouble connecting to the database:

1. Ensure the container is running:
   ```bash
   docker ps
   ```

2. Check the container logs:
   ```bash
   docker logs hrshell-postgres
   ```

3. Try connecting from within the container:
   ```bash
   docker exec -it hrshell-postgres psql -U postgres -d shell_dwh
   ```

### Data Persistence

The database data is stored in a Docker volume named `postgres_data`. This ensures your data persists even if the container is removed.

To list all volumes:

```bash
docker volume ls
```

## Advanced Configuration

### Custom PostgreSQL Configuration

To customize PostgreSQL configuration, you can add a `postgresql.conf` file to the `init-scripts` directory and mount it in the docker-compose.yml file.

### Adding More Initialization Scripts

You can add more SQL scripts to the `init-scripts` directory. They will be executed in alphabetical order when the container starts for the first time.

## Using with Django

The Django application is already configured to use this database. Make sure the settings in your `.env` file match the Docker configuration.

If you change the database name, username, or password in the docker-compose.yml file, make sure to update the corresponding values in your `.env` file.
