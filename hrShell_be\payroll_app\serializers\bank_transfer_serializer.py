from rest_framework import serializers
from payroll_app.models.bank_transfer import BankTransfer


class BankTransferSerializer(serializers.ModelSerializer):
    organization_name = serializers.SerializerMethodField()
    payroll_name = serializers.SerializerMethodField()
    transfer_type_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = BankTransfer
        fields = [
            'id', 'payroll', 'payroll_name', 'organization', 'organization_name',
            'reference_number', 'transfer_date', 'bank_name', 'account_number',
            'transfer_type', 'transfer_type_display', 'total_amount', 'total_employees',
            'file_format', 'transfer_file', 'status', 'status_display', 'remarks',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_organization_name(self, obj):
        return obj.organization.name if obj.organization else None
    
    def get_payroll_name(self, obj):
        return obj.payroll.name if obj.payroll else None
    
    def get_transfer_type_display(self, obj):
        return obj.get_transfer_type_display()
    
    def get_status_display(self, obj):
        return obj.get_status_display()
    
    def validate(self, data):
        # Validate total amount
        if data.get('total_amount', 0) <= 0:
            raise serializers.ValidationError({"total_amount": "Total amount must be greater than 0"})
        
        # Validate total employees
        if data.get('total_employees', 0) <= 0:
            raise serializers.ValidationError({"total_employees": "Total employees must be greater than 0"})
        
        return data
