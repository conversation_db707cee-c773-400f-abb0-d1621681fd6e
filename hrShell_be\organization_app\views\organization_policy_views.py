from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from organization_app.models.organization_policy import OrganizationPolicy
from organization_app.serializers.organization_policy_serializer import OrganizationPolicySerializer
from organization_app.filters import OrganizationPolicyFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class OrganizationPolicyViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Organization Structure'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing OrganizationPolicy instances.
    
    Additional actions:
    - by_type: Get policies filtered by type
    - by_location: Get policies for a specific location
    - by_department: Get policies for a specific department
    - by_business_unit: Get policies for a specific business unit
    """
    queryset = OrganizationPolicy.objects.all()
    serializer_class = OrganizationPolicySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OrganizationPolicyFilter
    search_fields = ['name', 'description', 'content']
    ordering_fields = ['name', 'policy_type', 'created_at']
    
    @action(detail=False, methods=['get'])
    def by_organization(self, request):
        """
        Get policies filtered by organization
        """
        organization_id = request.query_params.get('organization_id')
        if not organization_id:
            return Response(
                {"error": "organization_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        policies = OrganizationPolicy.objects.filter(organization_id=organization_id)
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """
        Get policies filtered by type
        """
        policy_type = request.query_params.get('policy_type')
        if not policy_type:
            return Response(
                {"error": "policy_type query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        policies = OrganizationPolicy.objects.filter(policy_type=policy_type)
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_location(self, request):
        """
        Get policies for a specific location
        """
        location_id = request.query_params.get('location_id')
        if not location_id:
            return Response(
                {"error": "location_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        policies = OrganizationPolicy.objects.filter(locations__id=location_id)
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_department(self, request):
        """
        Get policies for a specific department
        """
        department_id = request.query_params.get('department_id')
        if not department_id:
            return Response(
                {"error": "department_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        policies = OrganizationPolicy.objects.filter(departments__id=department_id)
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_business_unit(self, request):
        """
        Get policies for a specific business unit
        """
        business_unit_id = request.query_params.get('business_unit_id')
        if not business_unit_id:
            return Response(
                {"error": "business_unit_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        policies = OrganizationPolicy.objects.filter(business_units__id=business_unit_id)
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
