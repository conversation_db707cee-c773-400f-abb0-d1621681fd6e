# Employee Hikes API

This section provides detailed information about the employee hike-related endpoints in the HR Management API.

## List Employee Hikes

Retrieves a list of employee hikes.

```http
GET /api/v1/employee-hikes/
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `employee` | integer | Filter by employee ID |
| `department` | integer | Filter by department ID |
| `employee_name` | string | Filter by employee name |
| `department_name` | string | Filter by department name |
| `min_percentage` | number | Filter by minimum hike percentage |
| `max_percentage` | number | Filter by maximum hike percentage |
| `effective_after` | date | Filter by effective date (format: YYYY-MM-DD) |
| `effective_before` | date | Filter by effective date (format: YYYY-MM-DD) |
| `min_new_salary` | number | Filter by minimum new salary |
| `max_new_salary` | number | Filter by maximum new salary |
| `search` | string | Search in employee name and hike reason |
| `ordering` | string | Order by field (prefix with `-` for descending order) |

### Response

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "employee_name": "<PERSON> Doe",
      "hike_percentage": "10.00",
      "hike_effective_date": "2023-05-01",
      "department_name": "Engineering",
      "previous_salary": "50000.00",
      "new_salary": "55000.00"
    },
    {
      "id": 2,
      "employee_name": "Jane Smith",
      "hike_percentage": "15.00",
      "hike_effective_date": "2023-06-01",
      "department_name": "Product",
      "previous_salary": "60000.00",
      "new_salary": "69000.00"
    }
  ]
}
```

## Get Employee Hike

Retrieves a specific employee hike by ID.

```http
GET /api/v1/employee-hikes/{id}/
```

### Response

```json
{
  "id": 1,
  "employee": 1,
  "employee_name": "John Doe",
  "hike_percentage": "10.00",
  "hike_effective_date": "2023-05-01",
  "hike_reason": "Annual performance review",
  "department": 1,
  "department_name": "Engineering",
  "previous_salary": "50000.00",
  "new_salary": "55000.00",
  "approved_by": 2,
  "approved_by_name": "Jane Smith"
}
```

## Create Employee Hike

Creates a new employee hike.

```http
POST /api/v1/employee-hikes/
Content-Type: application/json
```

### Request Body

```json
{
  "employee": 1,
  "hike_percentage": "10.00",
  "hike_effective_date": "2023-05-01",
  "hike_reason": "Annual performance review",
  "department": 1,
  "previous_salary": "50000.00",
  "new_salary": "55000.00",
  "approved_by": 2
}
```

### Response

```json
{
  "id": 1,
  "employee": 1,
  "employee_name": "John Doe",
  "hike_percentage": "10.00",
  "hike_effective_date": "2023-05-01",
  "hike_reason": "Annual performance review",
  "department": 1,
  "department_name": "Engineering",
  "previous_salary": "50000.00",
  "new_salary": "55000.00",
  "approved_by": 2,
  "approved_by_name": "Jane Smith"
}
```

## Update Employee Hike

Updates an existing employee hike.

```http
PUT /api/v1/employee-hikes/{id}/
Content-Type: application/json
```

### Request Body

```json
{
  "employee": 1,
  "hike_percentage": "12.00",
  "hike_effective_date": "2023-05-01",
  "hike_reason": "Updated reason",
  "department": 1,
  "previous_salary": "50000.00",
  "new_salary": "56000.00",
  "approved_by": 2
}
```

### Response

```json
{
  "id": 1,
  "employee": 1,
  "employee_name": "John Doe",
  "hike_percentage": "12.00",
  "hike_effective_date": "2023-05-01",
  "hike_reason": "Updated reason",
  "department": 1,
  "department_name": "Engineering",
  "previous_salary": "50000.00",
  "new_salary": "56000.00",
  "approved_by": 2,
  "approved_by_name": "Jane Smith"
}
```

## Partial Update Employee Hike

Updates specific fields of an existing employee hike.

```http
PATCH /api/v1/employee-hikes/{id}/
Content-Type: application/json
```

### Request Body

```json
{
  "hike_percentage": "12.00",
  "new_salary": "56000.00"
}
```

### Response

```json
{
  "id": 1,
  "employee": 1,
  "employee_name": "John Doe",
  "hike_percentage": "12.00",
  "hike_effective_date": "2023-05-01",
  "hike_reason": "Annual performance review",
  "department": 1,
  "department_name": "Engineering",
  "previous_salary": "50000.00",
  "new_salary": "56000.00",
  "approved_by": 2,
  "approved_by_name": "Jane Smith"
}
```

## Delete Employee Hike

Deletes an employee hike.

```http
DELETE /api/v1/employee-hikes/{id}/
```

### Response

```
204 No Content
```
