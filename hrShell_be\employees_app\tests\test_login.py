from django.urls import reverse
from django.contrib.auth.models import User
from rest_framework.test import APITestCase
from rest_framework import status
from django.test import override_settings
import json


class LoginEndpointTests(APITestCase):
    """
    Test cases for the enhanced login endpoint following Augment guidelines.
    
    These tests ensure the login endpoint works correctly with proper
    authentication, error handling, and security features.
    """

    def setUp(self):
        """Set up test data for login tests."""
        # Create test users
        self.active_user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123',
            first_name='Test',
            last_name='User'
        )
        
        self.inactive_user = User.objects.create_user(
            username='inactiveuser',
            email='<EMAIL>',
            password='testpassword123',
            first_name='Inactive',
            last_name='User',
            is_active=False
        )
        
        self.staff_user = User.objects.create_user(
            username='staffuser',
            email='<EMAIL>',
            password='testpassword123',
            first_name='Staff',
            last_name='User',
            is_staff=True
        )
        
        # URL for the new login endpoint
        self.login_url = reverse('login')
        
        # Valid login data
        self.valid_login_data = {
            'username': 'testuser',
            'password': 'testpassword123'
        }

    def test_successful_login(self):
        """Test successful login with valid credentials."""
        response = self.client.post(
            self.login_url,
            data=self.valid_login_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check response structure
        data = response.json()
        self.assertIn('access', data)
        self.assertIn('refresh', data)
        self.assertIn('user', data)
        self.assertIn('message', data)
        
        # Check user information
        user_data = data['user']
        self.assertEqual(user_data['username'], 'testuser')
        self.assertEqual(user_data['email'], '<EMAIL>')
        self.assertEqual(user_data['first_name'], 'Test')
        self.assertEqual(user_data['last_name'], 'User')
        self.assertEqual(user_data['full_name'], 'Test User')
        self.assertEqual(user_data['is_staff'], False)
        self.assertEqual(user_data['is_superuser'], False)
        
        # Check tokens are strings
        self.assertIsInstance(data['access'], str)
        self.assertIsInstance(data['refresh'], str)
        self.assertTrue(len(data['access']) > 0)
        self.assertTrue(len(data['refresh']) > 0)

    def test_staff_user_login(self):
        """Test login with staff user credentials."""
        login_data = {
            'username': 'staffuser',
            'password': 'testpassword123'
        }
        
        response = self.client.post(
            self.login_url,
            data=login_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        user_data = data['user']
        self.assertEqual(user_data['is_staff'], True)
        self.assertEqual(user_data['is_superuser'], False)

    def test_invalid_username(self):
        """Test login with invalid username."""
        invalid_data = {
            'username': 'nonexistentuser',
            'password': 'testpassword123'
        }
        
        response = self.client.post(
            self.login_url,
            data=invalid_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        data = response.json()
        self.assertIn('detail', data)
        self.assertIn('Invalid username or password', data['detail'])

    def test_invalid_password(self):
        """Test login with invalid password."""
        invalid_data = {
            'username': 'testuser',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(
            self.login_url,
            data=invalid_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        data = response.json()
        self.assertIn('detail', data)
        self.assertIn('Invalid username or password', data['detail'])

    def test_inactive_user_login(self):
        """Test login with inactive user account."""
        inactive_data = {
            'username': 'inactiveuser',
            'password': 'testpassword123'
        }
        
        response = self.client.post(
            self.login_url,
            data=inactive_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        data = response.json()
        self.assertIn('detail', data)
        self.assertIn('deactivated', data['detail'])

    def test_missing_username(self):
        """Test login with missing username."""
        invalid_data = {
            'password': 'testpassword123'
        }
        
        response = self.client.post(
            self.login_url,
            data=invalid_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        data = response.json()
        self.assertIn('detail', data)
        self.assertIn('Both username and password are required', data['detail'])

    def test_missing_password(self):
        """Test login with missing password."""
        invalid_data = {
            'username': 'testuser'
        }
        
        response = self.client.post(
            self.login_url,
            data=invalid_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        data = response.json()
        self.assertIn('detail', data)
        self.assertIn('Both username and password are required', data['detail'])

    def test_empty_credentials(self):
        """Test login with empty credentials."""
        empty_data = {
            'username': '',
            'password': ''
        }
        
        response = self.client.post(
            self.login_url,
            data=empty_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_login_updates_last_login(self):
        """Test that successful login updates the user's last_login field."""
        # Check initial last_login is None
        user = User.objects.get(username='testuser')
        initial_last_login = user.last_login
        
        response = self.client.post(
            self.login_url,
            data=self.valid_login_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that last_login was updated
        user.refresh_from_db()
        self.assertIsNotNone(user.last_login)
        self.assertNotEqual(user.last_login, initial_last_login)

    def test_login_endpoint_allows_anonymous_access(self):
        """Test that the login endpoint allows anonymous access."""
        # This test ensures the endpoint doesn't require authentication
        response = self.client.post(
            self.login_url,
            data=self.valid_login_data,
            format='json'
        )
        
        # Should not return 401 Unauthorized
        self.assertNotEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_response_format_consistency(self):
        """Test that the response format is consistent with API standards."""
        response = self.client.post(
            self.login_url,
            data=self.valid_login_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response['Content-Type'], 'application/json')
        
        # Ensure response is valid JSON
        try:
            json.loads(response.content)
        except json.JSONDecodeError:
            self.fail("Response is not valid JSON")
