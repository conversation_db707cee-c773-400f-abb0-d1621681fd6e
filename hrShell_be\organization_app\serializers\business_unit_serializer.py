from rest_framework import serializers
from organization_app.models.business_unit import BusinessUnit, BusinessUnitStatus


class BusinessUnitSerializer(serializers.ModelSerializer):
    """
    Serializer for the BusinessUnit model
    """
    organization_name = serializers.ReadOnlyField(source='organization.name')
    parent_name = serializers.ReadOnlyField(source='parent.name')
    head_name = serializers.ReadOnlyField(source='head.full_name')
    primary_location_name = serializers.ReadOnlyField(source='primary_location.name')
    status_display = serializers.ReadOnlyField(source='get_status_display')
    department_count = serializers.ReadOnlyField()
    
    class Meta:
        model = BusinessUnit
        fields = [
            'id', 'name', 'organization', 'organization_name', 'description',
            'code', 'parent', 'parent_name', 'head', 'head_name',
            'primary_location', 'primary_location_name', 'status',
            'status_display', 'department_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_status(self, value):
        """
        Validate that the status is one of the allowed choices
        """
        try:
            BusinessUnitStatus(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError(
                f"Status must be one of: {', '.join([s.value for s in BusinessUnitStatus])}"
            )
    
    def validate(self, data):
        """
        Validate that the business unit name is unique within the organization
        """
        organization = data.get('organization')
        name = data.get('name')
        
        # Skip validation if organization or name is not provided
        if not organization or not name:
            return data
        
        # Check if we're updating an existing instance
        instance = getattr(self, 'instance', None)
        if instance and instance.organization == organization and instance.name == name:
            return data
        
        # Check if a business unit with the same name already exists in the organization
        if BusinessUnit.objects.filter(organization=organization, name=name).exists():
            raise serializers.ValidationError(
                {"name": "A business unit with this name already exists in the organization."}
            )
        
        # Validate that parent belongs to the same organization
        parent = data.get('parent')
        if parent and parent.organization != organization:
            raise serializers.ValidationError(
                {"parent": "Parent business unit must belong to the same organization."}
            )
        
        return data


class BusinessUnitDetailSerializer(BusinessUnitSerializer):
    """
    Detailed serializer for the BusinessUnit model including related entities
    """
    children = serializers.SerializerMethodField()
    departments = serializers.SerializerMethodField()
    
    class Meta(BusinessUnitSerializer.Meta):
        fields = BusinessUnitSerializer.Meta.fields + [
            'children', 'departments'
        ]
    
    def get_children(self, obj):
        children = obj.children.all()
        return BusinessUnitSerializer(children, many=True).data
    
    def get_departments(self, obj):
        from employees_app.serializers.department_serializer import DepartmentSerializer
        departments = obj.departments.all()
        return DepartmentSerializer(departments, many=True).data
