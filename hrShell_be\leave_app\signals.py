from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from leave_app.models.leave_request import LeaveRequest, LeaveRequestStatus
from leave_app.models.leave_approval import LeaveApproval, ApprovalStatus
from leave_app.models.leave_balance import LeaveBalance


@receiver(post_save, sender=LeaveRequest)
def create_leave_approval(sender, instance, created, **kwargs):
    """
    Create leave approval records when a leave request is created.
    """
    if created and instance.status == LeaveRequestStatus.PENDING.value:
        # Check if the leave type requires approval
        if instance.leave_type.requires_approval:
            # Get the employee's manager as the approver
            manager = None
            if instance.employee.department and instance.employee.department.manager:
                manager = instance.employee.department.manager
            
            if manager:
                LeaveApproval.objects.create(
                    leave_request=instance,
                    approver=manager,
                    level=1,
                    status=ApprovalStatus.PENDING.value
                )


@receiver(post_save, sender=LeaveApproval)
def update_leave_request_status(sender, instance, created, **kwargs):
    """
    Update leave request status when all approvals are complete.
    """
    if not created and instance.status in [ApprovalStatus.APPROVED.value, ApprovalStatus.REJECTED.value]:
        leave_request = instance.leave_request
        
        # If approval is rejected, reject the leave request
        if instance.status == ApprovalStatus.REJECTED.value:
            leave_request.status = LeaveRequestStatus.REJECTED.value
            leave_request.approved_by = instance.approver
            leave_request.approval_date = timezone.now()
            leave_request.rejection_reason = instance.comments
            leave_request.save()
            
            # Mark all other pending approvals as skipped
            leave_request.approvals.filter(status=ApprovalStatus.PENDING.value).update(
                status=ApprovalStatus.SKIPPED.value,
                comments="Skipped due to rejection at an earlier level",
                approval_date=timezone.now()
            )
        
        # If approval is approved, check if all approvals are complete
        elif instance.status == ApprovalStatus.APPROVED.value:
            pending_approvals = leave_request.approvals.filter(status=ApprovalStatus.PENDING.value)
            
            if not pending_approvals.exists():
                # All approvals are complete, update leave request status
                leave_request.status = LeaveRequestStatus.APPROVED.value
                leave_request.approved_by = instance.approver
                leave_request.approval_date = timezone.now()
                leave_request.save()
                
                # Update leave balance
                try:
                    current_year = leave_request.start_date.year
                    leave_balance = LeaveBalance.objects.get(
                        employee=leave_request.employee,
                        leave_type=leave_request.leave_type,
                        year=current_year
                    )
                    
                    # Update used days
                    leave_balance.used += leave_request.total_days
                    leave_balance.save()
                    
                except LeaveBalance.DoesNotExist:
                    # If no balance exists, create one
                    LeaveBalance.objects.create(
                        employee=leave_request.employee,
                        leave_type=leave_request.leave_type,
                        year=current_year,
                        used=leave_request.total_days
                    )


@receiver(pre_save, sender=LeaveRequest)
def update_leave_balance_on_cancel(sender, instance, **kwargs):
    """
    Update leave balance when a leave request is cancelled.
    """
    if instance.id:
        try:
            old_instance = LeaveRequest.objects.get(id=instance.id)
            
            # If the leave request was approved and is now being cancelled
            if old_instance.status == LeaveRequestStatus.APPROVED.value and instance.status == LeaveRequestStatus.CANCELLED.value:
                try:
                    current_year = instance.start_date.year
                    leave_balance = LeaveBalance.objects.get(
                        employee=instance.employee,
                        leave_type=instance.leave_type,
                        year=current_year
                    )
                    
                    # Restore used days
                    leave_balance.used -= instance.total_days
                    leave_balance.save()
                    
                except LeaveBalance.DoesNotExist:
                    pass
        except LeaveRequest.DoesNotExist:
            pass
