from django.db import models
from employees_app.models.employee import Employee
from onboarding_offboarding_app.models.offboarding import OffboardingRequest


class ExitFeedback(models.Model):
    """
    Model to represent exit feedback from employees leaving the company.
    """
    RATING_CHOICES = [
        (1, 'Very Dissatisfied'),
        (2, 'Dissatisfied'),
        (3, 'Neutral'),
        (4, 'Satisfied'),
        (5, 'Very Satisfied'),
    ]
    
    # Basic Information
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='exit_feedback')
    offboarding_request = models.OneToOneField(OffboardingRequest, on_delete=models.CASCADE, related_name='exit_feedback')
    
    # Feedback on Company
    overall_experience = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)
    work_environment = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)
    work_life_balance = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)
    compensation_benefits = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)
    career_growth = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)
    management = models.PositiveIntegerField(choices=RATING_CHOICES, null=True, blank=True)
    
    # Detailed Feedback
    reason_for_leaving = models.TextField(blank=True, null=True)
    what_did_you_like = models.TextField(blank=True, null=True)
    what_could_be_improved = models.TextField(blank=True, null=True)
    would_recommend = models.BooleanField(null=True, blank=True)
    would_return = models.BooleanField(null=True, blank=True)
    
    # Additional Comments
    additional_comments = models.TextField(blank=True, null=True)
    
    # Interview Details
    conducted_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='conducted_exit_interviews')
    interview_date = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Exit Feedback'
        verbose_name_plural = 'Exit Feedback'
    
    def __str__(self):
        return f"Exit Feedback for {self.employee}"
