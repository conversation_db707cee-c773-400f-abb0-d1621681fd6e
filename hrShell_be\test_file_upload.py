#!/usr/bin/env python3
"""
Test script to debug file upload issues in employee documents.
"""

import os
import sys
import django
import requests
from io import BytesIO

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hrShell_be.settings')
django.setup()

from employees_app.models.employee_documents import EmployeeDocument
from employees_app.models.employee import Employee
from django.core.files.uploadedfile import SimpleUploadedFile

def test_file_upload():
    """Test file upload functionality"""
    print("🧪 Testing Employee Document File Upload...")
    
    # Check if we have any employees
    employees = Employee.objects.all()
    if not employees.exists():
        print("❌ No employees found. Creating a test employee...")
        # Create a test employee
        employee = Employee.objects.create(
            first_name="Test",
            last_name="Employee",
            email="<EMAIL>",
            position="Developer"
        )
        print(f"✅ Created test employee: {employee}")
    else:
        employee = employees.first()
        print(f"✅ Using existing employee: {employee}")
    
    # Create a test file
    test_file_content = b"This is a test document content for employee documents."
    test_file = SimpleUploadedFile(
        name="test_document.txt",
        content=test_file_content,
        content_type="text/plain"
    )
    
    # Test 1: Create document with file using Django ORM
    print("\n📝 Test 1: Creating document using Django ORM...")
    try:
        document = EmployeeDocument.objects.create(
            employee=employee,
            document_type='resume',
            file=test_file,
            document_name="Test Resume",
            description="Test document for debugging"
        )
        print(f"✅ Document created: ID={document.id}")
        print(f"✅ File field: {document.file}")
        print(f"✅ File name: {document.file.name if document.file else 'None'}")
        print(f"✅ File URL: {document.file.url if document.file else 'None'}")
        
        # Check if file exists on disk
        if document.file:
            file_path = document.file.path
            print(f"✅ File path: {file_path}")
            print(f"✅ File exists on disk: {os.path.exists(file_path)}")
            if os.path.exists(file_path):
                print(f"✅ File size: {os.path.getsize(file_path)} bytes")
        
    except Exception as e:
        print(f"❌ Error creating document: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Test API endpoint
    print("\n🌐 Test 2: Testing API endpoint...")
    try:
        # Create another test file for API test
        api_test_file = BytesIO(b"API test document content")
        api_test_file.name = "api_test.txt"
        
        # Prepare form data
        data = {
            'employee': employee.id,
            'document_type': 'contract',
            'document_name': 'API Test Contract',
            'description': 'Test document via API'
        }
        
        files = {
            'file': ('api_test.txt', api_test_file, 'text/plain')
        }
        
        # Make API request
        response = requests.post(
            'http://127.0.0.1:8000/api/v1/employee-documents/',
            data=data,
            files=files
        )
        
        print(f"✅ API Response Status: {response.status_code}")
        print(f"✅ API Response: {response.json()}")
        
        if response.status_code == 201:
            response_data = response.json()
            print(f"✅ Created document via API: ID={response_data.get('id')}")
            print(f"✅ File field: {response_data.get('file')}")
            print(f"✅ File URL: {response_data.get('file_url')}")
        
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Check all documents for employee
    print(f"\n📋 Test 3: Checking all documents for employee {employee.id}...")
    try:
        documents = EmployeeDocument.objects.filter(employee=employee)
        print(f"✅ Found {documents.count()} documents")
        
        for doc in documents:
            print(f"  - Document {doc.id}: {doc.document_type}")
            print(f"    File: {doc.file}")
            print(f"    File name: {doc.file.name if doc.file else 'None'}")
            print(f"    File URL: {doc.file.url if doc.file else 'None'}")
            
            # Test API endpoint for this employee
            api_response = requests.get(f'http://127.0.0.1:8000/api/v1/employee-documents/?employee={employee.id}')
            print(f"    API Response Status: {api_response.status_code}")
            if api_response.status_code == 200:
                api_data = api_response.json()
                print(f"    API Documents Count: {len(api_data.get('results', []))}")
                for api_doc in api_data.get('results', []):
                    print(f"      API Doc {api_doc.get('id')}: file={api_doc.get('file')}, file_url={api_doc.get('file_url')}")
    
    except Exception as e:
        print(f"❌ Error checking documents: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 File upload test completed!")

if __name__ == "__main__":
    test_file_upload()
