from django.test import TestCase
from organization_app.models.organization import Organization
from organization_app.models.location import Location
from organization_app.models.business_unit import BusinessUnit
from organization_app.models.designation import Designation
from organization_app.models.organization_policy import OrganizationPolicy
from organization_app.models.organization_document import OrganizationDocument
from datetime import date


class OrganizationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test organization
        cls.organization = Organization.objects.create(
            name="Test Organization",
            registration_number="TEST123",
            business_type="private_limited",
            gst_number="GST123456789",
            pan_number="PAN123456789",
            ein_number="EIN123456789",
            industry="technology",
            currency="usd",
            timezone="UTC",
            fiscal_year="jan_dec",
            website="https://example.com",
            email="<EMAIL>",
            phone="+1234567890",
            status="active"
        )
    
    def test_organization_creation(self):
        self.assertEqual(self.organization.name, "Test Organization")
        self.assertEqual(self.organization.registration_number, "TEST123")
        self.assertEqual(self.organization.business_type, "private_limited")
        self.assertEqual(self.organization.status, "active")
    
    def test_organization_str(self):
        self.assertEqual(str(self.organization), "Test Organization")


class LocationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test organization
        cls.organization = Organization.objects.create(
            name="Test Organization",
            registration_number="TEST123",
            business_type="private_limited"
        )
        
        # Create test location
        cls.location = Location.objects.create(
            name="Test Location",
            organization=cls.organization,
            address_line1="123 Test Street",
            city="Test City",
            state="Test State",
            country="Test Country",
            postal_code="12345",
            phone="+1234567890",
            email="<EMAIL>",
            is_headquarters=True,
            status="active"
        )
    
    def test_location_creation(self):
        self.assertEqual(self.location.name, "Test Location")
        self.assertEqual(self.location.organization, self.organization)
        self.assertEqual(self.location.city, "Test City")
        self.assertTrue(self.location.is_headquarters)
        self.assertEqual(self.location.status, "active")
    
    def test_location_str(self):
        self.assertEqual(str(self.location), "Test Location (Test Organization)")


# Add more test classes for other models as needed
