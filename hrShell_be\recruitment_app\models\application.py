from django.db import models
from django.core.validators import Min<PERSON><PERSON>ueValidator, MaxValueValidator
from recruitment_app.models.job_posting import JobPosting
from recruitment_app.models.candidate import Candidate
from recruitment_app.models.recruitment_source import RecruitmentSource
from employees_app.models.employee import Employee


class Application(models.Model):
    """
    Model to represent job applications submitted by candidates.
    """
    STATUS_CHOICES = [
        ('new', 'New'),
        ('screening', 'Screening'),
        ('shortlisted', 'Shortlisted'),
        ('interview', 'Interview'),
        ('assessment', 'Assessment'),
        ('offer', 'Offer'),
        ('hired', 'Hired'),
        ('rejected', 'Rejected'),
        ('withdrawn', 'Withdrawn'),
        ('on_hold', 'On Hold'),
    ]
    
    STAGE_CHOICES = [
        ('application', 'Application'),
        ('resume_screening', 'Resume Screening'),
        ('phone_screening', 'Phone Screening'),
        ('assessment_test', 'Assessment Test'),
        ('first_interview', 'First Interview'),
        ('second_interview', 'Second Interview'),
        ('final_interview', 'Final Interview'),
        ('reference_check', 'Reference Check'),
        ('background_check', 'Background Check'),
        ('offer_stage', 'Offer Stage'),
        ('onboarding', 'Onboarding'),
    ]
    
    # Basic Information
    job_posting = models.ForeignKey(JobPosting, on_delete=models.CASCADE, related_name='applications')
    candidate = models.ForeignKey(Candidate, on_delete=models.CASCADE, related_name='applications')
    application_date = models.DateTimeField(auto_now_add=True)
    
    # Application Details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new')
    current_stage = models.CharField(max_length=20, choices=STAGE_CHOICES, default='application')
    source = models.ForeignKey(RecruitmentSource, on_delete=models.SET_NULL, null=True, blank=True, related_name='applications')
    
    # Cover Letter and Resume
    cover_letter = models.TextField(blank=True, null=True)
    resume = models.FileField(upload_to='application_resumes/%Y/%m/', blank=True, null=True)
    
    # Screening and Rating
    is_shortlisted = models.BooleanField(default=False)
    rating = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)], blank=True, null=True)
    skills_match_percentage = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(100)], blank=True, null=True)
    
    # Salary Information
    expected_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], blank=True, null=True)
    negotiated_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)], blank=True, null=True)
    
    # Availability
    available_from = models.DateField(blank=True, null=True)
    notice_period = models.PositiveIntegerField(blank=True, null=True, help_text="Notice period in days")
    
    # Rejection Information
    rejection_reason = models.TextField(blank=True, null=True)
    rejection_stage = models.CharField(max_length=20, choices=STAGE_CHOICES, blank=True, null=True)
    rejected_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='applications_rejected')
    rejection_date = models.DateTimeField(blank=True, null=True)
    
    # Withdrawal Information
    withdrawal_reason = models.TextField(blank=True, null=True)
    withdrawal_date = models.DateTimeField(blank=True, null=True)
    
    # Notes and Feedback
    notes = models.TextField(blank=True, null=True)
    internal_feedback = models.TextField(blank=True, null=True)
    
    # Assigned Recruiter
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_applications')
    
    # Timestamps
    last_status_change = models.DateTimeField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-application_date']
        verbose_name = 'Application'
        verbose_name_plural = 'Applications'
        unique_together = ['job_posting', 'candidate']
    
    def __str__(self):
        return f"{self.candidate} - {self.job_posting.title}"


class ApplicationStageHistory(models.Model):
    """
    Model to track the history of application stage changes.
    """
    application = models.ForeignKey(Application, on_delete=models.CASCADE, related_name='stage_history')
    from_stage = models.CharField(max_length=20, choices=Application.STAGE_CHOICES, blank=True, null=True)
    to_stage = models.CharField(max_length=20, choices=Application.STAGE_CHOICES)
    from_status = models.CharField(max_length=20, choices=Application.STATUS_CHOICES, blank=True, null=True)
    to_status = models.CharField(max_length=20, choices=Application.STATUS_CHOICES)
    changed_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='application_stage_changes')
    changed_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True, null=True)
    
    class Meta:
        ordering = ['-changed_at']
        verbose_name = 'Application Stage History'
        verbose_name_plural = 'Application Stage History'
    
    def __str__(self):
        return f"{self.application} - {self.from_stage} to {self.to_stage}"
