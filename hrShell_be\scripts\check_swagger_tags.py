#!/usr/bin/env python3
"""
<PERSON><PERSON>t to check all ViewSets and their current Swagger tags.
"""

import os
import re
from pathlib import Path

def find_python_files(directory):
    """Find all Python files in the project views."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Skip virtual environment, cache directories, and other non-project directories
        if any(skip_dir in root for skip_dir in ['.venv', '__pycache__', '.git', 'site-packages', 'Scripts', 'Lib']):
            continue
        if 'views' in root:
            for file in files:
                if file.endswith('.py') and file != '__init__.py':
                    python_files.append(os.path.join(root, file))
    return python_files

def extract_viewsets_and_tags(file_path):
    """Extract ViewSets and their tags from a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all ViewSet/View classes
        class_pattern = r'class\s+(\w+(?:ViewSet|View))\s*\([^)]*\):'
        classes = re.findall(class_pattern, content)
        
        results = []
        for class_name in classes:
            # Look for swagger_auto_schema decorator before this class
            class_start = content.find(f'class {class_name}')
            if class_start == -1:
                continue
                
            # Look backwards for the decorator
            before_class = content[:class_start]
            
            # Find the last @swagger_auto_schema decorator before this class
            decorator_pattern = r'@swagger_auto_schema\s*\([^)]*tags=\[([^\]]*)\][^)]*\)'
            decorators = list(re.finditer(decorator_pattern, before_class))
            
            if decorators:
                # Get the last decorator (closest to the class)
                last_decorator = decorators[-1]
                tags_match = last_decorator.group(1)
                # Clean up the tags (remove quotes and whitespace)
                tags = [tag.strip().strip('\'"') for tag in tags_match.split(',') if tag.strip()]
                results.append((class_name, tags))
            else:
                results.append((class_name, None))
        
        return results
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []

def main():
    """Main function to check all tags."""
    print("🔍 Checking all ViewSets and their Swagger tags...")
    
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Find all Python files in views
    python_files = find_python_files(project_root)
    
    # Filter to only our app files
    project_files = [f for f in python_files if any(app in f for app in [
        'employees_app', 'organization_app', 'leave_app', 'payroll_app', 
        'recruitment_app', 'onboarding_offboarding_app', 'common'
    ])]
    
    print(f"📁 Found {len(project_files)} view files to check")
    
    all_viewsets = {}
    untagged_viewsets = []
    
    # Process each file
    for file_path in project_files:
        relative_path = os.path.relpath(file_path, project_root)
        viewsets = extract_viewsets_and_tags(file_path)
        
        if viewsets:
            print(f"\n📄 {relative_path}")
            for class_name, tags in viewsets:
                if tags:
                    print(f"  ✅ {class_name}: {tags}")
                    all_viewsets[class_name] = tags
                else:
                    print(f"  ❌ {class_name}: NO TAGS")
                    untagged_viewsets.append((relative_path, class_name))
    
    # Summary
    print(f"\n📊 Summary:")
    print(f"  Total ViewSets/Views found: {len(all_viewsets) + len(untagged_viewsets)}")
    print(f"  Tagged ViewSets/Views: {len(all_viewsets)}")
    print(f"  Untagged ViewSets/Views: {len(untagged_viewsets)}")
    
    if untagged_viewsets:
        print(f"\n❌ Untagged ViewSets/Views:")
        for file_path, class_name in untagged_viewsets:
            print(f"  - {class_name} in {file_path}")
    
    # Group by tags
    if all_viewsets:
        print(f"\n📋 ViewSets grouped by tags:")
        tag_groups = {}
        for class_name, tags in all_viewsets.items():
            for tag in tags:
                if tag not in tag_groups:
                    tag_groups[tag] = []
                tag_groups[tag].append(class_name)
        
        for tag, classes in sorted(tag_groups.items()):
            print(f"  📁 {tag}: {len(classes)} ViewSets")
            for class_name in sorted(classes):
                print(f"    - {class_name}")

if __name__ == "__main__":
    main()
