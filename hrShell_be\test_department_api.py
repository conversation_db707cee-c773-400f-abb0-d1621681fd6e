#!/usr/bin/env python3
"""
Simple test script to verify that the department API works without permission errors.
"""

import requests
import json

def test_department_api():
    """Test the department API endpoints."""
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 Testing Department API endpoints...")
    
    # Test GET departments list
    print("\n1. Testing GET /api/v1/departments/")
    try:
        response = requests.get(f"{base_url}/api/v1/departments/")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ GET departments list - SUCCESS")
            data = response.json()
            print(f"   📊 Found {len(data.get('results', data))} departments")
        else:
            print(f"   ❌ GET departments list - FAILED: {response.text}")
    except Exception as e:
        print(f"   ❌ GET departments list - ERROR: {e}")
    
    # Test POST create department
    print("\n2. Testing POST /api/v1/departments/")
    test_department = {
        "name": "Test Department",
        "description": "A test department created via API",
        "is_active": True
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/departments/",
            json=test_department,
            headers={"Content-Type": "application/json"}
        )
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 201:
            print("   ✅ POST create department - SUCCESS")
            data = response.json()
            print(f"   📝 Created department: {data.get('name')} (ID: {data.get('id')})")
            return data.get('id')  # Return the ID for cleanup
        else:
            print(f"   ❌ POST create department - FAILED: {response.text}")
            return None
    except Exception as e:
        print(f"   ❌ POST create department - ERROR: {e}")
        return None

def cleanup_test_department(department_id):
    """Clean up the test department."""
    if not department_id:
        return
        
    base_url = "http://127.0.0.1:8000"
    print(f"\n🧹 Cleaning up test department (ID: {department_id})")
    
    try:
        response = requests.delete(f"{base_url}/api/v1/departments/{department_id}/")
        if response.status_code == 204:
            print("   ✅ Test department deleted successfully")
        else:
            print(f"   ⚠️  Could not delete test department: {response.status_code}")
    except Exception as e:
        print(f"   ⚠️  Error deleting test department: {e}")

if __name__ == "__main__":
    print("🚀 Starting Department API Test")
    print("=" * 50)
    
    # Run the test
    created_id = test_department_api()
    
    # Clean up
    cleanup_test_department(created_id)
    
    print("\n" + "=" * 50)
    print("🏁 Department API Test Completed")
