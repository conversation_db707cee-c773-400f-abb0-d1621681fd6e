from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from organization_app.models.location import Location
from organization_app.serializers.location_serializer import LocationSerializer, LocationDetailSerializer
from organization_app.filters import LocationFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class LocationViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Organization Structure'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing Location instances.
    
    Additional actions:
    - business_units: Get business units for a specific location
    """
    queryset = Location.objects.all()
    serializer_class = LocationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LocationFilter
    search_fields = ['name', 'city', 'country']
    ordering_fields = ['name', 'created_at']

    def get_serializer_class(self):
        """
        Return different serializers based on the action
        """
        if self.action == 'retrieve':
            return LocationDetailSerializer
        return LocationSerializer
    
    @action(detail=True, methods=['get'])
    def business_units(self, request, pk=None):
        """
        Get business units for a specific location
        """
        location = self.get_object()
        from organization_app.serializers.business_unit_serializer import BusinessUnitSerializer
        business_units = location.business_units.all()
        serializer = BusinessUnitSerializer(business_units, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def headquarters(self, request):
        """
        Get all headquarters locations
        """
        headquarters = Location.objects.filter(is_headquarters=True)
        serializer = self.get_serializer(headquarters, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_organization(self, request):
        """
        Get locations filtered by organization
        """
        organization_id = request.query_params.get('organization_id')
        if not organization_id:
            return Response(
                {"error": "organization_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        locations = Location.objects.filter(organization_id=organization_id)
        serializer = self.get_serializer(locations, many=True)
        return Response(serializer.data)
