from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.db.models import Q
from leave_app.models.leave_request import LeaveRequest, LeaveRequestStatus
from leave_app.models.leave_balance import LeaveBalance
from leave_app.models.leave_approval import LeaveApproval, ApprovalStatus
from leave_app.serializers.leave_request_serializer import LeaveRequestSerializer, LeaveRequestDetailSerializer
from leave_app.filters.leave_filters import LeaveRequestFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class LeaveRequestViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Leave Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for managing leave requests
    
    list:
    Return a list of all leave requests
    
    create:
    Create a new leave request
    
    retrieve:
    Return the given leave request
    
    update:
    Update the given leave request
    
    partial_update:
    Partially update the given leave request
    
    destroy:
    Delete the given leave request
    
    Additional actions:
    - employee_requests: Get leave requests for a specific employee
    - pending_requests: Get all pending leave requests
    - approved_requests: Get all approved leave requests
    - rejected_requests: Get all rejected leave requests
    - cancelled_requests: Get all cancelled leave requests
    - approve_request: Approve a leave request
    - reject_request: Reject a leave request
    - cancel_request: Cancel a leave request
    - date_range_requests: Get leave requests within a date range
    """
    queryset = LeaveRequest.objects.all()
    serializer_class = LeaveRequestSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LeaveRequestFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'reason']
    ordering_fields = ['start_date', 'end_date', 'created_at', 'status']
    ordering = ['-start_date']
    
    def get_serializer_class(self):
        """
        Return different serializers based on the action
        """
        if self.action == 'retrieve':
            return LeaveRequestDetailSerializer
        return LeaveRequestSerializer
    
    def perform_create(self, serializer):
        """
        Create a leave request and update leave balance
        """
        leave_request = serializer.save()
        
        # Create approval record if needed
        if leave_request.leave_type.requires_approval:
            # Get the employee's manager as the approver
            manager = None
            if leave_request.employee.department and leave_request.employee.department.manager:
                manager = leave_request.employee.department.manager
            
            if manager:
                LeaveApproval.objects.create(
                    leave_request=leave_request,
                    approver=manager,
                    level=1,
                    status=ApprovalStatus.PENDING.value
                )
    
    @action(detail=False, methods=['get'], url_path='employee/(?P<employee_id>[^/.]+)')
    def employee_requests(self, request, employee_id=None):
        """
        Get leave requests for a specific employee
        """
        requests = self.queryset.filter(employee_id=employee_id)
        page = self.paginate_queryset(requests)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def pending_requests(self, request):
        """
        Get all pending leave requests
        """
        requests = self.queryset.filter(status=LeaveRequestStatus.PENDING.value)
        page = self.paginate_queryset(requests)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def approved_requests(self, request):
        """
        Get all approved leave requests
        """
        requests = self.queryset.filter(status=LeaveRequestStatus.APPROVED.value)
        page = self.paginate_queryset(requests)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def rejected_requests(self, request):
        """
        Get all rejected leave requests
        """
        requests = self.queryset.filter(status=LeaveRequestStatus.REJECTED.value)
        page = self.paginate_queryset(requests)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def cancelled_requests(self, request):
        """
        Get all cancelled leave requests
        """
        requests = self.queryset.filter(status=LeaveRequestStatus.CANCELLED.value)
        page = self.paginate_queryset(requests)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def approve_request(self, request, pk=None):
        """
        Approve a leave request
        """
        leave_request = self.get_object()
        
        if leave_request.status != LeaveRequestStatus.PENDING.value:
            return Response(
                {"detail": "Only pending leave requests can be approved"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update leave request status
        leave_request.status = LeaveRequestStatus.APPROVED.value
        leave_request.approved_by = request.user.employee
        leave_request.approval_date = timezone.now()
        leave_request.save()
        
        # Update leave balance
        try:
            current_year = leave_request.start_date.year
            leave_balance = LeaveBalance.objects.get(
                employee=leave_request.employee,
                leave_type=leave_request.leave_type,
                year=current_year
            )
            
            # Update used days
            leave_balance.used += leave_request.total_days
            leave_balance.save()
            
        except LeaveBalance.DoesNotExist:
            # If no balance exists, create one
            LeaveBalance.objects.create(
                employee=leave_request.employee,
                leave_type=leave_request.leave_type,
                year=current_year,
                used=leave_request.total_days
            )
        
        # Update approval record if exists
        approvals = leave_request.approvals.filter(approver=request.user.employee)
        if approvals.exists():
            approval = approvals.first()
            approval.status = ApprovalStatus.APPROVED.value
            approval.approval_date = timezone.now()
            approval.save()
        
        serializer = self.get_serializer(leave_request)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reject_request(self, request, pk=None):
        """
        Reject a leave request
        """
        leave_request = self.get_object()
        
        if leave_request.status != LeaveRequestStatus.PENDING.value:
            return Response(
                {"detail": "Only pending leave requests can be rejected"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get rejection reason from request data
        rejection_reason = request.data.get('rejection_reason', '')
        
        # Update leave request status
        leave_request.status = LeaveRequestStatus.REJECTED.value
        leave_request.approved_by = request.user.employee
        leave_request.approval_date = timezone.now()
        leave_request.rejection_reason = rejection_reason
        leave_request.save()
        
        # Update approval record if exists
        approvals = leave_request.approvals.filter(approver=request.user.employee)
        if approvals.exists():
            approval = approvals.first()
            approval.status = ApprovalStatus.REJECTED.value
            approval.comments = rejection_reason
            approval.approval_date = timezone.now()
            approval.save()
        
        serializer = self.get_serializer(leave_request)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel_request(self, request, pk=None):
        """
        Cancel a leave request
        """
        leave_request = self.get_object()
        
        if leave_request.status == LeaveRequestStatus.CANCELLED.value:
            return Response(
                {"detail": "This leave request is already cancelled"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # If the leave request was approved, update the leave balance
        if leave_request.status == LeaveRequestStatus.APPROVED.value:
            try:
                current_year = leave_request.start_date.year
                leave_balance = LeaveBalance.objects.get(
                    employee=leave_request.employee,
                    leave_type=leave_request.leave_type,
                    year=current_year
                )
                
                # Restore used days
                leave_balance.used -= leave_request.total_days
                leave_balance.save()
                
            except LeaveBalance.DoesNotExist:
                pass
        
        # Update leave request status
        leave_request.status = LeaveRequestStatus.CANCELLED.value
        leave_request.save()
        
        serializer = self.get_serializer(leave_request)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def date_range_requests(self, request):
        """
        Get leave requests within a date range
        """
        start_date = request.query_params.get('start_date', None)
        end_date = request.query_params.get('end_date', None)
        
        if not start_date or not end_date:
            return Response(
                {"detail": "Both start_date and end_date are required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Parse dates
            start_date = timezone.datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = timezone.datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"detail": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get leaves that overlap with the date range
        requests = self.queryset.filter(
            # Leave starts within the range or before the range and ends within or after the range
            (
                (Q(start_date__gte=start_date) & Q(start_date__lte=end_date)) |
                (Q(end_date__gte=start_date) & Q(end_date__lte=end_date)) |
                (Q(start_date__lte=start_date) & Q(end_date__gte=end_date))
            )
        )
        
        page = self.paginate_queryset(requests)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(requests, many=True)
        return Response(serializer.data)
