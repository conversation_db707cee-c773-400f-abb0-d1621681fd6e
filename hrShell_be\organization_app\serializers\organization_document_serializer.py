from rest_framework import serializers
from organization_app.models.organization_document import OrganizationDocument, DocumentType


class OrganizationDocumentSerializer(serializers.ModelSerializer):
    """
    Serializer for the OrganizationDocument model
    """
    organization_name = serializers.ReadOnlyField(source='organization.name')
    document_type_display = serializers.ReadOnlyField(source='get_document_type_display')
    business_unit_name = serializers.ReadOnlyField(source='business_unit.name')
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = OrganizationDocument
        fields = [
            'id', 'title', 'organization', 'organization_name', 'description',
            'document_type', 'document_type_display', 'file', 'file_url',
            'version', 'effective_date', 'expiry_date', 'business_unit',
            'business_unit_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'file_url']
    
    def get_file_url(self, obj):
        request = self.context.get('request')
        if obj.file and request:
            return request.build_absolute_uri(obj.file.url)
        return None
    
    def validate_document_type(self, value):
        """
        Validate that the document type is one of the allowed choices
        """
        try:
            DocumentType(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError(
                f"Document type must be one of: {', '.join([dt.value for dt in DocumentType])}"
            )
    
    def validate(self, data):
        """
        Validate effective and expiry dates
        """
        effective_date = data.get('effective_date')
        expiry_date = data.get('expiry_date')
        
        if effective_date and expiry_date and effective_date > expiry_date:
            raise serializers.ValidationError(
                {"expiry_date": "Expiry date must be after effective date."}
            )
        
        return data
