# Contributing to the HR Management API

This guide explains how to contribute to the HR Management API project.

## Getting Started

### Prerequisites

Before you start contributing, make sure you have:

- Python 3.8 or higher
- PostgreSQL 12 or higher
- Git

### Setting Up the Development Environment

1. Fork the repository on GitHub.
2. Clone your fork:
   ```bash
   git clone https://github.com/yourusername/hrShell_be.git
   cd hrShell_be
   ```
3. Create a virtual environment:
   ```bash
   python -m venv hrShellEnv
   ```
4. Activate the virtual environment:
   - Windows: `hrShellEnv\Scripts\activate`
   - macOS/Linux: `source hrShellEnv/bin/activate`
5. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
6. Set up the database:
   ```bash
   createdb shell_dwh
   python manage.py migrate
   ```
7. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```
8. Run the development server:
   ```bash
   python manage.py runserver
   ```

## Development Workflow

### Creating a Branch

Create a new branch for your feature or bugfix:

```bash
git checkout -b feature/your-feature-name
```

or

```bash
git checkout -b bugfix/your-bugfix-name
```

### Making Changes

1. Make your changes to the codebase.
2. Run the tests to make sure everything works:
   ```bash
   python manage.py test
   ```
3. Format your code:
   ```bash
   black .
   ```
4. Check for linting errors:
   ```bash
   flake8
   ```

### Committing Changes

1. Stage your changes:
   ```bash
   git add .
   ```
2. Commit your changes:
   ```bash
   git commit -m "Your commit message"
   ```
3. Push your changes to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

### Creating a Pull Request

1. Go to the original repository on GitHub.
2. Click on "Pull Requests" and then "New Pull Request".
3. Select your fork and branch.
4. Fill out the pull request form.
5. Submit the pull request.

## Code Style

### Python Code Style

The HR Management API follows the [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide for Python code. We use [Black](https://black.readthedocs.io/en/stable/) for code formatting and [Flake8](https://flake8.pycqa.org/en/latest/) for linting.

### Docstrings

Use [Google-style docstrings](https://google.github.io/styleguide/pyguide.html#38-comments-and-docstrings) for functions and classes:

```python
def function_with_types_in_docstring(param1, param2):
    """Example function with types documented in the docstring.

    Args:
        param1 (int): The first parameter.
        param2 (str): The second parameter.

    Returns:
        bool: The return value. True for success, False otherwise.
    """
    return True
```

### Imports

Organize imports in the following order:

1. Standard library imports
2. Related third-party imports
3. Local application/library-specific imports

Separate each group of imports with a blank line.

## Testing

### Running Tests

Run the tests with:

```bash
python manage.py test
```

### Writing Tests

Write tests for all new features and bugfixes. We use Django's built-in testing framework.

Example test:

```python
from django.test import TestCase
from employees_app.models import Employee, Department

class EmployeeModelTest(TestCase):
    def setUp(self):
        self.department = Department.objects.create(name="Engineering")
        self.employee = Employee.objects.create(
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            position="Software Engineer",
            department=self.department
        )

    def test_full_name(self):
        self.assertEqual(self.employee.full_name, "John Doe")
```

## Documentation

### API Documentation

Document all API endpoints using Markdown files in the `docs/api/` directory.

### Code Documentation

Document all functions, classes, and methods using docstrings.

## Versioning

The HR Management API uses [Semantic Versioning](https://semver.org/). The version number is in the format `MAJOR.MINOR.PATCH`:

- `MAJOR`: Incompatible API changes
- `MINOR`: Backwards-compatible functionality
- `PATCH`: Backwards-compatible bug fixes

## Release Process

1. Update the version number in `setup.py` and `docs/conf.py`.
2. Update the changelog.
3. Create a new release on GitHub.
4. Tag the release with the version number.
5. Publish the release.

## Getting Help

If you need help with contributing to the HR Management API, you can:

- Open an issue on GitHub
- Contact the maintainers
- Join the community chat

## Code of Conduct

Please follow our [Code of Conduct](CODE_OF_CONDUCT.md) when contributing to the project.

## License

By contributing to the HR Management API, you agree that your contributions will be licensed under the project's license.
