from django.db import models
from organization_app.models.organization import Organization


class SalaryComponent(models.Model):
    """
    Model to define salary components like Basic, HRA, Allowances, etc.
    """
    COMPONENT_TYPE_CHOICES = [
        ('earning', 'Earning'),
        ('deduction', 'Deduction'),
    ]
    
    CALCULATION_TYPE_CHOICES = [
        ('fixed', 'Fixed Amount'),
        ('percentage', 'Percentage of Basic'),
        ('percentage_gross', 'Percentage of Gross'),
        ('formula', 'Custom Formula'),
    ]
    
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    component_type = models.CharField(max_length=20, choices=COMPONENT_TYPE_CHOICES)
    calculation_type = models.CharField(max_length=20, choices=CALCULATION_TYPE_CHOICES)
    value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    formula = models.TextField(blank=True, null=True, help_text="Custom formula for calculation")
    is_taxable = models.<PERSON><PERSON>anField(default=True)
    is_fixed = models.BooleanField(default=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='salary_components')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['component_type', 'name']
        verbose_name = 'Salary Component'
        verbose_name_plural = 'Salary Components'
    
    def __str__(self):
        return f"{self.name} ({self.code})"


class SalaryStructure(models.Model):
    """
    Model to define salary structures that can be assigned to employees.
    """
    SALARY_TYPE_CHOICES = [
        ('monthly', 'Monthly'),
        ('hourly', 'Hourly'),
        ('daily', 'Daily'),
    ]
    
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='salary_structures')
    salary_type = models.CharField(max_length=20, choices=SALARY_TYPE_CHOICES, default='monthly')
    components = models.ManyToManyField(SalaryComponent, related_name='salary_structures')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = 'Salary Structure'
        verbose_name_plural = 'Salary Structures'
    
    def __str__(self):
        return f"{self.name} ({self.code})"
