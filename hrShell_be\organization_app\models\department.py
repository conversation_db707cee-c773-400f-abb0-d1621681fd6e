from django.db import models
from .organization import Organization
from .business_unit import BusinessUnit


class DepartmentStatus(models.TextChoices):
    ACTIVE = 'active', 'Active'
    INACTIVE = 'inactive', 'Inactive'


class Department(models.Model):
    """
    Model to represent departments within an organization.
    """
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='departments')
    business_unit = models.ForeignKey(BusinessUnit, on_delete=models.CASCADE, related_name='departments', null=True, blank=True)
    description = models.TextField(blank=True, null=True)
    status = models.Char<PERSON>ield(max_length=20, choices=DepartmentStatus.choices, default=DepartmentStatus.ACTIVE)
    parent_department = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='child_departments')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.name
