from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from onboarding_offboarding_app.views.onboarding_views import OnboardingPlanViewSet, OnboardingTaskViewSet
from onboarding_offboarding_app.views.offboarding_views import OffboardingRequestViewSet, OffboardingTaskViewSet
from onboarding_offboarding_app.views.document_views import DocumentViewSet, DocumentTemplateViewSet
from onboarding_offboarding_app.views.feedback_views import ExitFeedbackViewSet

app_name = 'onboarding_offboarding'

router = DefaultRouter()
router.register(r'onboarding-plans', OnboardingPlanViewSet, basename='onboardingplan')
router.register(r'onboarding-tasks', OnboardingTaskViewSet, basename='onboardingtask')
router.register(r'offboarding-requests', OffboardingRequestViewSet, basename='offboardingrequest')
router.register(r'offboarding-tasks', OffboardingTaskViewSet, basename='offboardingtask')
router.register(r'documents', DocumentViewSet, basename='document')
router.register(r'document-templates', DocumentTemplateViewSet, basename='documenttemplate')
router.register(r'exit-feedback', ExitFeedbackViewSet, basename='exitfeedback')

# Custom URLs for nested resources
onboarding_plan_urls = [
    path('status/<str:status_value>/', OnboardingPlanViewSet.as_view({'get': 'status'}), name='status-onboarding-plans'),
    path('employee/<int:employee_pk>/', OnboardingPlanViewSet.as_view({'get': 'employee'}), name='employee-onboarding-plan'),
    path('department/<int:department_pk>/', OnboardingPlanViewSet.as_view({'get': 'department'}), name='department-onboarding-plans'),
    path('mentor/<int:mentor_pk>/', OnboardingPlanViewSet.as_view({'get': 'mentor'}), name='mentor-onboarding-plans'),
]

onboarding_task_urls = [
    path('status/<str:status_value>/', OnboardingTaskViewSet.as_view({'get': 'status'}), name='status-onboarding-tasks'),
    path('plan/<int:plan_pk>/', OnboardingTaskViewSet.as_view({'get': 'plan'}), name='plan-onboarding-tasks'),
    path('category/<str:category>/', OnboardingTaskViewSet.as_view({'get': 'category'}), name='category-onboarding-tasks'),
    path('assigned-to/<int:employee_pk>/', OnboardingTaskViewSet.as_view({'get': 'assigned_to'}), name='assigned-to-onboarding-tasks'),
]

offboarding_request_urls = [
    path('status/<str:status_value>/', OffboardingRequestViewSet.as_view({'get': 'status'}), name='status-offboarding-requests'),
    path('employee/<int:employee_pk>/', OffboardingRequestViewSet.as_view({'get': 'employee'}), name='employee-offboarding-requests'),
    path('department/<int:department_pk>/', OffboardingRequestViewSet.as_view({'get': 'department'}), name='department-offboarding-requests'),
    path('manager/<int:manager_pk>/', OffboardingRequestViewSet.as_view({'get': 'manager'}), name='manager-offboarding-requests'),
]

offboarding_task_urls = [
    path('status/<str:status_value>/', OffboardingTaskViewSet.as_view({'get': 'status'}), name='status-offboarding-tasks'),
    path('request/<int:request_pk>/', OffboardingTaskViewSet.as_view({'get': 'request'}), name='request-offboarding-tasks'),
    path('category/<str:category>/', OffboardingTaskViewSet.as_view({'get': 'category'}), name='category-offboarding-tasks'),
    path('assigned-to/<int:employee_pk>/', OffboardingTaskViewSet.as_view({'get': 'assigned_to'}), name='assigned-to-offboarding-tasks'),
]

document_urls = [
    path('employee/<int:employee_pk>/', DocumentViewSet.as_view({'get': 'employee'}), name='employee-documents'),
    path('document-type/<str:document_type>/', DocumentViewSet.as_view({'get': 'document_type'}), name='document-type-documents'),
    path('status/<str:status_value>/', DocumentViewSet.as_view({'get': 'status'}), name='status-documents'),
    path('template/<int:template_pk>/', DocumentViewSet.as_view({'get': 'template'}), name='template-documents'),
]

document_template_urls = [
    path('organization/<int:organization_pk>/', DocumentTemplateViewSet.as_view({'get': 'organization'}), name='organization-document-templates'),
    path('document-type/<str:document_type>/', DocumentTemplateViewSet.as_view({'get': 'document_type'}), name='document-type-document-templates'),
    path('active/', DocumentTemplateViewSet.as_view({'get': 'active'}), name='active-document-templates'),
]

exit_feedback_urls = [
    path('employee/<int:employee_pk>/', ExitFeedbackViewSet.as_view({'get': 'employee'}), name='employee-exit-feedback'),
    path('offboarding-request/<int:offboarding_request_pk>/', ExitFeedbackViewSet.as_view({'get': 'offboarding_request'}), name='offboarding-request-exit-feedback'),
    path('conducted-by/<int:employee_pk>/', ExitFeedbackViewSet.as_view({'get': 'conducted_by'}), name='conducted-by-exit-feedback'),
    path('ratings-summary/', ExitFeedbackViewSet.as_view({'get': 'ratings_summary'}), name='ratings-summary-exit-feedback'),
]

urlpatterns = [
    path('', include(router.urls)),
    path('onboarding-plans/', include(onboarding_plan_urls)),
    path('onboarding-tasks/', include(onboarding_task_urls)),
    path('offboarding-requests/', include(offboarding_request_urls)),
    path('offboarding-tasks/', include(offboarding_task_urls)),
    path('documents/', include(document_urls)),
    path('document-templates/', include(document_template_urls)),
    path('exit-feedback/', include(exit_feedback_urls)),
]
