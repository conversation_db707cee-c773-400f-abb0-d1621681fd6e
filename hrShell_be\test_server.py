#!/usr/bin/env python3
"""
Test script to check if Django server can start properly.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hrShell_be.settings')

try:
    django.setup()
    print("✅ Django setup successful")
    
    # Test imports
    from django.conf import settings
    print(f"✅ Settings loaded: DEBUG={settings.DEBUG}")
    
    # Test CORS settings
    print(f"✅ CORS_ALLOW_ALL_ORIGINS: {getattr(settings, 'CORS_ALLOW_ALL_ORIGINS', 'Not set')}")
    print(f"✅ CORS_ALLOWED_ORIGINS: {getattr(settings, 'CORS_ALLOWED_ORIGINS', 'Not set')}")
    
    # Test database connection
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        print(f"✅ Database connection successful: {result}")
    
    print("\n🚀 All checks passed! Server should start successfully.")
    print("Run: python manage.py runserver")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
