"""
Health check view for the API.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi


@swagger_auto_schema(
    tags=['System'],
    operation_description="healthcheck management endpoints"
)
class HealthCheckView(APIView):
    """
    API endpoint for health check.
    """
    permission_classes = []

    @swagger_auto_schema(
        operation_description="Check if the API is up and running",
        operation_summary="Health Check",
        responses={
            200: openapi.Response(
                description="API is healthy",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'status': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example='ok'
                        )
                    }
                )
            )
        },
        tags=['System']
    )
    def get(self, request, format=None):
        return Response({"status": "ok"}, status=status.HTTP_200_OK)
