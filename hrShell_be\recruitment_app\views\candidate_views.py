from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from recruitment_app.models.candidate import Candidate, CandidateSkill, CandidateEducation, CandidateExperience
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from recruitment_app.serializers.candidate_serializer import (
    CandidateSerializer, CandidateSkillSerializer, CandidateEducationSerializer, CandidateExperienceSerializer
)
from recruitment_app.filters.recruitment_filters import CandidateFilter


class CandidateViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for candidates.
    """
    queryset = Candidate.objects.all()
    serializer_class = CandidateSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = CandidateFilter
    search_fields = ['first_name', 'last_name', 'email', 'phone', 'headline', 'summary', 'current_employer', 'current_designation', 'tags']
    ordering_fields = ['first_name', 'last_name', 'created_at', 'total_experience', 'rating']
    ordering = ['-created_at']

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all candidates with a specific status.
        """
        candidates = Candidate.objects.filter(status=status_value)
        page = self.paginate_queryset(candidates)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(candidates, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all candidates for a specific organization.
        """
        candidates = Candidate.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(candidates)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(candidates, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def source(self, request, source_pk=None):
        """
        Get all candidates from a specific source.
        """
        candidates = Candidate.objects.filter(source_id=source_pk)
        page = self.paginate_queryset(candidates)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(candidates, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def skill(self, request, skill_pk=None):
        """
        Get all candidates with a specific skill.
        """
        candidates = Candidate.objects.filter(skills__id=skill_pk)
        page = self.paginate_queryset(candidates)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(candidates, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def skills(self, request, pk=None):
        """
        Get all skills for a specific candidate.
        """
        candidate = self.get_object()
        skills = CandidateSkill.objects.filter(candidate=candidate)

        serializer = CandidateSkillSerializer(skills, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_skill(self, request, pk=None):
        """
        Add a skill to a candidate.
        """
        candidate = self.get_object()

        # Validate required fields
        required_fields = ['skill', 'proficiency']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {"detail": f"Field '{field}' is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Check if skill already exists
        skill_id = request.data.get('skill')
        if CandidateSkill.objects.filter(candidate=candidate, skill_id=skill_id).exists():
            return Response(
                {"detail": "Skill already exists for this candidate."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create skill
        skill_data = {
            'candidate': candidate.id,
            'skill': skill_id,
            'proficiency': request.data.get('proficiency'),
            'years_of_experience': request.data.get('years_of_experience'),
            'is_primary': request.data.get('is_primary', False)
        }

        serializer = CandidateSkillSerializer(data=skill_data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def education(self, request, pk=None):
        """
        Get all education records for a specific candidate.
        """
        candidate = self.get_object()
        education = CandidateEducation.objects.filter(candidate=candidate)

        serializer = CandidateEducationSerializer(education, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_education(self, request, pk=None):
        """
        Add an education record to a candidate.
        """
        candidate = self.get_object()

        # Validate required fields
        required_fields = ['institution', 'degree', 'field_of_study', 'start_date']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {"detail": f"Field '{field}' is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Create education record
        education_data = {
            'candidate': candidate.id,
            'institution': request.data.get('institution'),
            'degree': request.data.get('degree'),
            'field_of_study': request.data.get('field_of_study'),
            'start_date': request.data.get('start_date'),
            'end_date': request.data.get('end_date'),
            'is_current': request.data.get('is_current', False),
            'grade': request.data.get('grade'),
            'description': request.data.get('description')
        }

        serializer = CandidateEducationSerializer(data=education_data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def experience(self, request, pk=None):
        """
        Get all experience records for a specific candidate.
        """
        candidate = self.get_object()
        experience = CandidateExperience.objects.filter(candidate=candidate)

        serializer = CandidateExperienceSerializer(experience, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_experience(self, request, pk=None):
        """
        Add an experience record to a candidate.
        """
        candidate = self.get_object()

        # Validate required fields
        required_fields = ['company', 'title', 'start_date']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {"detail": f"Field '{field}' is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Create experience record
        experience_data = {
            'candidate': candidate.id,
            'company': request.data.get('company'),
            'title': request.data.get('title'),
            'location': request.data.get('location'),
            'start_date': request.data.get('start_date'),
            'end_date': request.data.get('end_date'),
            'is_current': request.data.get('is_current', False),
            'description': request.data.get('description')
        }

        serializer = CandidateExperienceSerializer(data=experience_data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def blacklist(self, request, pk=None):
        """
        Blacklist a candidate.
        """
        candidate = self.get_object()

        if candidate.status == 'blacklisted':
            return Response(
                {"detail": "Candidate is already blacklisted."},
                status=status.HTTP_400_BAD_REQUEST
            )

        candidate.status = 'blacklisted'
        candidate.save()

        serializer = self.get_serializer(candidate)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """
        Activate a candidate.
        """
        candidate = self.get_object()

        if candidate.status == 'active':
            return Response(
                {"detail": "Candidate is already active."},
                status=status.HTTP_400_BAD_REQUEST
            )

        candidate.status = 'active'
        candidate.save()

        serializer = self.get_serializer(candidate)
        return Response(serializer.data)
