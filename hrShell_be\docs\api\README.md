# API Reference

This section provides detailed information about the API endpoints available in the HR Management API.

## Authentication

The API uses JWT (JSON Web Token) authentication. To authenticate, you need to obtain a token by sending a POST request to the `/api/token/` endpoint with your username and password.

### Obtaining a Token

```http
POST /api/token/
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

Response:

```json
{
  "access": "your_access_token",
  "refresh": "your_refresh_token"
}
```

### Using the Token

Include the token in the Authorization header of your requests:

```http
GET /api/v1/employees/
Authorization: Bearer your_access_token
```

### Refreshing the Token

To refresh an expired token, send a POST request to the `/api/token/refresh/` endpoint with your refresh token:

```http
POST /api/token/refresh/
Content-Type: application/json

{
  "refresh": "your_refresh_token"
}
```

Response:

```json
{
  "access": "new_access_token"
}
```

## Endpoints

The API provides the following endpoints:

- [Employees](employees.md)
- [Departments](departments.md)
- [Employee Hikes](employee_hikes.md)

## Common Parameters

### Pagination

All list endpoints support pagination with the following query parameters:

- `page`: The page number (default: 1)
- `page_size`: The number of items per page (default: 10, max: 100)

Example:

```http
GET /api/v1/employees/?page=2&page_size=20
```

### Filtering

Most endpoints support filtering with query parameters. The available filters depend on the endpoint.

Example:

```http
GET /api/v1/employees/?status=active&department=1
```

### Searching

Most endpoints support searching with the `search` query parameter.

Example:

```http
GET /api/v1/employees/?search=john
```

### Ordering

Most endpoints support ordering with the `ordering` query parameter.

Example:

```http
GET /api/v1/employees/?ordering=-hire_date
```

Use a minus sign (`-`) to indicate descending order.

## Error Handling

The API returns appropriate HTTP status codes and error messages in case of errors.

Example error response:

```json
{
  "detail": "Authentication credentials were not provided."
}
```

Common error status codes:

- `400 Bad Request`: The request was invalid.
- `401 Unauthorized`: Authentication is required.
- `403 Forbidden`: The authenticated user does not have permission to access the requested resource.
- `404 Not Found`: The requested resource was not found.
- `500 Internal Server Error`: An error occurred on the server.
