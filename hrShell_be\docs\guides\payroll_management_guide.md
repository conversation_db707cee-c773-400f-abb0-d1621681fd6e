# Payroll Management Guide

This guide provides a comprehensive overview of the Payroll Management Module, including its features, workflow, API usage examples, and best practices.

## Table of Contents

1. [Introduction](#introduction)
2. [Features](#features)
3. [Workflow](#workflow)
4. [API Usage Examples](#api-usage-examples)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)
7. [FAQ](#faq)

## Introduction

The Payroll Management Module automates the salary calculation, deductions, statutory compliance, payslip generation, and disbursement processes. It ensures timely and accurate salary processing based on attendance, leaves, taxes, and benefits.

## Features

### 1. Salary Structure Definition
- Set up components:
  - Earnings: Basic, HRA, Allowances, Bonus, Overtime
  - Deductions: Provident Fund, ESI, TDS, Loan EMI, Professional Tax
- Support for:
  - Fixed salary
  - Variable pay (performance-based)
  - Hourly wage

### 2. Employee Salary Assignment
- Link salary structure per employee
- Effective from date
- Revision history (salary hikes)

### 3. Attendance & Leave Integration
- Calculate pay based on:
  - Working days
  - LOP (Leave Without Pay)
  - Overtime hours
- Pull data from Attendance/Leave module

### 4. Payroll Processing
- Monthly payroll run
- Preview salary sheet before finalization
- Auto-calculation:
  - Gross Pay
  - Total Deductions
  - Net Pay

### 5. Payslip Generation
- Auto-generate PDF payslips for all employees
- Includes salary breakup, deductions, and net payable
- Download or email payslips to employees

### 6. Salary Disbursement
- Generate bank transfer statements (CSV, NEFT format)
- Integration with accounting software (like Tally, QuickBooks)

### 7. Loans & Advances
- Track company-issued loans
- EMI deduction schedules
- Interest calculation (if applicable)

### 8. Bonus & Incentive Management
- Handle one-time payments, bonuses, performance incentives

### 9. Full & Final Settlement (FnF)
- On resignation/termination:
  - Pro-rata salary
  - Leave encashment
  - Gratuity, notice period pay
  - Deductions

### 10. Reports & Analytics
- Payroll summary report (monthly/annually)
- Payslip register
- Deduction breakdown (PF, ESI, TDS)
- Bank statement for salary disbursement
- Audit trail of salary changes

## Workflow

### 1. Setup Phase

#### 1.1 Define Salary Components
```python
# Create basic salary component
basic_pay = SalaryComponent.objects.create(
    name="Basic Pay",
    code="BASIC",
    description="Basic salary component",
    component_type="earning",
    calculation_type="fixed",
    value=0.00,  # Will be set per employee
    is_taxable=True,
    is_fixed=True,
    organization=organization
)

# Create HRA component
hra = SalaryComponent.objects.create(
    name="House Rent Allowance",
    code="HRA",
    description="House rent allowance",
    component_type="earning",
    calculation_type="percentage",
    value=40.00,  # 40% of Basic
    is_taxable=False,
    is_fixed=True,
    organization=organization
)
```

#### 1.2 Define Salary Structure
```python
# Create standard salary structure
standard_structure = SalaryStructure.objects.create(
    name="Standard Salary Structure",
    code="STD",
    description="Standard salary structure for employees",
    organization=organization,
    salary_type="monthly"
)

# Add components to the structure
standard_structure.components.add(basic_pay, hra)
```

#### 1.3 Assign Salary to Employees
```python
# Assign salary to employee
employee_salary = EmployeeSalary.objects.create(
    employee=employee,
    salary_structure=standard_structure,
    effective_from=date.today(),
    basic_salary=50000.00,
    gross_salary=80000.00,
    net_salary=70000.00,
    is_active=True
)

# Add component values
EmployeeSalaryComponent.objects.create(
    employee_salary=employee_salary,
    salary_component=basic_pay,
    value=50000.00,
    is_active=True
)

EmployeeSalaryComponent.objects.create(
    employee_salary=employee_salary,
    salary_component=hra,
    value=20000.00,
    is_active=True
)
```

### 2. Monthly Payroll Process

#### 2.1 Create Payroll
```python
# Create payroll for the month
payroll = Payroll.objects.create(
    name="April 2023 Payroll",
    organization=organization,
    start_date=date(2023, 4, 1),
    end_date=date(2023, 4, 30),
    payment_date=date(2023, 5, 1),
    status="draft",
    created_by=admin_employee
)
```

#### 2.2 Generate Payroll Items
```python
# Generate payroll items for all active employees
for employee in Employee.objects.filter(status='active'):
    try:
        salary = EmployeeSalary.objects.filter(
            employee=employee,
            is_active=True
        ).latest('effective_from')
        
        # Calculate working days, leave days, etc.
        working_days, leave_days, lop_days = calculate_attendance(
            employee, payroll.start_date, payroll.end_date
        )
        
        # Create payroll item
        payroll_item = PayrollItem.objects.create(
            payroll=payroll,
            employee=employee,
            working_days=working_days,
            leave_days=leave_days,
            lop_days=lop_days,
            basic_salary=salary.basic_salary,
            gross_earnings=salary.gross_salary,
            net_payable=salary.net_salary,
            status="draft"
        )
        
        # Create payroll item components
        for salary_component in salary.components.all():
            component = salary_component.salary_component
            
            PayrollItemComponent.objects.create(
                payroll_item=payroll_item,
                name=component.name,
                component_type=component.component_type,
                amount=salary_component.value,
                is_taxable=component.is_taxable
            )
    except EmployeeSalary.DoesNotExist:
        continue
```

#### 2.3 Calculate Payroll Items
```python
# Calculate each payroll item
for item in payroll.items.all():
    # Calculate gross earnings
    earnings = item.components.filter(component_type='earning')
    gross_earnings = sum(earning.amount for earning in earnings)
    
    # Calculate total deductions
    deductions = item.components.filter(component_type='deduction')
    total_deductions = sum(deduction.amount for deduction in deductions)
    
    # Calculate net payable
    net_payable = gross_earnings - total_deductions
    
    # Update the payroll item
    item.gross_earnings = gross_earnings
    item.total_deductions = total_deductions
    item.net_payable = net_payable
    item.status = 'calculated'
    item.save()
```

#### 2.4 Finalize Payroll
```python
# Update payroll status
payroll.status = 'completed'
payroll.save()

# Update all payroll items to approved status
payroll.items.all().update(status='approved')
```

#### 2.5 Generate Payslips
```python
# Generate payslips for all approved payroll items
for item in payroll.items.filter(status='approved'):
    # Generate a unique payslip number
    payslip_number = f"PS-{payroll.payment_date.strftime('%Y%m')}-{item.employee.id:04d}"
    
    # Create the payslip
    Payslip.objects.create(
        payroll_item=item,
        employee=item.employee,
        payslip_number=payslip_number,
        month=payroll.payment_date.month,
        year=payroll.payment_date.year,
        generation_date=date.today(),
        basic_salary=item.basic_salary,
        gross_earnings=item.gross_earnings,
        total_deductions=item.total_deductions,
        net_payable=item.net_payable,
        status='draft'
    )
```

#### 2.6 Generate Bank Transfer
```python
# Create bank transfer
bank_transfer = BankTransfer.objects.create(
    payroll=payroll,
    organization=organization,
    reference_number=f"BT-{payroll.payment_date.strftime('%Y%m')}-0001",
    transfer_date=payroll.payment_date,
    bank_name="ACME Bank",
    account_number="**********",
    transfer_type="salary",
    total_amount=payroll.net_payable,
    total_employees=payroll.items.count(),
    file_format="csv",
    status="draft"
)
```

## API Usage Examples

### 1. Create Salary Structure

```http
POST /api/v1/salary-structures/
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Standard Salary Structure",
  "code": "STD",
  "description": "Standard salary structure for employees",
  "organization": 1,
  "salary_type": "monthly",
  "component_ids": [1, 2, 3]
}
```

### 2. Assign Salary to Employee

```http
POST /api/v1/employee-salaries/
Content-Type: application/json
Authorization: Bearer <token>

{
  "employee": 1,
  "salary_structure": 1,
  "effective_from": "2023-05-01",
  "basic_salary": 50000.00,
  "gross_salary": 80000.00,
  "net_salary": 70000.00,
  "is_active": true,
  "component_values": [
    {
      "salary_component": 1,
      "value": 50000.00
    },
    {
      "salary_component": 2,
      "value": 20000.00
    }
  ]
}
```

### 3. Create Payroll

```http
POST /api/v1/payrolls/
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "April 2023 Payroll",
  "organization": 1,
  "start_date": "2023-04-01",
  "end_date": "2023-04-30",
  "payment_date": "2023-05-01",
  "status": "draft",
  "created_by": 1
}
```

### 4. Generate Payroll Items

```http
POST /api/v1/payrolls/1/generate-items/
Content-Type: application/json
Authorization: Bearer <token>
```

### 5. Calculate Payroll

```http
POST /api/v1/payrolls/1/calculate/
Content-Type: application/json
Authorization: Bearer <token>
```

### 6. Finalize Payroll

```http
POST /api/v1/payrolls/1/finalize/
Content-Type: application/json
Authorization: Bearer <token>
```

### 7. Generate Payslips

```http
POST /api/v1/payslips/generate-for-payroll/
Content-Type: application/json
Authorization: Bearer <token>

{
  "payroll_id": 1
}
```

## Best Practices

1. **Regular Backups**: Regularly backup payroll data to prevent data loss
2. **Data Validation**: Validate all data before processing payroll
3. **Testing**: Test payroll calculations with sample data before running actual payroll
4. **Audit Trail**: Maintain an audit trail of all payroll activities
5. **Security**: Implement strict access controls for payroll data
6. **Documentation**: Document all payroll processes and procedures
7. **Compliance**: Stay updated with tax laws and regulations
8. **Training**: Train HR staff on payroll processes and procedures

## Troubleshooting

### Common Issues and Solutions

1. **Incorrect Salary Calculation**
   - Check salary components and their values
   - Verify attendance and leave data
   - Check tax calculations

2. **Missing Payroll Items**
   - Ensure all employees have active salary assignments
   - Check employee status (should be active)

3. **Bank Transfer Failures**
   - Verify bank account details
   - Check file format
   - Ensure sufficient balance in the company account

## FAQ

### 1. How do I set up a new salary structure?
Use the Salary Structure API to create a new structure and add components to it.

### 2. How do I process payroll for a new employee?
Assign a salary structure to the employee and include them in the next payroll run.

### 3. How do I handle salary revisions?
Create a new salary assignment with the revised salary and set the effective date.

### 4. How do I handle tax deductions?
Set up tax slabs and employee tax declarations, and the system will automatically calculate tax deductions.

### 5. How do I handle loans and advances?
Create a loan record for the employee and the system will automatically deduct EMIs from their salary.
