from rest_framework import serializers
from payroll_app.models.employee_salary import Employee<PERSON><PERSON>ry, EmployeeSalaryComponent, SalaryRevision
from payroll_app.models.salary_structure import SalaryComponent
from employees_app.models.employee import Employee


class EmployeeSalaryComponentSerializer(serializers.ModelSerializer):
    salary_component_name = serializers.SerializerMethodField()
    component_type = serializers.SerializerMethodField()

    class Meta:
        model = EmployeeSalaryComponent
        fields = [
            'id', 'employee_salary', 'salary_component', 'salary_component_name',
            'component_type', 'value', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_salary_component_name(self, obj):
        return obj.salary_component.name if obj.salary_component else None

    def get_component_type(self, obj):
        return obj.salary_component.component_type if obj.salary_component else None


class EmployeeSalarySerializer(serializers.ModelSerializer):
    employee_name = serializers.SerializerMethodField()
    salary_structure_name = serializers.SerializerMethodField()
    components = EmployeeSalaryComponentSerializer(many=True, read_only=True)
    component_values = serializers.ListField(
        child=serializers.DictField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = EmployeeSalary
        fields = [
            'id', 'employee', 'employee_name', 'salary_structure', 'salary_structure_name',
            'effective_from', 'effective_to', 'basic_salary', 'gross_salary', 'net_salary',
            'is_active', 'remarks', 'components', 'component_values', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_employee_name(self, obj):
        return f"{obj.employee.first_name} {obj.employee.last_name}" if obj.employee else None

    def get_salary_structure_name(self, obj):
        return obj.salary_structure.name if obj.salary_structure else None

    def create(self, validated_data):
        component_values = validated_data.pop('component_values', [])

        # Create the employee salary
        employee_salary = EmployeeSalary.objects.create(**validated_data)

        # Create the salary components
        self._create_or_update_components(employee_salary, component_values)

        return employee_salary

    def update(self, instance, validated_data):
        component_values = validated_data.pop('component_values', None)

        # Update the employee salary
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()

        # Update the salary components if provided
        if component_values is not None:
            self._create_or_update_components(instance, component_values)

        return instance

    def _create_or_update_components(self, employee_salary, component_values):
        if not component_values:
            return

        # Delete existing components if we're updating with new values
        EmployeeSalaryComponent.objects.filter(employee_salary=employee_salary).delete()

        # Create new components
        for component_data in component_values:
            component_id = component_data.get('salary_component')
            value = component_data.get('value')

            if component_id and value is not None:
                try:
                    salary_component = SalaryComponent.objects.get(id=component_id)
                    EmployeeSalaryComponent.objects.create(
                        employee_salary=employee_salary,
                        salary_component=salary_component,
                        value=value
                    )
                except SalaryComponent.DoesNotExist:
                    pass


class SalaryRevisionSerializer(serializers.ModelSerializer):
    employee_name = serializers.SerializerMethodField()
    previous_salary_details = serializers.SerializerMethodField()
    new_salary_details = serializers.SerializerMethodField()
    approved_by_name = serializers.SerializerMethodField()

    class Meta:
        model = SalaryRevision
        fields = [
            'id', 'employee', 'employee_name', 'previous_salary', 'previous_salary_details',
            'new_salary', 'new_salary_details', 'revision_date', 'revision_type',
            'percentage_increase', 'amount_increase', 'approved_by', 'approved_by_name',
            'approval_date', 'remarks', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_employee_name(self, obj):
        return f"{obj.employee.first_name} {obj.employee.last_name}" if obj.employee else None

    def get_previous_salary_details(self, obj):
        if not obj.previous_salary:
            return None

        return {
            'basic_salary': obj.previous_salary.basic_salary,
            'gross_salary': obj.previous_salary.gross_salary,
            'net_salary': obj.previous_salary.net_salary,
            'effective_from': obj.previous_salary.effective_from
        }

    def get_new_salary_details(self, obj):
        if not obj.new_salary:
            return None

        return {
            'basic_salary': obj.new_salary.basic_salary,
            'gross_salary': obj.new_salary.gross_salary,
            'net_salary': obj.new_salary.net_salary,
            'effective_from': obj.new_salary.effective_from
        }

    def get_approved_by_name(self, obj):
        if not obj.approved_by:
            return None

        return f"{obj.approved_by.first_name} {obj.approved_by.last_name}"
