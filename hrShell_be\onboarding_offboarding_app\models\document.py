from django.db import models
from employees_app.models.employee import Employee
from organization_app.models import Organization


class DocumentTemplate(models.Model):
    """
    Model to represent document templates for onboarding and offboarding.
    """
    TYPE_CHOICES = [
        ('onboarding', 'Onboarding'),
        ('offboarding', 'Offboarding'),
        ('both', 'Both'),
    ]

    # Basic Information
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='document_templates')
    document_type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='both')

    # Template Content
    template_file = models.FileField(upload_to='document_templates/', blank=True, null=True)
    template_content = models.TextField(blank=True, null=True)

    # Status
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'Document Template'
        verbose_name_plural = 'Document Templates'

    def __str__(self):
        return self.name


class Document(models.Model):
    """
    Model to represent documents for onboarding and offboarding.
    """
    TYPE_CHOICES = [
        ('onboarding', 'Onboarding'),
        ('offboarding', 'Offboarding'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('submitted', 'Submitted'),
        ('verified', 'Verified'),
        ('rejected', 'Rejected'),
    ]

    # Basic Information
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='onboarding_documents')
    document_type = models.CharField(max_length=20, choices=TYPE_CHOICES)

    # Template
    template = models.ForeignKey(DocumentTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='documents')

    # Document Content
    document_file = models.FileField(upload_to='employee_documents/%Y/%m/', blank=True, null=True)

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Verification
    verified_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_documents')
    verified_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Document'
        verbose_name_plural = 'Documents'

    def __str__(self):
        return f"{self.name} for {self.employee}"
