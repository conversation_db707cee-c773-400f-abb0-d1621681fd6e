#!/usr/bin/env python3
"""
Script to apply method-level Swagger tags to all ViewSets.

This script adds @swagger_auto_schema decorators to individual CRUD methods
to ensure proper grouping in Swagger UI.
"""

import os
import re
from pathlib import Path

# Tag mapping for ViewSets
VIEWSET_TAGS = {
    # Authentication
    'LoginView': 'Authentication',
    'RegisterView': 'Authentication', 
    'UserInfoView': 'Authentication',
    'LogoutView': 'Authentication',
    'CustomTokenObtainPairView': 'Authentication',
    
    # Employee Management
    'EmployeeViewSet': 'Employee Management',
    'DepartmentViewSet': 'Employee Management',
    'EmployeeDetailViewSet': 'Employee Management',
    'EmployeeLeaveViewSet': 'Employee Management',
    'JobHistoryViewSet': 'Employee Management',
    'SkillOfferingViewSet': 'Employee Management',
    
    # Organization Structure
    'OrganizationViewSet': 'Organization Structure',
    'LocationViewSet': 'Organization Structure',
    'DesignationViewSet': 'Organization Structure',
    'BusinessUnitViewSet': 'Organization Structure',
    'OrganizationPolicyViewSet': 'Organization Structure',
    
    # Leave Management
    'LeaveTypeViewSet': 'Leave Management',
    'LeavePolicyViewSet': 'Leave Management',
    'LeaveRequestViewSet': 'Leave Management',
    'LeaveApprovalViewSet': 'Leave Management',
    'LeaveBalanceViewSet': 'Leave Management',
    'HolidayViewSet': 'Leave Management',
    'WeekOffViewSet': 'Leave Management',
    
    # Attendance Management
    'EmployeeAttendanceViewSet': 'Attendance Management',
    
    # Payroll Management
    'SalaryStructureViewSet': 'Payroll Management',
    'SalaryComponentViewSet': 'Payroll Management',
    'EmployeeSalaryViewSet': 'Payroll Management',
    'PayrollViewSet': 'Payroll Management',
    'PayrollItemViewSet': 'Payroll Management',
    'PayslipViewSet': 'Payroll Management',
    'BankTransferViewSet': 'Payroll Management',
    
    # Compensation & Benefits
    'SalaryViewSet': 'Compensation & Benefits',
    'EmployeeHikeViewSet': 'Compensation & Benefits',
    'BonusViewSet': 'Compensation & Benefits',
    'BonusBatchViewSet': 'Compensation & Benefits',
    'LoanViewSet': 'Compensation & Benefits',
    'LoanInstallmentViewSet': 'Compensation & Benefits',
    'TaxSlabViewSet': 'Compensation & Benefits',
    'EmployeeTaxDeclarationViewSet': 'Compensation & Benefits',
    
    # Recruitment & Hiring
    'JobRequisitionViewSet': 'Recruitment & Hiring',
    'JobPostingViewSet': 'Recruitment & Hiring',
    'CandidateViewSet': 'Recruitment & Hiring',
    'ApplicationViewSet': 'Recruitment & Hiring',
    'InterviewViewSet': 'Recruitment & Hiring',
    'InterviewFeedbackViewSet': 'Recruitment & Hiring',
    'OfferViewSet': 'Recruitment & Hiring',
    'OnboardingViewSet': 'Recruitment & Hiring',
    'RecruitmentSourceViewSet': 'Recruitment & Hiring',
    'SkillViewSet': 'Recruitment & Hiring',
    
    # Onboarding & Offboarding
    'OnboardingPlanViewSet': 'Onboarding & Offboarding',
    'OnboardingTaskViewSet': 'Onboarding & Offboarding',
    'OffboardingRequestViewSet': 'Onboarding & Offboarding',
    'OffboardingTaskViewSet': 'Onboarding & Offboarding',
    'ExitFeedbackViewSet': 'Onboarding & Offboarding',
    
    # Document Management
    'DocumentTemplateViewSet': 'Document Management',
    'DocumentViewSet': 'Document Management',
    'EmployeeDocumentViewSet': 'Document Management',
    'OrganizationDocumentViewSet': 'Document Management',
    
    # AI Assistant
    'VectorChatView': 'AI Assistant',
    
    # System
    'HealthCheckView': 'System'
}

def find_python_files(directory):
    """Find all Python files in the project views."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        if any(skip_dir in root for skip_dir in ['.venv', '__pycache__', '.git', 'site-packages', 'Scripts', 'Lib']):
            continue
        if 'views' in root:
            for file in files:
                if file.endswith('.py') and file != '__init__.py':
                    python_files.append(os.path.join(root, file))
    return python_files

def add_method_tags(file_path):
    """Add method-level tags to ViewSets in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Find ViewSet classes and their tags
        for viewset_name, tag in VIEWSET_TAGS.items():
            if viewset_name in content:
                # Add method-level decorators for standard CRUD operations
                methods_to_tag = ['list', 'create', 'retrieve', 'update', 'partial_update', 'destroy']
                
                for method in methods_to_tag:
                    # Look for method definitions
                    method_pattern = rf'(\s+)def {method}\(self[^)]*\):'
                    matches = list(re.finditer(method_pattern, content))
                    
                    for match in reversed(matches):  # Process in reverse to maintain positions
                        indent = match.group(1)
                        method_start = match.start()
                        
                        # Check if there's already a decorator
                        before_method = content[:method_start]
                        if '@swagger_auto_schema' not in before_method[-200:]:
                            # Add the decorator
                            decorator = f'{indent}@swagger_auto_schema(tags=[\'{tag}\'])\n'
                            content = content[:method_start] + decorator + content[method_start:]
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"  ❌ Error processing {file_path}: {e}")
        return False

def main():
    """Main function to add method-level tags."""
    print("🏷️  Adding method-level Swagger tags...")
    
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Find all Python files in views
    python_files = find_python_files(project_root)
    
    # Filter to only our app files
    project_files = [f for f in python_files if any(app in f for app in [
        'employees_app', 'organization_app', 'leave_app', 'payroll_app', 
        'recruitment_app', 'onboarding_offboarding_app', 'common'
    ])]
    
    print(f"📁 Found {len(project_files)} view files to process")
    
    updated_count = 0
    
    # Process each file
    for file_path in project_files:
        if add_method_tags(file_path):
            print(f"  ✅ Added method tags to: {os.path.relpath(file_path, project_root)}")
            updated_count += 1
    
    print(f"\n✨ Method-level tagging completed!")
    print(f"📊 Updated {updated_count} files")
    
    if updated_count > 0:
        print("\n📋 Next steps:")
        print("1. Restart your Django server")
        print("2. Check Swagger UI for proper grouping")
        print("3. Test the API documentation")

if __name__ == "__main__":
    main()
