# Generated by Django 5.1.7 on 2025-05-10 10:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employees_app', '0012_employeehike_status_alter_employeehike_approved_by'),
        ('organization_app', '0002_department'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('document_type', models.CharField(choices=[('onboarding', 'Onboarding'), ('offboarding', 'Offboarding'), ('both', 'Both')], default='both', max_length=20)),
                ('template_file', models.FileField(blank=True, null=True, upload_to='document_templates/')),
                ('template_content', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_templates', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Document Template',
                'verbose_name_plural': 'Document Templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('document_type', models.CharField(choices=[('onboarding', 'Onboarding'), ('offboarding', 'Offboarding')], max_length=20)),
                ('document_file', models.FileField(blank=True, null=True, upload_to='employee_documents/%Y/%m/')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('submitted', 'Submitted'), ('verified', 'Verified'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding_documents', to='employees_app.employee')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_documents', to='employees_app.employee')),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='documents', to='onboarding_offboarding_app.documenttemplate')),
            ],
            options={
                'verbose_name': 'Document',
                'verbose_name_plural': 'Documents',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OffboardingRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('submitted', 'Submitted'), ('approved', 'Approved'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rejected', 'Rejected')], default='submitted', max_length=20)),
                ('exit_type', models.CharField(choices=[('resignation', 'Resignation'), ('termination', 'Termination'), ('retirement', 'Retirement'), ('end_of_contract', 'End of Contract'), ('layoff', 'Layoff'), ('other', 'Other')], default='resignation', max_length=20)),
                ('resignation_date', models.DateField()),
                ('last_working_day', models.DateField()),
                ('notice_period_days', models.PositiveIntegerField(default=30)),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('exit_reason', models.TextField()),
                ('exit_reason_category', models.CharField(blank=True, max_length=50, null=True)),
                ('rehire_eligible', models.BooleanField(default=True)),
                ('exit_interview_scheduled', models.BooleanField(default=False)),
                ('exit_interview_date', models.DateTimeField(blank=True, null=True)),
                ('final_settlement_processed', models.BooleanField(default=False)),
                ('final_settlement_date', models.DateField(blank=True, null=True)),
                ('final_settlement_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_offboarding_requests', to='employees_app.employee')),
                ('department', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='offboarding_requests', to='organization_app.department')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='offboarding_requests', to='employees_app.employee')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_offboarding_requests', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Offboarding Request',
                'verbose_name_plural': 'Offboarding Requests',
                'ordering': ['-resignation_date'],
            },
        ),
        migrations.CreateModel(
            name='ExitFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_experience', models.PositiveIntegerField(blank=True, choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')], null=True)),
                ('work_environment', models.PositiveIntegerField(blank=True, choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')], null=True)),
                ('work_life_balance', models.PositiveIntegerField(blank=True, choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')], null=True)),
                ('compensation_benefits', models.PositiveIntegerField(blank=True, choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')], null=True)),
                ('career_growth', models.PositiveIntegerField(blank=True, choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')], null=True)),
                ('management', models.PositiveIntegerField(blank=True, choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')], null=True)),
                ('reason_for_leaving', models.TextField(blank=True, null=True)),
                ('what_did_you_like', models.TextField(blank=True, null=True)),
                ('what_could_be_improved', models.TextField(blank=True, null=True)),
                ('would_recommend', models.BooleanField(blank=True, null=True)),
                ('would_return', models.BooleanField(blank=True, null=True)),
                ('additional_comments', models.TextField(blank=True, null=True)),
                ('interview_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('conducted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conducted_exit_interviews', to='employees_app.employee')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='exit_feedback', to='employees_app.employee')),
                ('offboarding_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='exit_feedback', to='onboarding_offboarding_app.offboardingrequest')),
            ],
            options={
                'verbose_name': 'Exit Feedback',
                'verbose_name_plural': 'Exit Feedback',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OffboardingTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('category', models.CharField(choices=[('hr', 'HR'), ('it', 'IT'), ('admin', 'Admin'), ('finance', 'Finance'), ('department', 'Department'), ('knowledge_transfer', 'Knowledge Transfer'), ('exit_interview', 'Exit Interview'), ('other', 'Other')], default='other', max_length=20)),
                ('status', models.CharField(choices=[('not_started', 'Not Started'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('not_applicable', 'Not Applicable')], default='not_started', max_length=20)),
                ('due_date', models.DateField(blank=True, null=True)),
                ('completed_date', models.DateField(blank=True, null=True)),
                ('sequence', models.PositiveIntegerField(default=1)),
                ('is_mandatory', models.BooleanField(default=True)),
                ('assigned_to_role', models.CharField(blank=True, max_length=50, null=True)),
                ('document', models.FileField(blank=True, null=True, upload_to='offboarding_documents/%Y/%m/')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_offboarding_tasks', to='employees_app.employee')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_offboarding_tasks', to='employees_app.employee')),
                ('request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='onboarding_offboarding_app.offboardingrequest')),
            ],
            options={
                'verbose_name': 'Offboarding Task',
                'verbose_name_plural': 'Offboarding Tasks',
                'ordering': ['sequence', 'due_date'],
            },
        ),
        migrations.CreateModel(
            name='OnboardingPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('welcome_email_sent', models.BooleanField(default=False)),
                ('welcome_email_sent_at', models.DateTimeField(blank=True, null=True)),
                ('orientation_completed', models.BooleanField(default=False)),
                ('orientation_completed_at', models.DateTimeField(blank=True, null=True)),
                ('it_setup_completed', models.BooleanField(default=False)),
                ('it_setup_completed_at', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_plans', to='employees_app.employee')),
                ('department', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='onboarding_plans', to='organization_app.department')),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding_plan', to='employees_app.employee')),
                ('mentor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='mentored_plans', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Onboarding Plan',
                'verbose_name_plural': 'Onboarding Plans',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='OnboardingTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('category', models.CharField(choices=[('documentation', 'Documentation'), ('it_setup', 'IT Setup'), ('orientation', 'Orientation'), ('training', 'Training'), ('department', 'Department'), ('hr', 'HR'), ('admin', 'Admin'), ('other', 'Other')], default='other', max_length=20)),
                ('status', models.CharField(choices=[('not_started', 'Not Started'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='not_started', max_length=20)),
                ('due_date', models.DateField(blank=True, null=True)),
                ('completed_date', models.DateField(blank=True, null=True)),
                ('sequence', models.PositiveIntegerField(default=1)),
                ('is_mandatory', models.BooleanField(default=True)),
                ('assigned_to_role', models.CharField(blank=True, max_length=50, null=True)),
                ('document', models.FileField(blank=True, null=True, upload_to='onboarding_documents/%Y/%m/')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_onboarding_app_tasks', to='employees_app.employee')),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_onboarding_app_tasks', to='employees_app.employee')),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='onboarding_offboarding_app.onboardingplan')),
            ],
            options={
                'verbose_name': 'Onboarding Task',
                'verbose_name_plural': 'Onboarding Tasks',
                'ordering': ['sequence', 'due_date'],
            },
        ),
    ]
