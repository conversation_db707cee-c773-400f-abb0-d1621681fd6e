# Generated by Django 5.1.5 on 2025-05-03 21:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees_app', '0011_employeedocument_employeeleave_jobhistory_salary_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='employeehike',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=10),
        ),
        migrations.AlterField(
            model_name='employeehike',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='approved_hikes', to='employees_app.employee'),
        ),
    ]
