from rest_framework import serializers
from recruitment_app.models.job_posting import JobPosting
from recruitment_app.serializers.job_requisition_serializer import JobRequisitionSerializer
from recruitment_app.serializers.recruitment_source_serializer import RecruitmentSourceSerializer
from employees_app.serializers.employee_serializer import EmployeeSerializer


class JobPostingSerializer(serializers.ModelSerializer):
    """
    Serializer for the JobPosting model.
    """
    job_requisition_details = JobRequisitionSerializer(source='job_requisition', read_only=True)
    sources_details = RecruitmentSourceSerializer(source='sources', many=True, read_only=True)
    created_by_details = EmployeeSerializer(source='created_by', read_only=True)
    updated_by_details = EmployeeSerializer(source='updated_by', read_only=True)
    
    class Meta:
        model = JobPosting
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at', 'view_count', 'application_count']
    
    def validate(self, data):
        """
        Validate the job posting data.
        """
        # Validate published and expiry dates
        published_date = data.get('published_date')
        expiry_date = data.get('expiry_date')
        
        if published_date and expiry_date and published_date > expiry_date:
            raise serializers.ValidationError("Published date cannot be later than expiry date.")
        
        # Validate salary range
        salary_min = data.get('salary_min')
        salary_max = data.get('salary_max')
        
        if salary_min is not None and salary_max is not None and salary_min > salary_max:
            raise serializers.ValidationError("Minimum salary cannot be greater than maximum salary.")
        
        return data
