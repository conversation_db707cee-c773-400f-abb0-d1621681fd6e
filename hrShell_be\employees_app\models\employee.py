from django.db import models
from django.utils import timezone
from enum import Enum


def get_current_date():
    """
    Returns the current date (without time)
    """
    return timezone.now().date()


class Gender(str, Enum):
    MALE = 'male'
    FEMALE = 'female'
    OTHER = 'other'

    @classmethod
    def choices(cls):
        return [(item.value, item.name.title()) for item in cls]


class Status(str, Enum):
    ACTIVE = 'active'
    INACTIVE = 'inactive'

    @classmethod
    def choices(cls):
        return [(item.value, item.name.title()) for item in cls]


class Employee(models.Model):
    """
    Employee model for storing employee information
    """
    # Basic personal information
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=20, blank=True, null=True)

    # Employment details
    position = models.CharField(max_length=100)
    department = models.ForeignKey(
        'Department',
        on_delete=models.SET_NULL,
        null=True,
        related_name='employees'
    )
    hire_date = models.DateField(default=get_current_date)

    # Status
    gender = models.CharField(
        max_length=10,
        choices=Gender.choices(),
        default=Gender.MALE.value
    )
    status = models.CharField(
        max_length=10,
        choices=Status.choices(),
        default=Status.ACTIVE.value
    )

    # Document attachments
    profile_picture = models.ImageField(
        upload_to='employee_images/',
        null=True,
        blank=True
    )
    resume = models.FileField(
        upload_to='employee_documents/resumes/',
        null=True,
        blank=True
    )
    contract = models.FileField(
        upload_to='employee_documents/contracts/',
        null=True,
        blank=True
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
