from rest_framework import serializers
from payroll_app.models.payroll import Payroll, PayrollItem, PayrollItemComponent
from employees_app.models.employee import Employee


class PayrollItemComponentSerializer(serializers.ModelSerializer):
    class Meta:
        model = PayrollItemComponent
        fields = [
            'id', 'payroll_item', 'name', 'component_type', 'amount',
            'is_taxable', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class PayrollItemSerializer(serializers.ModelSerializer):
    employee_name = serializers.SerializerMethodField()
    components = PayrollItemComponentSerializer(many=True, read_only=True)
    component_values = serializers.ListField(
        child=serializers.DictField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = PayrollItem
        fields = [
            'id', 'payroll', 'employee', 'employee_name', 'working_days', 'leave_days',
            'lop_days', 'overtime_hours', 'basic_salary', 'gross_earnings', 'total_deductions',
            'net_payable', 'status', 'remarks', 'components', 'component_values',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_employee_name(self, obj):
        return f"{obj.employee.first_name} {obj.employee.last_name}" if obj.employee else None
    
    def create(self, validated_data):
        component_values = validated_data.pop('component_values', [])
        
        # Create the payroll item
        payroll_item = PayrollItem.objects.create(**validated_data)
        
        # Create the components
        self._create_or_update_components(payroll_item, component_values)
        
        return payroll_item
    
    def update(self, instance, validated_data):
        component_values = validated_data.pop('component_values', None)
        
        # Update the payroll item
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()
        
        # Update the components if provided
        if component_values is not None:
            self._create_or_update_components(instance, component_values)
        
        return instance
    
    def _create_or_update_components(self, payroll_item, component_values):
        if not component_values:
            return
        
        # Delete existing components if we're updating with new values
        PayrollItemComponent.objects.filter(payroll_item=payroll_item).delete()
        
        # Create new components
        for component_data in component_values:
            PayrollItemComponent.objects.create(
                payroll_item=payroll_item,
                name=component_data.get('name'),
                component_type=component_data.get('component_type'),
                amount=component_data.get('amount'),
                is_taxable=component_data.get('is_taxable', True)
            )


class PayrollSerializer(serializers.ModelSerializer):
    organization_name = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    approved_by_name = serializers.SerializerMethodField()
    items_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Payroll
        fields = [
            'id', 'name', 'organization', 'organization_name', 'start_date', 'end_date',
            'payment_date', 'status', 'total_earnings', 'total_deductions', 'net_payable',
            'remarks', 'created_by', 'created_by_name', 'approved_by', 'approved_by_name',
            'items_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'total_earnings', 'total_deductions', 'net_payable', 'items_count']
    
    def get_organization_name(self, obj):
        return obj.organization.name if obj.organization else None
    
    def get_created_by_name(self, obj):
        if not obj.created_by:
            return None
        return f"{obj.created_by.first_name} {obj.created_by.last_name}"
    
    def get_approved_by_name(self, obj):
        if not obj.approved_by:
            return None
        return f"{obj.approved_by.first_name} {obj.approved_by.last_name}"
    
    def get_items_count(self, obj):
        return obj.items.count()


class PayrollDetailSerializer(PayrollSerializer):
    items = PayrollItemSerializer(many=True, read_only=True)
    
    class Meta(PayrollSerializer.Meta):
        fields = PayrollSerializer.Meta.fields + ['items']
