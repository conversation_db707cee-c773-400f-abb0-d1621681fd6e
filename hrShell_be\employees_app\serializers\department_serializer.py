from rest_framework import serializers
from employees_app.models.department import Department, DepartmentStatus
from employees_app.serializers.employee_serializer import EmployeeListSerializer


class DepartmentSerializer(serializers.ModelSerializer):
    """
    Serializer for the Department model
    """
    manager_name = serializers.ReadOnlyField(source='manager.full_name')
    employee_count = serializers.ReadOnlyField()

    class Meta:
        model = Department
        fields = [
            'id', 'name', 'description', 'manager', 'manager_name',
            'status', 'employee_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
        ref_name = 'EmployeeDepartment'

    def validate_name(self, value):
        """
        Validate that the department name is unique
        """
        if self.instance and self.instance.name == value:
            return value

        if Department.objects.filter(name=value).exists():
            raise serializers.ValidationError("A department with this name already exists.")
        return value

    def validate_status(self, value):
        """
        Validate that the status is one of the allowed choices
        """
        try:
            DepartmentStatus(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError("Status must be one of: active or inactive")


class DepartmentDetailSerializer(DepartmentSerializer):
    """
    Detailed serializer for the Department model including employees
    """
    employees = EmployeeListSerializer(many=True, read_only=True)

    class Meta(DepartmentSerializer.Meta):
        fields = DepartmentSerializer.Meta.fields + ['employees']
        ref_name = 'EmployeeDepartmentDetail'
