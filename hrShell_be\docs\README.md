# HR Management API Documentation

Welcome to the documentation for the HR Management API. This documentation provides comprehensive information about the API, its endpoints, models, and how to use it.

## Table of Contents

1. [Setup and Installation](setup/installation.md)
2. [API Reference](api/README.md)
3. [Models](models/README.md)
4. [User Guides](guides/README.md)

## Overview

The HR Management API is a RESTful API built with Django REST Framework that provides endpoints for managing employees and departments in an HR system. It includes features such as:

- Employee management (CRUD operations)
- Department management (CRUD operations)
- Authentication and authorization
- Filtering and searching
- Pagination

## Quick Start

To get started with the HR Management API, follow these steps:

1. [Install the API](setup/installation.md)
2. [Configure the API](setup/configuration.md)
3. [Run the API](setup/running.md)
4. [Make your first API call](guides/first-api-call.md)

## API Endpoints

For a complete list of API endpoints, see the [API Reference](api/README.md).

## Models

For information about the data models used in the API, see the [Models](models/README.md) section.

## Contributing

For information about contributing to the project, see the [Contributing Guide](guides/contributing.md).

## License

This project is licensed under the MIT License - see the LICENSE file for details.
