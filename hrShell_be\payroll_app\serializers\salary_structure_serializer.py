from rest_framework import serializers
from payroll_app.models.salary_structure import SalaryStructure, SalaryComponent
from organization_app.models.organization import Organization


class SalaryComponentSerializer(serializers.ModelSerializer):
    organization_name = serializers.SerializerMethodField()
    
    class Meta:
        model = SalaryComponent
        fields = [
            'id', 'name', 'code', 'description', 'component_type', 'calculation_type',
            'value', 'formula', 'is_taxable', 'is_fixed', 'organization', 'organization_name',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_organization_name(self, obj):
        return obj.organization.name if obj.organization else None
    
    def validate(self, data):
        # Validate formula if calculation_type is 'formula'
        if data.get('calculation_type') == 'formula' and not data.get('formula'):
            raise serializers.ValidationError({"formula": "Formula is required when calculation type is 'formula'"})
        
        # Validate value if calculation_type is not 'formula'
        if data.get('calculation_type') != 'formula' and data.get('value', 0) <= 0:
            raise serializers.ValidationError({"value": "Value must be greater than 0 for non-formula calculation types"})
        
        return data


class SalaryStructureSerializer(serializers.ModelSerializer):
    organization_name = serializers.SerializerMethodField()
    components = SalaryComponentSerializer(many=True, read_only=True)
    component_ids = serializers.PrimaryKeyRelatedField(
        queryset=SalaryComponent.objects.all(),
        many=True,
        write_only=True,
        required=False
    )
    
    class Meta:
        model = SalaryStructure
        fields = [
            'id', 'name', 'code', 'description', 'organization', 'organization_name',
            'salary_type', 'components', 'component_ids', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_organization_name(self, obj):
        return obj.organization.name if obj.organization else None
    
    def create(self, validated_data):
        component_ids = validated_data.pop('component_ids', [])
        salary_structure = SalaryStructure.objects.create(**validated_data)
        
        if component_ids:
            salary_structure.components.set(component_ids)
        
        return salary_structure
    
    def update(self, instance, validated_data):
        component_ids = validated_data.pop('component_ids', None)
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()
        
        if component_ids is not None:
            instance.components.set(component_ids)
        
        return instance
