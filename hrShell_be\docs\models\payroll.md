# Payroll Models

## Payroll

The Payroll model represents a payroll run for a specific period.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the payroll | Yes |
| `organization` | ForeignKey | Organization this payroll belongs to | Yes |
| `start_date` | Date | Start date of the payroll period | Yes |
| `end_date` | Date | End date of the payroll period | Yes |
| `payment_date` | Date | Date of payment | Yes |
| `status` | String | Status of the payroll | Yes (default: draft) |
| `total_earnings` | Decimal | Total earnings for all employees | Yes (default: 0) |
| `total_deductions` | Decimal | Total deductions for all employees | Yes (default: 0) |
| `net_payable` | Decimal | Net payable amount for all employees | Yes (default: 0) |
| `remarks` | Text | Any remarks about this payroll | No |
| `created_by` | ForeignKey | Employee who created the payroll | No |
| `approved_by` | ForeignKey | Employee who approved the payroll | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Status Choices
- `draft`: Draft
- `processing`: Processing
- `completed`: Completed
- `cancelled`: Cancelled

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `organization` | Organization | The organization this payroll belongs to |
| `created_by` | Employee | The employee who created the payroll |
| `approved_by` | Employee | The employee who approved the payroll |
| `items` | PayrollItem | The payroll items for this payroll |
| `bank_transfers` | BankTransfer | The bank transfers for this payroll |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name, start date, and end date |

### Example

```python
payroll = Payroll.objects.create(
    name="April 2023 Payroll",
    organization=acme_corp,
    start_date=date(2023, 4, 1),
    end_date=date(2023, 4, 30),
    payment_date=date(2023, 5, 1),
    status="draft",
    created_by=jane_smith
)
```

## PayrollItem

The PayrollItem model represents an individual employee's payroll calculation for a specific payroll run.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `payroll` | ForeignKey | Payroll this item belongs to | Yes |
| `employee` | ForeignKey | Employee this item is for | Yes |
| `working_days` | Decimal | Number of working days | Yes (default: 0) |
| `leave_days` | Decimal | Number of leave days | Yes (default: 0) |
| `lop_days` | Decimal | Number of LOP (Leave Without Pay) days | Yes (default: 0) |
| `overtime_hours` | Decimal | Number of overtime hours | Yes (default: 0) |
| `basic_salary` | Decimal | Basic salary amount | Yes |
| `gross_earnings` | Decimal | Gross earnings amount | Yes (default: 0) |
| `total_deductions` | Decimal | Total deductions amount | Yes (default: 0) |
| `net_payable` | Decimal | Net payable amount | Yes (default: 0) |
| `status` | String | Status of the payroll item | Yes (default: draft) |
| `remarks` | Text | Any remarks about this payroll item | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Status Choices
- `draft`: Draft
- `calculated`: Calculated
- `approved`: Approved
- `paid`: Paid
- `cancelled`: Cancelled

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `payroll` | Payroll | The payroll this item belongs to |
| `employee` | Employee | The employee this item is for |
| `components` | PayrollItemComponent | The components for this payroll item |
| `payslip` | Payslip | The payslip for this payroll item |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee and payroll name |

### Example

```python
payroll_item = PayrollItem.objects.create(
    payroll=payroll,
    employee=john_doe,
    working_days=21.0,
    leave_days=1.0,
    lop_days=0.0,
    overtime_hours=5.0,
    basic_salary=55000.00,
    gross_earnings=88000.00,
    total_deductions=11000.00,
    net_payable=77000.00,
    status="draft"
)
```

## PayrollItemComponent

The PayrollItemComponent model stores the breakdown of earnings and deductions for a payroll item.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `payroll_item` | ForeignKey | Payroll item this component belongs to | Yes |
| `name` | String | Name of the component | Yes |
| `component_type` | String | Type of component (earning/deduction) | Yes |
| `amount` | Decimal | Amount of the component | Yes |
| `is_taxable` | Boolean | Whether the component is taxable | Yes (default: True) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Component Type Choices
- `earning`: Earning component (e.g., Basic, HRA)
- `deduction`: Deduction component (e.g., PF, TDS)

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `payroll_item` | PayrollItem | The payroll item this component belongs to |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, component name, and amount |

### Example

```python
basic_component = PayrollItemComponent.objects.create(
    payroll_item=payroll_item,
    name="Basic Pay",
    component_type="earning",
    amount=55000.00,
    is_taxable=True
)

hra_component = PayrollItemComponent.objects.create(
    payroll_item=payroll_item,
    name="House Rent Allowance",
    component_type="earning",
    amount=22000.00,
    is_taxable=False
)

pf_component = PayrollItemComponent.objects.create(
    payroll_item=payroll_item,
    name="Provident Fund",
    component_type="deduction",
    amount=6600.00,
    is_taxable=True
)
```

## API Endpoints

### Payroll Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/payrolls/` | GET | List all payrolls |
| `/api/v1/payrolls/` | POST | Create a new payroll |
| `/api/v1/payrolls/{id}/` | GET | Retrieve a specific payroll |
| `/api/v1/payrolls/{id}/` | PUT | Update a specific payroll |
| `/api/v1/payrolls/{id}/` | DELETE | Delete a specific payroll |
| `/api/v1/payrolls/status/{status}/` | GET | List all payrolls with a specific status |
| `/api/v1/payrolls/organization/{organization_id}/` | GET | List all payrolls for a specific organization |
| `/api/v1/payrolls/{id}/items/` | GET | List all payroll items for a specific payroll |
| `/api/v1/payrolls/{id}/generate-items/` | POST | Generate payroll items for all active employees |
| `/api/v1/payrolls/{id}/calculate/` | POST | Calculate all payroll items for this payroll |
| `/api/v1/payrolls/{id}/finalize/` | POST | Finalize the payroll and mark it as completed |
| `/api/v1/payrolls/{id}/cancel/` | POST | Cancel the payroll |

### PayrollItem Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/payroll-items/` | GET | List all payroll items |
| `/api/v1/payroll-items/` | POST | Create a new payroll item |
| `/api/v1/payroll-items/{id}/` | GET | Retrieve a specific payroll item |
| `/api/v1/payroll-items/{id}/` | PUT | Update a specific payroll item |
| `/api/v1/payroll-items/{id}/` | DELETE | Delete a specific payroll item |
| `/api/v1/payroll-items/status/{status}/` | GET | List all payroll items with a specific status |
| `/api/v1/payroll-items/employee/{employee_id}/` | GET | List all payroll items for a specific employee |
| `/api/v1/payroll-items/{id}/components/` | GET | List all components for a specific payroll item |
| `/api/v1/payroll-items/{id}/calculate/` | POST | Calculate a specific payroll item |
