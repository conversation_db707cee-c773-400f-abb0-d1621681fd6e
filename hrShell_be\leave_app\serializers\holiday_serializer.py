from rest_framework import serializers
from leave_app.models.holiday import Holiday, HolidayType


class HolidaySerializer(serializers.ModelSerializer):
    """
    Serializer for the Holiday model
    """
    organization_name = serializers.ReadOnlyField(source='organization.name')
    location_name = serializers.ReadOnlyField(source='location.name')
    holiday_type_display = serializers.ReadOnlyField(source='get_holiday_type_display')
    
    class Meta:
        model = Holiday
        fields = [
            'id', 'name', 'date', 'description', 'organization', 'organization_name',
            'location', 'location_name', 'holiday_type', 'holiday_type_display',
            'is_recurring', 'is_half_day', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate that the holiday is unique for the organization, location, and date
        """
        organization = data.get('organization')
        location = data.get('location')
        date = data.get('date')
        
        # Check if we're updating an existing instance
        instance = self.instance
        if instance:
            # If we're not changing organization, location, or date, no need to check
            if (instance.organization == organization and 
                instance.location == location and 
                instance.date == date):
                return data
        
        # Check if a holiday already exists for this organization, location, and date
        existing_holiday = Holiday.objects.filter(
            organization=organization,
            date=date
        )
        
        if location:
            existing_holiday = existing_holiday.filter(location=location)
        else:
            existing_holiday = existing_holiday.filter(location__isnull=True)
        
        if existing_holiday.exists():
            raise serializers.ValidationError(
                "A holiday already exists for this organization, location, and date"
            )
        
        return data
