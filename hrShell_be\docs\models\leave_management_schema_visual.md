# Leave Management Module - Visual Schema

This document provides a visual representation of the leave management module's database schema.

## Database Schema Diagram

```mermaid
classDiagram
    direction LR
    
    class LeaveType {
        <<table>>
        id: bigint PK
        name: varchar
        code: varchar
        description: text
        organization_id: bigint FK
        is_paid: boolean
        max_days_per_year: int
        min_days_per_request: int
        max_days_per_request: int
        carry_forward_allowed: boolean
        max_carry_forward_days: int
        encashment_allowed: boolean
        max_encashment_days: int
        applicable_during_probation: boolean
        probation_period_percentage: int
        requires_documentation: boolean
        documentation_instructions: text
        status: varchar
        created_at: timestamp
        updated_at: timestamp
    }
    
    class LeavePolicy {
        <<table>>
        id: bigint PK
        name: varchar
        description: text
        organization_id: bigint FK
        leave_type_id: bigint FK
        department_id: bigint FK
        location_id: bigint FK
        designation_id: bigint FK
        business_unit_id: bigint FK
        employee_type: varchar
        accrual_method: varchar
        accrual_frequency: varchar
        max_accrual: int
        carry_forward_limit: int
        carry_forward_expiry_months: int
        encashment_limit: int
        probation_period_days: int
        apply_sandwich_rule: boolean
        requires_approval: boolean
        auto_approve_after_days: int
        min_days_before_application: int
        max_days_before_application: int
        created_at: timestamp
        updated_at: timestamp
    }
    
    class LeaveBalance {
        <<table>>
        id: bigint PK
        employee_id: bigint FK
        leave_type_id: bigint FK
        year: int
        opening_balance: decimal
        accrued: decimal
        used: decimal
        adjusted: decimal
        encashed: decimal
        created_at: timestamp
        updated_at: timestamp
    }
    
    class LeaveRequest {
        <<table>>
        id: bigint PK
        employee_id: bigint FK
        leave_type_id: bigint FK
        start_date: date
        end_date: date
        total_days: decimal
        half_day: boolean
        first_half: boolean
        reason: text
        attachment: file
        status: varchar
        approved_by_id: bigint FK
        approval_date: timestamp
        rejection_reason: text
        created_at: timestamp
        updated_at: timestamp
    }
    
    class LeaveApproval {
        <<table>>
        id: bigint PK
        leave_request_id: bigint FK
        approver_id: bigint FK
        level: int
        status: varchar
        comments: text
        approval_date: timestamp
        created_at: timestamp
        updated_at: timestamp
    }
    
    class Holiday {
        <<table>>
        id: bigint PK
        name: varchar
        date: date
        description: text
        organization_id: bigint FK
        location_id: bigint FK
        holiday_type: varchar
        is_recurring: boolean
        is_half_day: boolean
        created_at: timestamp
        updated_at: timestamp
    }
    
    class WeekOff {
        <<table>>
        id: bigint PK
        organization_id: bigint FK
        location_id: bigint FK
        department_id: bigint FK
        day_of_week: int
        is_half_day: boolean
        first_half: boolean
        created_at: timestamp
        updated_at: timestamp
    }
    
    class Organization {
        <<external>>
        id: bigint PK
        name: varchar
        ...
    }
    
    class Employee {
        <<external>>
        id: bigint PK
        first_name: varchar
        last_name: varchar
        ...
    }
    
    class Department {
        <<external>>
        id: bigint PK
        name: varchar
        ...
    }
    
    class Location {
        <<external>>
        id: bigint PK
        name: varchar
        ...
    }
    
    class Designation {
        <<external>>
        id: bigint PK
        title: varchar
        ...
    }
    
    class BusinessUnit {
        <<external>>
        id: bigint PK
        name: varchar
        ...
    }
    
    %% Relationships
    LeaveType "1" --> "0..*" LeavePolicy : has
    LeaveType "1" --> "0..*" LeaveBalance : has
    LeaveType "1" --> "0..*" LeaveRequest : has
    
    LeavePolicy "0..*" --> "1" Organization : belongs to
    LeavePolicy "0..*" --> "0..1" Department : applies to
    LeavePolicy "0..*" --> "0..1" Location : applies to
    LeavePolicy "0..*" --> "0..1" Designation : applies to
    LeavePolicy "0..*" --> "0..1" BusinessUnit : applies to
    
    LeaveBalance "0..*" --> "1" Employee : belongs to
    
    LeaveRequest "0..*" --> "1" Employee : belongs to
    LeaveRequest "0..*" --> "0..1" Employee : approved by
    LeaveRequest "1" --> "0..*" LeaveApproval : has
    
    LeaveApproval "0..*" --> "1" Employee : approved by
    
    Holiday "0..*" --> "1" Organization : belongs to
    Holiday "0..*" --> "0..1" Location : applies to
    
    WeekOff "0..*" --> "1" Organization : belongs to
    WeekOff "0..*" --> "0..1" Location : applies to
    WeekOff "0..*" --> "0..1" Department : applies to
    
    %% Style
    class LeaveType fill:#3a3a5a,stroke:#56e5b1,color:white
    class LeavePolicy fill:#3a3a5a,stroke:#56e5b1,color:white
    class LeaveBalance fill:#3a3a5a,stroke:#56e5b1,color:white
    class LeaveRequest fill:#3a3a5a,stroke:#56e5b1,color:white
    class LeaveApproval fill:#3a3a5a,stroke:#56e5b1,color:white
    class Holiday fill:#3a3a5a,stroke:#56e5b1,color:white
    class WeekOff fill:#3a3a5a,stroke:#56e5b1,color:white
    
    class Organization fill:#3a3a5a,stroke:#8a56e5,color:white
    class Employee fill:#3a3a5a,stroke:#8a56e5,color:white
    class Department fill:#3a3a5a,stroke:#8a56e5,color:white
    class Location fill:#3a3a5a,stroke:#8a56e5,color:white
    class Designation fill:#3a3a5a,stroke:#8a56e5,color:white
    class BusinessUnit fill:#3a3a5a,stroke:#8a56e5,color:white
```

## Alternative Compact Representation

```mermaid
graph TD
    subgraph "Leave Management Module"
        A[LeaveType]
        B[LeavePolicy]
        C[LeaveBalance]
        D[LeaveRequest]
        E[LeaveApproval]
        F[Holiday]
        G[WeekOff]
    end
    
    subgraph "External Modules"
        H[Organization]
        I[Employee]
        J[Department]
        K[Location]
        L[Designation]
        M[BusinessUnit]
    end
    
    %% Relationships
    A -->|has many| B
    A -->|has many| C
    A -->|has many| D
    
    B -->|belongs to| H
    B -->|applies to| J
    B -->|applies to| K
    B -->|applies to| L
    B -->|applies to| M
    
    C -->|belongs to| I
    C -->|for| A
    
    D -->|belongs to| I
    D -->|for| A
    D -->|approved by| I
    D -->|has many| E
    
    E -->|for| D
    E -->|approved by| I
    
    F -->|belongs to| H
    F -->|applies to| K
    
    G -->|belongs to| H
    G -->|applies to| K
    G -->|applies to| J
    
    %% Style
    classDef leaveModule fill:#3a3a5a,stroke:#56e5b1,color:white;
    classDef externalModule fill:#3a3a5a,stroke:#8a56e5,color:white;
    
    class A,B,C,D,E,F,G leaveModule;
    class H,I,J,K,L,M externalModule;
```

This visual representation shows the leave management module's database schema in a style similar to ChartDB, with tables represented as nodes and relationships as connecting lines. The color scheme and layout are designed to match the provided reference image.
