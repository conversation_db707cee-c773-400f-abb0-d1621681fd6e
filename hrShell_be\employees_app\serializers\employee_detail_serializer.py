from rest_framework import serializers
from employees_app.models.employee_detail import EmployeeDetail, MaritalStatus


class EmployeeDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for the EmployeeDetail model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    department_name = serializers.ReadOnlyField(source='department.name')
    reporting_manager_name = serializers.ReadOnlyField(source='reporting_manager.full_name')
    
    class Meta:
        model = EmployeeDetail
        fields = [
            'id', 'employee', 'employee_name', 'address', 'contact_number',
            'emergency_contact_name', 'emergency_contact_number', 'email',
            'department', 'department_name', 'reporting_manager', 'reporting_manager_name',
            'experience', 'technical_skills', 'qualifications',
            'date_of_birth', 'marital_status', 'blood_group',
            'hobbies', 'languages_known', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_contact_number(self, value):
        """
        Validate that the contact number is in the correct format
        """
        import re
        if not re.match(r'^\+?1?\d{9,15}$', value):
            raise serializers.ValidationError(
                "Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
            )
        return value
    
    def validate_emergency_contact_number(self, value):
        """
        Validate that the emergency contact number is in the correct format
        """
        if value:
            import re
            if not re.match(r'^\+?1?\d{9,15}$', value):
                raise serializers.ValidationError(
                    "Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
                )
        return value
    
    def validate_marital_status(self, value):
        """
        Validate that the marital status is one of the allowed choices
        """
        try:
            MaritalStatus(value.lower())
            return value.lower()
        except ValueError:
            raise serializers.ValidationError(
                "Marital status must be one of: single, married, divorced, widowed, or other"
            )
    
    def validate(self, data):
        """
        Validate that the employee and reporting manager are not the same person
        """
        if 'employee' in data and 'reporting_manager' in data and data['employee'] == data['reporting_manager']:
            raise serializers.ValidationError(
                "An employee cannot be their own reporting manager."
            )
        return data
