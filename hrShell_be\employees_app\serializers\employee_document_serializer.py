from rest_framework import serializers
from employees_app.models.employee_documents import EmployeeDocument


class EmployeeDocumentSerializer(serializers.ModelSerializer):
    """
    Serializer for the EmployeeDocument model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    document_type_display = serializers.ReadOnlyField(source='get_document_type_display')
    file_url = serializers.SerializerMethodField()
    file_size = serializers.SerializerMethodField()
    file_size_formatted = serializers.SerializerMethodField()

    class Meta:
        model = EmployeeDocument
        fields = [
            'id', 'employee', 'employee_name', 'document_type', 'document_type_display',
            'file', 'file_url', 'file_size', 'file_size_formatted', 'document_name',
            'description', 'upload_date', 'is_verified', 'resume', 'contract',
            'signed_offer_letter', 'experience_certificates', 'qualification_certificates',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'upload_date', 'file_url', 'file_size', 'file_size_formatted']

    def get_file_url(self, obj):
        """Get the full URL for the file"""
        if obj.file and obj.file.name:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None

    def get_file_size(self, obj):
        """Get the file size in bytes"""
        if obj.file and obj.file.name:
            try:
                return obj.file.size
            except (OSError, ValueError):
                return None
        return None

    def get_file_size_formatted(self, obj):
        """Get the file size in human-readable format"""
        if obj.file and obj.file.name:
            try:
                size_bytes = obj.file.size
                return self._format_file_size(size_bytes)
            except (OSError, ValueError):
                return None
        return None

    def _format_file_size(self, size_bytes):
        """Convert bytes to human readable format"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)

        # Remove unnecessary decimal places
        if s == int(s):
            s = int(s)

        return f"{s} {size_names[i]}"

    def create(self, validated_data):
        """Custom create method to handle file uploads"""
        # Extract file from validated data if present
        file_obj = validated_data.get('file')

        # Create the document instance
        document = super().create(validated_data)

        # If file was provided, ensure it's properly saved
        if file_obj:
            document.file = file_obj
            document.save()

        return document

    def update(self, instance, validated_data):
        """Custom update method to handle file uploads"""
        # Extract file from validated data if present
        file_obj = validated_data.get('file')

        # Update the document instance
        document = super().update(instance, validated_data)

        # If file was provided, ensure it's properly saved
        if file_obj:
            document.file = file_obj
            document.save()

        return document
