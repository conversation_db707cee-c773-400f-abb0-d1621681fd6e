from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from employees_app.models.department import Department
from employees_app.serializers.department_serializer import DepartmentSerializer, DepartmentDetailSerializer
from employees_app.filters import DepartmentFilter


class DepartmentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for viewing and editing Department instances.
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = DepartmentFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']

    def get_serializer_class(self):
        """
        Return different serializers based on the action
        """
        if self.action == 'retrieve':
            return DepartmentDetailSerializer
        return DepartmentSerializer

    @swagger_auto_schema(
        tags=['Employee Management'],
        manual_parameters=[
            openapi.Parameter(
                'status',
                openapi.IN_QUERY,
                description='Filter by department status',
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'manager',
                openapi.IN_QUERY,
                description='Filter by manager ID',
                type=openapi.TYPE_INTEGER,
                required=False
            )
        ],
        operation_description="List all departments with optional filtering by status and manager ID"
    )
    def list(self, request, *args, **kwargs):
        """List all departments with optional filtering"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Employee Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Return a list of all active departments
        """
        active_departments = Department.objects.filter(status='active')
        page = self.paginate_queryset(active_departments)
        if page is not None:
            serializer = DepartmentSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = DepartmentSerializer(active_departments, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'])
    def deactivate(self, request, pk=None):
        """
        Deactivate a department
        """
        department = self.get_object()
        department.status = 'inactive'
        department.save()
        serializer = DepartmentSerializer(department)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'])
    def activate(self, request, pk=None):
        """
        Activate a department
        """
        department = self.get_object()
        department.status = 'active'
        department.save()
        serializer = DepartmentSerializer(department)
        return Response(serializer.data)
