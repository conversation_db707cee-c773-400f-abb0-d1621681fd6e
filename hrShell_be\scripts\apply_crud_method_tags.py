#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to apply CRUD method-level Swagger tags to all ViewSets.

This script adds @swagger_auto_schema decorators to individual CRUD methods
(list, create, retrieve, update, partial_update, destroy) to ensure proper 
grouping in Swagger UI.
"""

import os
import re
from pathlib import Path

# Tag mapping for ViewSets
VIEWSET_TAGS = {
    # Employee Management
    'EmployeeViewSet': 'Employee Management',
    'DepartmentViewSet': 'Employee Management',
    'EmployeeDetailViewSet': 'Employee Management',
    'EmployeeLeaveViewSet': 'Employee Management',
    'JobHistoryViewSet': 'Employee Management',
    'SkillOfferingViewSet': 'Employee Management',
    
    # Organization Structure
    'OrganizationViewSet': 'Organization Structure',
    'LocationViewSet': 'Organization Structure',
    'DesignationViewSet': 'Organization Structure',
    'BusinessUnitViewSet': 'Organization Structure',
    'OrganizationPolicyViewSet': 'Organization Structure',
    
    # Leave Management
    'LeaveTypeViewSet': 'Leave Management',
    'LeavePolicyViewSet': 'Leave Management',
    'LeaveRequestViewSet': 'Leave Management',
    'LeaveApprovalViewSet': 'Leave Management',
    'LeaveBalanceViewSet': 'Leave Management',
    'HolidayViewSet': 'Leave Management',
    'WeekOffViewSet': 'Leave Management',
    
    # Attendance Management
    'EmployeeAttendanceViewSet': 'Attendance Management',
    
    # Payroll Management
    'SalaryStructureViewSet': 'Payroll Management',
    'SalaryComponentViewSet': 'Payroll Management',
    'EmployeeSalaryViewSet': 'Payroll Management',
    'PayrollViewSet': 'Payroll Management',
    'PayrollItemViewSet': 'Payroll Management',
    'PayslipViewSet': 'Payroll Management',
    'BankTransferViewSet': 'Payroll Management',
    
    # Compensation & Benefits
    'SalaryViewSet': 'Compensation & Benefits',
    'EmployeeHikeViewSet': 'Compensation & Benefits',
    'BonusViewSet': 'Compensation & Benefits',
    'BonusBatchViewSet': 'Compensation & Benefits',
    'LoanViewSet': 'Compensation & Benefits',
    'LoanInstallmentViewSet': 'Compensation & Benefits',
    'TaxSlabViewSet': 'Compensation & Benefits',
    'EmployeeTaxDeclarationViewSet': 'Compensation & Benefits',
    
    # Recruitment & Hiring
    'JobRequisitionViewSet': 'Recruitment & Hiring',
    'JobPostingViewSet': 'Recruitment & Hiring',
    'CandidateViewSet': 'Recruitment & Hiring',
    'ApplicationViewSet': 'Recruitment & Hiring',
    'InterviewViewSet': 'Recruitment & Hiring',
    'InterviewFeedbackViewSet': 'Recruitment & Hiring',
    'OfferViewSet': 'Recruitment & Hiring',
    'OnboardingViewSet': 'Recruitment & Hiring',
    'RecruitmentSourceViewSet': 'Recruitment & Hiring',
    'SkillViewSet': 'Recruitment & Hiring',
    
    # Onboarding & Offboarding
    'OnboardingPlanViewSet': 'Onboarding & Offboarding',
    'OnboardingTaskViewSet': 'Onboarding & Offboarding',
    'OffboardingRequestViewSet': 'Onboarding & Offboarding',
    'OffboardingTaskViewSet': 'Onboarding & Offboarding',
    'ExitFeedbackViewSet': 'Onboarding & Offboarding',
    
    # Document Management
    'DocumentTemplateViewSet': 'Document Management',
    'DocumentViewSet': 'Document Management',
    'EmployeeDocumentViewSet': 'Document Management',
    'OrganizationDocumentViewSet': 'Document Management',
}

def find_python_files(directory):
    """Find all Python files in the project views."""
    python_files = []
    for root, dirs, files in os.walk(directory):
        if any(skip_dir in root for skip_dir in ['.venv', '__pycache__', '.git', 'site-packages', 'Scripts', 'Lib']):
            continue
        if 'views' in root:
            for file in files:
                if file.endswith('.py') and file != '__init__.py':
                    python_files.append(os.path.join(root, file))
    return python_files

def add_crud_method_tags(file_path):
    """Add CRUD method-level tags to ViewSets in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Find ViewSet classes and add CRUD method decorators
        for viewset_name, tag in VIEWSET_TAGS.items():
            if f'class {viewset_name}(' in content:
                # Remove class-level decorator if it exists
                class_pattern = rf'@swagger_auto_schema\([^)]*\)\s*class {viewset_name}\('
                content = re.sub(class_pattern, f'class {viewset_name}(', content)
                
                # Find the class definition
                class_match = re.search(rf'class {viewset_name}\([^)]*\):(.*?)(?=class|\Z)', content, re.DOTALL)
                if class_match:
                    class_content = class_match.group(1)
                    class_start = class_match.start(1)
                    
                    # Check if CRUD methods already exist
                    crud_methods = ['list', 'create', 'retrieve', 'update', 'partial_update', 'destroy']
                    methods_to_add = []
                    
                    for method in crud_methods:
                        if f'def {method}(self,' not in class_content:
                            methods_to_add.append(method)
                    
                    if methods_to_add:
                        # Find a good place to insert methods (after existing methods or at the beginning)
                        insert_position = class_start
                        
                        # Look for the end of __init__ or other setup methods
                        setup_methods = ['__init__', 'get_queryset', 'get_serializer_class', 'get_permissions']
                        for setup_method in setup_methods:
                            method_match = re.search(rf'def {setup_method}\([^)]*\):.*?(?=\n    def|\n    @|\Z)', class_content, re.DOTALL)
                            if method_match:
                                insert_position = class_start + method_match.end()
                        
                        # Generate CRUD method implementations
                        crud_methods_code = '\n'
                        for method in methods_to_add:
                            method_doc = {
                                'list': 'List all items',
                                'create': 'Create a new item',
                                'retrieve': 'Retrieve an item',
                                'update': 'Update an item',
                                'partial_update': 'Partially update an item',
                                'destroy': 'Delete an item'
                            }
                            
                            crud_methods_code += f'''
    @swagger_auto_schema(tags=['{tag}'])
    def {method}(self, request, *args, **kwargs):
        """{method_doc[method]}"""
        return super().{method}(request, *args, **kwargs)
'''
                        
                        # Insert the methods
                        content = content[:insert_position] + crud_methods_code + content[insert_position:]
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"  ❌ Error processing {file_path}: {e}")
        return False

def main():
    """Main function to add CRUD method-level tags."""
    print("🔧 Adding CRUD method-level Swagger tags...")
    
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Find all Python files in views
    python_files = find_python_files(project_root)
    
    # Filter to only our app files (excluding employee_views.py since we already updated it)
    project_files = [f for f in python_files if any(app in f for app in [
        'employees_app', 'organization_app', 'leave_app', 'payroll_app', 
        'recruitment_app', 'onboarding_offboarding_app'
    ]) and 'employee_views.py' not in f]
    
    print(f"📁 Found {len(project_files)} view files to process")
    
    updated_count = 0
    
    # Process each file
    for file_path in project_files:
        if add_crud_method_tags(file_path):
            print(f"  ✅ Added CRUD method tags to: {os.path.relpath(file_path, project_root)}")
            updated_count += 1
    
    print(f"\n✨ CRUD method tagging completed!")
    print(f"📊 Updated {updated_count} files")
    
    if updated_count > 0:
        print("\n📋 Next steps:")
        print("1. Restart your Django server")
        print("2. Check Swagger UI for proper grouping")
        print("3. Test the API documentation")

if __name__ == "__main__":
    main()
