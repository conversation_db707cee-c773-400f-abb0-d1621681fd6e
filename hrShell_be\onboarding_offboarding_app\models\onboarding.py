from django.db import models
from django.utils import timezone
from employees_app.models.employee import Employee
from organization_app.models import Department


class OnboardingPlan(models.Model):
    """
    Model to represent onboarding plans for new employees.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic Information
    employee = models.OneToOneField(Employee, on_delete=models.CASCADE, related_name='onboarding_plan')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)

    # Stakeholders
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, related_name='onboarding_plans')
    mentor = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='mentored_plans')
    assigned_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_plans')

    # Additional Information
    welcome_email_sent = models.BooleanField(default=False)
    welcome_email_sent_at = models.DateTimeField(null=True, blank=True)
    orientation_completed = models.BooleanField(default=False)
    orientation_completed_at = models.DateTimeField(null=True, blank=True)
    it_setup_completed = models.BooleanField(default=False)
    it_setup_completed_at = models.DateTimeField(null=True, blank=True)

    # Notes
    notes = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-start_date']
        verbose_name = 'Onboarding Plan'
        verbose_name_plural = 'Onboarding Plans'

    def __str__(self):
        return f"Onboarding Plan for {self.employee}"

    def save(self, *args, **kwargs):
        # Set end date to 90 days after start date if not provided
        if not self.end_date:
            self.end_date = self.start_date + timezone.timedelta(days=90)
        super().save(*args, **kwargs)


class OnboardingTask(models.Model):
    """
    Model to represent tasks to be completed during onboarding.
    """
    STATUS_CHOICES = [
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    CATEGORY_CHOICES = [
        ('documentation', 'Documentation'),
        ('it_setup', 'IT Setup'),
        ('orientation', 'Orientation'),
        ('training', 'Training'),
        ('department', 'Department'),
        ('hr', 'HR'),
        ('admin', 'Admin'),
        ('other', 'Other'),
    ]

    # Basic Information
    plan = models.ForeignKey(OnboardingPlan, on_delete=models.CASCADE, related_name='tasks')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='other')

    # Task Details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_started')
    due_date = models.DateField(null=True, blank=True)
    completed_date = models.DateField(null=True, blank=True)
    sequence = models.PositiveIntegerField(default=1)
    is_mandatory = models.BooleanField(default=True)

    # Assignment
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_onboarding_app_tasks')
    assigned_to_role = models.CharField(max_length=50, blank=True, null=True)

    # Completion Information
    completed_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='completed_onboarding_app_tasks')

    # Document
    document = models.FileField(upload_to='onboarding_documents/%Y/%m/', blank=True, null=True)

    # Notes
    notes = models.TextField(blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['sequence', 'due_date']
        verbose_name = 'Onboarding Task'
        verbose_name_plural = 'Onboarding Tasks'

    def __str__(self):
        return f"{self.name} for {self.plan.employee}"

    def save(self, *args, **kwargs):
        # Set due date to start date if not provided
        if not self.due_date:
            self.due_date = self.plan.start_date

        # Update status to completed if completed_date is set
        if self.completed_date and self.status != 'completed':
            self.status = 'completed'

        # Update completed_date if status is set to completed
        if self.status == 'completed' and not self.completed_date:
            self.completed_date = timezone.now().date()

        super().save(*args, **kwargs)
