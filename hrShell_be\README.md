# HR Management API

A Django REST API for HR management, providing endpoints for managing employees and departments.

## Features

- Employee management (CRUD operations)
- Department management (CRUD operations)
- Employee details management
- Employee attendance tracking
- Leave management
  - Leave types (Casual, Sick, Annual, etc.)
  - Leave policies per department/location/designation
  - Leave balances and accruals
  - Leave requests with multi-level approval
  - Holidays and week offs management
- Job history tracking
- Salary management
- Document management
- Skills management
- AI-powered assistance
  - Vector-based search for HR knowledge
  - AI chat interface for answering HR questions
  - Document processing and indexing
- JWT authentication
- Permission-based authorization
- Filtering, searching, and pagination
- File uploads (profile pictures, resumes, contracts, certificates)
- Comprehensive documentation

## Tech Stack

- Python 3.8+
- Django 5.1+
- Django REST Framework
- PostgreSQL with pgvector extension
- JWT Authentication
- Django Filter
- Sentence Transformers (for vector embeddings)
- Google Generative AI (Gemini)
- LangChain (for AI integrations)

## Quick Start

### Prerequisites

- Python 3.8 or higher
- PostgreSQL 12 or higher with pgvector extension
  - <PERSON> Docker and Docker Compose (recommended for easy setup with pgvector)
- pip (Python package manager)
- virtualenv (recommended)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/hrShell_be.git
   cd hrShell_be
   ```

2. Create a virtual environment:
   ```bash
   python -m venv hrShellEnv
   ```

3. Activate the virtual environment:
   - Windows: `hrShellEnv\Scripts\activate`
   - macOS/Linux: `source hrShellEnv/bin/activate`

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

5. Set up the database:

   **Option 1: Using Docker (Recommended for pgvector support)**
   ```bash
   # Start the PostgreSQL container with pgvector
   docker-compose up -d

   # Apply migrations
   python manage.py migrate
   ```

   **Option 2: Using local PostgreSQL**
   ```bash
   # Create the database
   createdb shell_dwh

   # Apply migrations
   python manage.py migrate
   ```

   For detailed Docker setup instructions, see [DOCKER_SETUP.md](DOCKER_SETUP.md)

6. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```

7. Run the development server:
   ```bash
   python manage.py runserver
   ```

The API will be available at http://127.0.0.1:8000/.

## API Endpoints

### System

- `GET /health/`: Health check endpoint

### Authentication

- `POST /api/token/`: Obtain JWT token
- `POST /api/token/refresh/`: Refresh JWT token
- `POST /api/token/verify/`: Verify JWT token

### AI Assistant

- `POST /api/v1/ai/chat/`: Chat with AI Assistant

### Employees

- `GET /api/v1/employees/`: List employees
- `POST /api/v1/employees/`: Create employee
- `GET /api/v1/employees/{id}/`: Retrieve employee
- `PUT /api/v1/employees/{id}/`: Update employee
- `PATCH /api/v1/employees/{id}/`: Partial update employee
- `DELETE /api/v1/employees/{id}/`: Delete employee

### Departments

- `GET /api/v1/departments/`: List departments
- `POST /api/v1/departments/`: Create department
- `GET /api/v1/departments/{id}/`: Retrieve department
- `PUT /api/v1/departments/{id}/`: Update department
- `PATCH /api/v1/departments/{id}/`: Partial update department
- `DELETE /api/v1/departments/{id}/`: Delete department

### Employee Details

- `GET /api/v1/employee-details/`: List employee details
- `POST /api/v1/employee-details/`: Create employee details
- `GET /api/v1/employee-details/{id}/`: Retrieve employee details
- `PUT /api/v1/employee-details/{id}/`: Update employee details
- `PATCH /api/v1/employee-details/{id}/`: Partial update employee details
- `DELETE /api/v1/employee-details/{id}/`: Delete employee details

### Employee Hikes

- `GET /api/v1/employee-hikes/`: List employee hikes
- `POST /api/v1/employee-hikes/`: Create employee hike
- `GET /api/v1/employee-hikes/{id}/`: Retrieve employee hike
- `PUT /api/v1/employee-hikes/{id}/`: Update employee hike
- `PATCH /api/v1/employee-hikes/{id}/`: Partial update employee hike
- `DELETE /api/v1/employee-hikes/{id}/`: Delete employee hike

### Employee Attendance

- `GET /api/v1/employee-attendance/`: List attendance records
- `POST /api/v1/employee-attendance/`: Create attendance record
- `GET /api/v1/employee-attendance/{id}/`: Retrieve attendance record
- `PUT /api/v1/employee-attendance/{id}/`: Update attendance record
- `PATCH /api/v1/employee-attendance/{id}/`: Partial update attendance record
- `DELETE /api/v1/employee-attendance/{id}/`: Delete attendance record

### Employee Leaves (Legacy)

- `GET /api/v1/employee-leaves/`: List leave records
- `POST /api/v1/employee-leaves/`: Create leave record
- `GET /api/v1/employee-leaves/{id}/`: Retrieve leave record
- `PUT /api/v1/employee-leaves/{id}/`: Update leave record
- `PATCH /api/v1/employee-leaves/{id}/`: Partial update leave record
- `DELETE /api/v1/employee-leaves/{id}/`: Delete leave record

### Leave Management

- `GET /api/v1/leave-types/`: List leave types
- `GET /api/v1/leave-policies/`: List leave policies
- `GET /api/v1/leave-balances/`: List leave balances
- `GET /api/v1/leave-requests/`: List leave requests
- `GET /api/v1/leave-approvals/`: List leave approvals
- `GET /api/v1/holidays/`: List holidays
- `GET /api/v1/week-offs/`: List week offs

For detailed endpoints, see the [Leave Management API Reference](docs/api/leave_management_api.md)

### Job History

- `GET /api/v1/job-history/`: List job history records
- `POST /api/v1/job-history/`: Create job history record
- `GET /api/v1/job-history/{id}/`: Retrieve job history record
- `PUT /api/v1/job-history/{id}/`: Update job history record
- `PATCH /api/v1/job-history/{id}/`: Partial update job history record
- `DELETE /api/v1/job-history/{id}/`: Delete job history record

### Salaries

- `GET /api/v1/salaries/`: List salary records
- `POST /api/v1/salaries/`: Create salary record
- `GET /api/v1/salaries/{id}/`: Retrieve salary record
- `PUT /api/v1/salaries/{id}/`: Update salary record
- `PATCH /api/v1/salaries/{id}/`: Partial update salary record
- `DELETE /api/v1/salaries/{id}/`: Delete salary record

### Employee Documents

- `GET /api/v1/employee-documents/`: List document records
- `POST /api/v1/employee-documents/`: Create document record
- `GET /api/v1/employee-documents/{id}/`: Retrieve document record
- `PUT /api/v1/employee-documents/{id}/`: Update document record
- `PATCH /api/v1/employee-documents/{id}/`: Partial update document record
- `DELETE /api/v1/employee-documents/{id}/`: Delete document record

### Skill Offerings

- `GET /api/v1/skill-offerings/`: List skill records
- `POST /api/v1/skill-offerings/`: Create skill record
- `GET /api/v1/skill-offerings/{id}/`: Retrieve skill record
- `PUT /api/v1/skill-offerings/{id}/`: Update skill record
- `PATCH /api/v1/skill-offerings/{id}/`: Partial update skill record
- `DELETE /api/v1/skill-offerings/{id}/`: Delete skill record

## Documentation

For detailed documentation, see the [docs](docs/) directory.

### API Documentation
- [API Documentation](docs/api_documentation.md)
- [API Endpoints](docs/api_endpoints.md)
- [Swagger UI](http://localhost:8000/swagger-ui/) (when server is running)

### Module Documentation
- [AI Assistant Guide](docs/guides/ai_assistant_guide.md)
- [Leave Management Guide](docs/guides/leave_management_guide.md)
- [Leave Management API Reference](docs/api/leave_management_api.md)
- [Leave Management Schema](docs/models/leave_management_schema.md)
- [Leave Management Visual Schema](docs/models/leave_management_schema_visual.md)

### Development Guidelines
- [Augment Guidelines](docs/AUGMENT_GUIDELINES.md)
- [Swagger Integration Guide](docs/SWAGGER_INTEGRATION_GUIDE.md)
- [Documentation Update Process](docs/DOCUMENTATION_UPDATE_PROCESS.md)
- [Module Integration Guide](docs/MODULE_INTEGRATION_GUIDE.md)

## Contributing

Contributions are welcome! See the [Contributing Guide](docs/guides/contributing.md) for more information.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

superuser credentials:

## username: neuraArk
## password: superuser
