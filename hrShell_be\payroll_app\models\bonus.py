from django.db import models
from django.core.validators import MinValueValidator
from organization_app.models.organization import Organization
from employees_app.models.employee import Employee


class Bonus(models.Model):
    """
    Model to represent bonuses and incentives given to employees.
    """
    BONUS_TYPE_CHOICES = [
        ('performance', 'Performance Bonus'),
        ('annual', 'Annual Bonus'),
        ('festival', 'Festival Bonus'),
        ('incentive', 'Sales Incentive'),
        ('joining', 'Joining Bonus'),
        ('retention', 'Retention Bonus'),
        ('referral', 'Referral Bonus'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('paid', 'Paid'),
        ('cancelled', 'Cancelled'),
    ]
    
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='bonuses')
    bonus_type = models.CharField(max_length=20, choices=BONUS_TYPE_CHOICES)
    amount = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    payment_date = models.DateField()
    reference_period_start = models.DateField(null=True, blank=True)
    reference_period_end = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    description = models.TextField(blank=True, null=True)
    is_taxable = models.BooleanField(default=True)
    is_prorated = models.BooleanField(default=False)
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='bonuses_approved')
    approval_date = models.DateField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-payment_date']
        verbose_name = 'Bonus'
        verbose_name_plural = 'Bonuses'
    
    def __str__(self):
        return f"{self.employee} - {self.get_bonus_type_display()} ({self.amount})"


class BonusBatch(models.Model):
    """
    Model to represent a batch of bonuses for multiple employees.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    name = models.CharField(max_length=100)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='bonus_batches')
    bonus_type = models.CharField(max_length=20, choices=Bonus.BONUS_TYPE_CHOICES)
    payment_date = models.DateField()
    reference_period_start = models.DateField(null=True, blank=True)
    reference_period_end = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    description = models.TextField(blank=True, null=True)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='bonus_batches_created')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='bonus_batches_approved')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-payment_date']
        verbose_name = 'Bonus Batch'
        verbose_name_plural = 'Bonus Batches'
    
    def __str__(self):
        return f"{self.name} - {self.get_bonus_type_display()} ({self.payment_date})"
