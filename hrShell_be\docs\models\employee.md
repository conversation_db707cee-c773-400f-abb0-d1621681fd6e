# Employee Model

The Employee model represents an employee in the organization.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `first_name` | String | First name of the employee | Yes |
| `last_name` | String | Last name of the employee | Yes |
| `email` | Email | Email address of the employee | Yes (unique) |
| `phone` | String | Phone number of the employee | No |
| `position` | String | Job position of the employee | Yes |
| `department` | ForeignKey | Department the employee belongs to | No |
| `hire_date` | Date | Date the employee was hired | Yes (default: current date) |
| `gender` | String | Gender of the employee (choices: male, female, other) | Yes (default: male) |
| `status` | String | Status of the employee (choices: active, inactive) | Yes (default: active) |
| `profile_picture` | Image | Profile picture of the employee | No |
| `resume` | File | Resume document of the employee | No |
| `contract` | File | Contract document of the employee | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Properties

| Property | Type | Description |
|----------|------|-------------|
| `full_name` | String | Concatenation of first_name and last_name |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `department` | Department | The department the employee belongs to |
| `managed_departments` | Department | The departments managed by the employee |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the full name of the employee |

## Example

```python
employee = Employee.objects.create(
    first_name="John",
    last_name="Doe",
    email="<EMAIL>",
    phone="+1234567890",
    position="Software Engineer",
    department=engineering_department,
    hire_date="2023-01-15",
    gender="male",
    status="active"
)

print(employee.full_name)  # Output: "John Doe"
```

## Validation Rules

- Email must be unique
- Gender must be one of: male, female, or other
- Status must be one of: active or inactive
