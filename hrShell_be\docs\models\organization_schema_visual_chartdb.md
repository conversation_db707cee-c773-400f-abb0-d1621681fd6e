# Organization Module Database Schema - Visual Representation

This document provides a visual representation of the organization module's database schema in a style similar to ChartDB.

## Database Schema Diagram

```mermaid
classDiagram
    direction LR
    
    class os_organization {
        <<table>>
        id: bigint PK
        name: varchar
        logo: varchar
        registration_number: varchar
        gst_number: varchar
        pan_number: varchar
        ein_number: varchar
        business_type: varchar
        establishment_date: date
        industry_sector: varchar
        default_currency: varchar
        default_timezone: varchar
        fiscal_year_start_month: int
        fiscal_year_start_day: int
        website: varchar
        email: varchar
        phone: varchar
        status: varchar
        custom_fields: jsonb
        created_at: timestamp
        updated_at: timestamp
    }
    
    class os_location {
        <<table>>
        id: bigint PK
        organization_id: bigint FK
        name: varchar
        address_line1: varchar
        address_line2: varchar
        city: varchar
        state: varchar
        country: varchar
        postal_code: varchar
        phone: varchar
        email: varchar
        latitude: decimal
        longitude: decimal
        working_hours_start: time
        working_hours_end: time
        working_days: varchar
        is_headquarters: boolean
        status: varchar
        custom_fields: jsonb
        created_at: timestamp
        updated_at: timestamp
    }
    
    class os_business_unit {
        <<table>>
        id: bigint PK
        organization_id: bigint FK
        name: varchar
        description: text
        code: varchar
        parent_id: bigint FK
        head_id: bigint FK
        primary_location_id: bigint FK
        status: varchar
        created_at: timestamp
        updated_at: timestamp
    }
    
    class os_designation {
        <<table>>
        id: bigint PK
        organization_id: bigint FK
        title: varchar
        description: text
        level: varchar
        department_id: bigint FK
        pay_grade: varchar
        min_salary: decimal
        max_salary: decimal
        created_at: timestamp
        updated_at: timestamp
    }
    
    class os_organization_policy {
        <<table>>
        id: bigint PK
        organization_id: bigint FK
        name: varchar
        description: text
        policy_type: varchar
        content: text
        applicable_to_all: boolean
        effective_from: date
        effective_to: date
        created_at: timestamp
        updated_at: timestamp
    }
    
    class os_organization_document {
        <<table>>
        id: bigint PK
        organization_id: bigint FK
        title: varchar
        description: text
        document_type: varchar
        file: varchar
        version: varchar
        effective_date: date
        expiry_date: date
        business_unit_id: bigint FK
        created_at: timestamp
        updated_at: timestamp
    }
    
    class os_policy_location {
        <<junction>>
        id: bigint PK
        policy_id: bigint FK
        location_id: bigint FK
    }
    
    class os_policy_department {
        <<junction>>
        id: bigint PK
        policy_id: bigint FK
        department_id: bigint FK
    }
    
    class os_policy_business_unit {
        <<junction>>
        id: bigint PK
        policy_id: bigint FK
        business_unit_id: bigint FK
    }
    
    class em_department {
        <<table>>
        id: bigint PK
        name: varchar
        description: text
        manager_id: bigint FK
        status: varchar
        business_unit_id: bigint FK
        created_at: timestamp
        updated_at: timestamp
    }
    
    class em_employee {
        <<table>>
        id: bigint PK
        first_name: varchar
        last_name: varchar
        email: varchar
        phone: varchar
        position: varchar
        department_id: bigint FK
        hire_date: date
        gender: varchar
        status: varchar
        profile_picture: varchar
        resume: varchar
        contract: varchar
        created_at: timestamp
        updated_at: timestamp
    }
    
    %% Define relationships with lines
    os_organization "1" --> "0..*" os_location : has
    os_organization "1" --> "0..*" os_business_unit : has
    os_organization "1" --> "0..*" os_designation : has
    os_organization "1" --> "0..*" os_organization_policy : has
    os_organization "1" --> "0..*" os_organization_document : has
    
    os_organization_policy "1" --> "0..*" os_policy_location : has
    os_location "1" --> "0..*" os_policy_location : belongs to
    
    os_organization_policy "1" --> "0..*" os_policy_department : has
    em_department "1" --> "0..*" os_policy_department : belongs to
    
    os_organization_policy "1" --> "0..*" os_policy_business_unit : has
    os_business_unit "1" --> "0..*" os_policy_business_unit : belongs to
    
    os_business_unit "0..1" --> "0..*" os_business_unit : parent of
    os_business_unit "0..*" --> "0..1" os_location : has primary
    os_business_unit "0..*" --> "0..1" em_employee : headed by
    
    em_department "0..*" --> "0..1" os_business_unit : belongs to
    em_department "1" --> "0..*" os_designation : has
    
    em_employee "0..*" --> "1" em_department : belongs to
    
    os_organization_document "0..*" --> "0..1" os_business_unit : related to
    
    %% Style the diagram
    class os_organization fill:#3a3a5a,stroke:#8a56e5,color:white
    class os_location fill:#3a3a5a,stroke:#56e5b1,color:white
    class os_business_unit fill:#3a3a5a,stroke:#56e5b1,color:white
    class os_designation fill:#3a3a5a,stroke:#8a56e5,color:white
    class os_organization_policy fill:#3a3a5a,stroke:#e556b1,color:white
    class os_organization_document fill:#3a3a5a,stroke:#56e5b1,color:white
    class os_policy_location fill:#3a3a5a,stroke:#e55656,color:white
    class os_policy_department fill:#3a3a5a,stroke:#e55656,color:white
    class os_policy_business_unit fill:#3a3a5a,stroke:#e55656,color:white
    class em_department fill:#3a3a5a,stroke:#8a56e5,color:white
    class em_employee fill:#3a3a5a,stroke:#56e5b1,color:white
```

## Alternative Visual Representation

For a more compact representation similar to the ChartDB style:

```mermaid
graph TD
    subgraph "Organization Module"
        A[os_organization]
        B[os_location]
        C[os_business_unit]
        D[os_designation]
        E[os_organization_policy]
        F[os_organization_document]
        G[os_policy_location]
        H[os_policy_department]
        I[os_policy_business_unit]
    end
    
    subgraph "Employee Module"
        J[em_department]
        K[em_employee]
    end
    
    %% Organization relationships
    A -->|has many| B
    A -->|has many| C
    A -->|has many| D
    A -->|has many| E
    A -->|has many| F
    
    %% Policy junction tables
    E -->|has many| G
    B -->|belongs to| G
    
    E -->|has many| H
    J -->|belongs to| H
    
    E -->|has many| I
    C -->|belongs to| I
    
    %% Business unit relationships
    C -->|parent of| C
    C -->|has primary| B
    C -->|headed by| K
    
    %% Department relationships
    J -->|belongs to| C
    J -->|has many| D
    
    %% Employee relationships
    K -->|belongs to| J
    
    %% Document relationships
    F -->|related to| C
    
    %% Style
    classDef organization fill:#3a3a5a,stroke:#8a56e5,color:white;
    classDef location fill:#3a3a5a,stroke:#56e5b1,color:white;
    classDef businessUnit fill:#3a3a5a,stroke:#56e5b1,color:white;
    classDef designation fill:#3a3a5a,stroke:#8a56e5,color:white;
    classDef policy fill:#3a3a5a,stroke:#e556b1,color:white;
    classDef document fill:#3a3a5a,stroke:#56e5b1,color:white;
    classDef junction fill:#3a3a5a,stroke:#e55656,color:white;
    classDef department fill:#3a3a5a,stroke:#8a56e5,color:white;
    classDef employee fill:#3a3a5a,stroke:#56e5b1,color:white;
    
    class A organization;
    class B location;
    class C businessUnit;
    class D designation;
    class E policy;
    class F document;
    class G,H,I junction;
    class J department;
    class K employee;
```

This visual representation shows the organization module's database schema in a style similar to ChartDB, with tables represented as nodes and relationships as connecting lines. The color scheme and layout are designed to match the provided reference image.
