from django.db import models
from django.core.validators import MinValueValidator
from organization_app.models.organization import Organization
from employees_app.models.employee import Employee


class Payroll(models.Model):
    """
    Model to represent a payroll run for a specific period.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    name = models.CharField(max_length=100)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='payrolls')
    start_date = models.DateField()
    end_date = models.DateField()
    payment_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    total_earnings = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_deductions = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    net_payable = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    remarks = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='payrolls_created')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='payrolls_approved')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-payment_date']
        verbose_name = 'Payroll'
        verbose_name_plural = 'Payrolls'
    
    def __str__(self):
        return f"{self.name} ({self.start_date} to {self.end_date})"


class PayrollItem(models.Model):
    """
    Model to represent an individual employee's payroll calculation for a specific payroll run.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('calculated', 'Calculated'),
        ('approved', 'Approved'),
        ('paid', 'Paid'),
        ('cancelled', 'Cancelled'),
    ]
    
    payroll = models.ForeignKey(Payroll, on_delete=models.CASCADE, related_name='items')
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='payroll_items')
    working_days = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    leave_days = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    lop_days = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text="Leave Without Pay days")
    overtime_hours = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    basic_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    gross_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_deductions = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    net_payable = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['employee__first_name', 'employee__last_name']
        verbose_name = 'Payroll Item'
        verbose_name_plural = 'Payroll Items'
        unique_together = ['payroll', 'employee']
    
    def __str__(self):
        return f"{self.employee} - {self.payroll.name}"


class PayrollItemComponent(models.Model):
    """
    Model to store the breakdown of earnings and deductions for a payroll item.
    """
    COMPONENT_TYPE_CHOICES = [
        ('earning', 'Earning'),
        ('deduction', 'Deduction'),
    ]
    
    payroll_item = models.ForeignKey(PayrollItem, on_delete=models.CASCADE, related_name='components')
    name = models.CharField(max_length=100)
    component_type = models.CharField(max_length=20, choices=COMPONENT_TYPE_CHOICES)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    is_taxable = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['component_type', 'name']
        verbose_name = 'Payroll Item Component'
        verbose_name_plural = 'Payroll Item Components'
    
    def __str__(self):
        return f"{self.payroll_item.employee} - {self.name}: {self.amount}"
