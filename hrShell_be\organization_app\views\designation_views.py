from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from organization_app.models.designation import Designation
from organization_app.serializers.designation_serializer import DesignationSerializer
from organization_app.filters import DesignationFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class DesignationViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Organization Structure'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Organization Structure'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    ViewSet for viewing and editing Designation instances.
    
    Additional actions:
    - by_department: Get designations filtered by department
    - by_level: Get designations filtered by level
    """
    queryset = Designation.objects.all()
    serializer_class = DesignationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = DesignationFilter
    search_fields = ['title', 'description']
    ordering_fields = ['title', 'level', 'created_at']
    
    @action(detail=False, methods=['get'])
    def by_organization(self, request):
        """
        Get designations filtered by organization
        """
        organization_id = request.query_params.get('organization_id')
        if not organization_id:
            return Response(
                {"error": "organization_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        designations = Designation.objects.filter(organization_id=organization_id)
        serializer = self.get_serializer(designations, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_department(self, request):
        """
        Get designations filtered by department
        """
        department_id = request.query_params.get('department_id')
        if not department_id:
            return Response(
                {"error": "department_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        designations = Designation.objects.filter(department_id=department_id)
        serializer = self.get_serializer(designations, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_level(self, request):
        """
        Get designations filtered by level
        """
        level = request.query_params.get('level')
        if not level:
            return Response(
                {"error": "level query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        designations = Designation.objects.filter(level=level)
        serializer = self.get_serializer(designations, many=True)
        return Response(serializer.data)
