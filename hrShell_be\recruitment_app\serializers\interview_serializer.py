from rest_framework import serializers
from recruitment_app.models.interview import Interview, InterviewFeedback, InterviewQuestion
from recruitment_app.serializers.application_serializer import ApplicationSerializer
from employees_app.serializers.employee_serializer import EmployeeSerializer


class InterviewQuestionSerializer(serializers.ModelSerializer):
    """
    Serializer for the InterviewQuestion model.
    """
    class Meta:
        model = InterviewQuestion
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']


class InterviewFeedbackSerializer(serializers.ModelSerializer):
    """
    Serializer for the InterviewFeedback model.
    """
    interviewer_details = EmployeeSerializer(source='interviewer', read_only=True)
    
    class Meta:
        model = InterviewFeedback
        fields = '__all__'
        read_only_fields = ['submitted_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate the interview feedback data.
        """
        # Ensure the interviewer is part of the interview's interviewers
        interview = data.get('interview')
        interviewer = data.get('interviewer')
        
        if interview and interviewer and interviewer not in interview.interviewers.all():
            raise serializers.ValidationError("The interviewer must be part of the interview's interviewers.")
        
        return data


class InterviewSerializer(serializers.ModelSerializer):
    """
    Serializer for the Interview model.
    """
    application_details = ApplicationSerializer(source='application', read_only=True)
    interviewers_details = EmployeeSerializer(source='interviewers', many=True, read_only=True)
    scheduled_by_details = EmployeeSerializer(source='scheduled_by', read_only=True)
    cancelled_by_details = EmployeeSerializer(source='cancelled_by', read_only=True)
    feedback = InterviewFeedbackSerializer(many=True, read_only=True)
    questions = InterviewQuestionSerializer(many=True, read_only=True)
    
    class Meta:
        model = Interview
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
    
    def validate(self, data):
        """
        Validate the interview data.
        """
        # Validate scheduled times
        scheduled_start_time = data.get('scheduled_start_time')
        scheduled_end_time = data.get('scheduled_end_time')
        
        if scheduled_start_time and scheduled_end_time and scheduled_start_time >= scheduled_end_time:
            raise serializers.ValidationError("Scheduled start time must be before scheduled end time.")
        
        # Validate actual times
        actual_start_time = data.get('actual_start_time')
        actual_end_time = data.get('actual_end_time')
        
        if actual_start_time and actual_end_time and actual_start_time >= actual_end_time:
            raise serializers.ValidationError("Actual start time must be before actual end time.")
        
        return data
    
    def create(self, validated_data):
        """
        Create a new interview with interviewers.
        """
        interviewers_data = self.context.get('request').data.get('interviewers', [])
        
        # Remove interviewers from validated data
        if 'interviewers' in validated_data:
            validated_data.pop('interviewers')
        
        interview = Interview.objects.create(**validated_data)
        
        # Add interviewers
        if interviewers_data:
            interview.interviewers.set(interviewers_data)
        
        return interview
    
    def update(self, instance, validated_data):
        """
        Update an interview with interviewers.
        """
        interviewers_data = self.context.get('request').data.get('interviewers', None)
        
        # Remove interviewers from validated data
        if 'interviewers' in validated_data:
            validated_data.pop('interviewers')
        
        # Update interview
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update interviewers if provided
        if interviewers_data is not None:
            instance.interviewers.set(interviewers_data)
        
        return instance
