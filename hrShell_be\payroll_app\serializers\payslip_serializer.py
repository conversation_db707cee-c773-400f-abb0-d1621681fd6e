from rest_framework import serializers
from payroll_app.models.payslip import Payslip
from payroll_app.models.payroll import PayrollItemComponent


class PayslipSerializer(serializers.ModelSerializer):
    employee_name = serializers.SerializerMethodField()
    payroll_name = serializers.SerializerMethodField()
    earnings = serializers.SerializerMethodField()
    deductions = serializers.SerializerMethodField()
    
    class Meta:
        model = Payslip
        fields = [
            'id', 'payroll_item', 'employee', 'employee_name', 'payroll_name',
            'payslip_number', 'month', 'year', 'generation_date', 'basic_salary',
            'gross_earnings', 'total_deductions', 'net_payable', 'pdf_file',
            'status', 'email_sent', 'email_sent_at', 'remarks', 'earnings',
            'deductions', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'earnings', 'deductions']
    
    def get_employee_name(self, obj):
        return f"{obj.employee.first_name} {obj.employee.last_name}" if obj.employee else None
    
    def get_payroll_name(self, obj):
        return obj.payroll_item.payroll.name if obj.payroll_item and obj.payroll_item.payroll else None
    
    def get_earnings(self, obj):
        if not obj.payroll_item:
            return []
        
        earnings = PayrollItemComponent.objects.filter(
            payroll_item=obj.payroll_item,
            component_type='earning'
        ).values('name', 'amount')
        
        return earnings
    
    def get_deductions(self, obj):
        if not obj.payroll_item:
            return []
        
        deductions = PayrollItemComponent.objects.filter(
            payroll_item=obj.payroll_item,
            component_type='deduction'
        ).values('name', 'amount')
        
        return deductions
