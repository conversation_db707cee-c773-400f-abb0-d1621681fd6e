# Organization Model

The Organization model represents a company in the system.

## Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `name` | String | Name of the organization | Yes (unique) |
| `logo` | Image | Organization logo | No |
| `registration_number` | String | Registration number | No |
| `gst_number` | String | GST number | No |
| `pan_number` | String | PAN number | No |
| `ein_number` | String | EIN number | No |
| `business_type` | String | Type of business (choices: llc, private_limited, public_limited, partnership, sole_proprietorship, corporation, other) | Yes (default: private_limited) |
| `establishment_date` | Date | Date of establishment | No |
| `industry_sector` | String | Industry sector | No |
| `default_currency` | String | Default currency (choices: usd, eur, gbp, inr, jpy, cad, aud, cny) | Yes (default: usd) |
| `default_timezone` | String | Default timezone | Yes (default: UTC) |
| `fiscal_year_start_month` | Integer | Month when fiscal year starts (1-12) | Yes (default: 1) |
| `fiscal_year_start_day` | Integer | Day when fiscal year starts (1-31) | Yes (default: 1) |
| `website` | URL | Organization website | No |
| `email` | Email | Organization email | No |
| `phone` | String | Organization phone number | No |
| `status` | String | Status of the organization (choices: active, inactive) | Yes (default: active) |
| `custom_fields` | JSON | Custom fields for flexibility | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

## Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `locations` | Location | The locations/branches of the organization |
| `business_units` | BusinessUnit | The business units/divisions of the organization |
| `designations` | Designation | The job titles/roles in the organization |
| `policies` | OrganizationPolicy | The policies of the organization |
| `documents` | OrganizationDocument | The documents of the organization |

## Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the name of the organization |

## Example

```python
organization = Organization.objects.create(
    name="Acme Corporation",
    business_type="private_limited",
    establishment_date="2000-01-01",
    industry_sector="Technology",
    default_currency="usd",
    default_timezone="UTC",
    status="active"
)
```
