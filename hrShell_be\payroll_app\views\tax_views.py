from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from payroll_app.models.tax import TaxSlab, EmployeeTaxDeclaration
from payroll_app.serializers.tax_serializer import TaxSlabSerializer, EmployeeTaxDeclarationSerializer
from payroll_app.filters.payroll_filters import TaxSlabFilter, EmployeeTaxDeclarationFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class TaxSlabViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for tax slabs.
    """
    queryset = TaxSlab.objects.all()
    serializer_class = TaxSlabSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = TaxSlabFilter
    search_fields = ['name', 'financial_year']
    ordering_fields = ['financial_year', 'min_income', 'tax_rate', 'created_at']
    ordering = ['financial_year', 'min_income']

    @action(detail=False, methods=['get'])
    def active(self, request):
        """
        Get all active tax slabs.
        """
        active = TaxSlab.objects.filter(is_active=True)
        page = self.paginate_queryset(active)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(active, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def organization(self, request, organization_pk=None):
        """
        Get all tax slabs for a specific organization.
        """
        slabs = TaxSlab.objects.filter(organization_id=organization_pk)
        page = self.paginate_queryset(slabs)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(slabs, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def financial_year(self, request):
        """
        Get all tax slabs for a specific financial year.
        """
        financial_year = request.query_params.get('financial_year')

        if not financial_year:
            return Response(
                {"detail": "Financial year parameter is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        slabs = TaxSlab.objects.filter(financial_year=financial_year)
        page = self.paginate_queryset(slabs)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(slabs, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def gender(self, request, gender=None):
        """
        Get all tax slabs for a specific gender.
        """
        slabs = TaxSlab.objects.filter(gender=gender)
        page = self.paginate_queryset(slabs)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(slabs, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def age_group(self, request, age_group=None):
        """
        Get all tax slabs for a specific age group.
        """
        slabs = TaxSlab.objects.filter(age_group=age_group)
        page = self.paginate_queryset(slabs)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(slabs, many=True)
        return Response(serializer.data)


class EmployeeTaxDeclarationViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Compensation & Benefits'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for employee tax declarations.
    """
    queryset = EmployeeTaxDeclaration.objects.all()
    serializer_class = EmployeeTaxDeclarationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = EmployeeTaxDeclarationFilter
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__email', 'declaration_name']
    ordering_fields = ['employee__first_name', 'financial_year', 'declaration_type', 'declared_amount', 'status', 'created_at']
    ordering = ['financial_year', 'employee__first_name', 'employee__last_name', 'declaration_type']

    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all declarations with a specific status.
        """
        declarations = EmployeeTaxDeclaration.objects.filter(status=status_value)
        page = self.paginate_queryset(declarations)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(declarations, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def employee(self, request, employee_pk=None):
        """
        Get all declarations for a specific employee.
        """
        declarations = EmployeeTaxDeclaration.objects.filter(employee_id=employee_pk)
        page = self.paginate_queryset(declarations)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(declarations, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def financial_year(self, request):
        """
        Get all declarations for a specific financial year.
        """
        financial_year = request.query_params.get('financial_year')

        if not financial_year:
            return Response(
                {"detail": "Financial year parameter is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        declarations = EmployeeTaxDeclaration.objects.filter(financial_year=financial_year)
        page = self.paginate_queryset(declarations)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(declarations, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def declaration_type(self, request, declaration_type=None):
        """
        Get all declarations of a specific type.
        """
        declarations = EmployeeTaxDeclaration.objects.filter(declaration_type=declaration_type)
        page = self.paginate_queryset(declarations)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(declarations, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """
        Verify a specific declaration.
        """
        declaration = self.get_object()

        if declaration.status not in ['declared', 'submitted']:
            return Response(
                {"detail": "Only declared or submitted declarations can be verified."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update declaration status
        declaration.status = 'verified'
        declaration.verification_date = timezone.now().date()

        # Set the verified amount if provided
        verified_amount = request.data.get('verified_amount')
        if verified_amount is not None:
            declaration.verified_amount = verified_amount
        else:
            declaration.verified_amount = declaration.declared_amount

        # Set the verifier if provided
        verifier_id = request.data.get('verified_by')
        if verifier_id:
            declaration.verified_by_id = verifier_id

        declaration.save()

        serializer = self.get_serializer(declaration)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """
        Reject a specific declaration.
        """
        declaration = self.get_object()

        if declaration.status not in ['declared', 'submitted']:
            return Response(
                {"detail": "Only declared or submitted declarations can be rejected."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update declaration status
        declaration.status = 'rejected'

        # Set the remarks if provided
        remarks = request.data.get('remarks')
        if remarks:
            declaration.remarks = remarks

        # Set the verifier if provided
        verifier_id = request.data.get('verified_by')
        if verifier_id:
            declaration.verified_by_id = verifier_id

        declaration.save()

        serializer = self.get_serializer(declaration)
        return Response(serializer.data)
