from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from recruitment_app.models.application import Application, ApplicationStageHistory
from recruitment_app.models.job_posting import JobPosting
from recruitment_app.serializers.application_serializer import ApplicationSerializer, ApplicationStageHistorySerializer
from recruitment_app.filters.recruitment_filters import ApplicationFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class ApplicationViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Recruitment & Hiring'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for applications.
    """
    queryset = Application.objects.all()
    serializer_class = ApplicationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ApplicationFilter
    search_fields = ['candidate__first_name', 'candidate__last_name', 'candidate__email', 'job_posting__title', 'notes', 'internal_feedback']
    ordering_fields = ['application_date', 'status', 'current_stage', 'rating', 'last_status_change']
    ordering = ['-application_date']
    
    def perform_create(self, serializer):
        """
        Create a new application and increment the application count for the job posting.
        """
        application = serializer.save()
        
        # Increment application count for the job posting
        job_posting = application.job_posting
        job_posting.application_count += 1
        job_posting.save()
    
    @action(detail=False, methods=['get'])
    def status(self, request, status_value=None):
        """
        Get all applications with a specific status.
        """
        applications = Application.objects.filter(status=status_value)
        page = self.paginate_queryset(applications)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(applications, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stage(self, request, stage_value=None):
        """
        Get all applications at a specific stage.
        """
        applications = Application.objects.filter(current_stage=stage_value)
        page = self.paginate_queryset(applications)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(applications, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def job_posting(self, request, job_posting_pk=None):
        """
        Get all applications for a specific job posting.
        """
        applications = Application.objects.filter(job_posting_id=job_posting_pk)
        page = self.paginate_queryset(applications)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(applications, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def candidate(self, request, candidate_pk=None):
        """
        Get all applications for a specific candidate.
        """
        applications = Application.objects.filter(candidate_id=candidate_pk)
        page = self.paginate_queryset(applications)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(applications, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def assigned_to(self, request, employee_pk=None):
        """
        Get all applications assigned to a specific employee.
        """
        applications = Application.objects.filter(assigned_to_id=employee_pk)
        page = self.paginate_queryset(applications)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(applications, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def stage_history(self, request, pk=None):
        """
        Get the stage history for a specific application.
        """
        application = self.get_object()
        history = ApplicationStageHistory.objects.filter(application=application)
        
        serializer = ApplicationStageHistorySerializer(history, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def change_stage(self, request, pk=None):
        """
        Change the stage of an application.
        """
        application = self.get_object()
        
        # Validate required fields
        required_fields = ['to_stage', 'to_status']
        for field in required_fields:
            if field not in request.data:
                return Response(
                    {"detail": f"Field '{field}' is required."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Get current stage and status
        from_stage = application.current_stage
        from_status = application.status
        
        # Get new stage and status
        to_stage = request.data.get('to_stage')
        to_status = request.data.get('to_status')
        
        # Update application
        application.current_stage = to_stage
        application.status = to_status
        application.last_status_change = timezone.now()
        application.save()
        
        # Create stage history
        history = ApplicationStageHistory.objects.create(
            application=application,
            from_stage=from_stage,
            to_stage=to_stage,
            from_status=from_status,
            to_status=to_status,
            changed_by_id=request.data.get('changed_by'),
            notes=request.data.get('notes')
        )
        
        serializer = self.get_serializer(application)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def shortlist(self, request, pk=None):
        """
        Shortlist an application.
        """
        application = self.get_object()
        
        if application.is_shortlisted:
            return Response(
                {"detail": "Application is already shortlisted."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update application
        application.is_shortlisted = True
        application.status = 'shortlisted'
        application.current_stage = 'resume_screening'
        application.last_status_change = timezone.now()
        application.save()
        
        # Create stage history
        ApplicationStageHistory.objects.create(
            application=application,
            from_stage=application.current_stage,
            to_stage='resume_screening',
            from_status=application.status,
            to_status='shortlisted',
            changed_by_id=request.data.get('changed_by'),
            notes=request.data.get('notes', 'Application shortlisted')
        )
        
        serializer = self.get_serializer(application)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """
        Reject an application.
        """
        application = self.get_object()
        
        if application.status == 'rejected':
            return Response(
                {"detail": "Application is already rejected."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update application
        application.status = 'rejected'
        application.rejection_reason = request.data.get('rejection_reason')
        application.rejection_stage = application.current_stage
        application.rejected_by_id = request.data.get('rejected_by')
        application.rejection_date = timezone.now()
        application.last_status_change = timezone.now()
        application.save()
        
        # Create stage history
        ApplicationStageHistory.objects.create(
            application=application,
            from_stage=application.current_stage,
            to_stage=application.current_stage,
            from_status=application.status,
            to_status='rejected',
            changed_by_id=request.data.get('rejected_by'),
            notes=request.data.get('rejection_reason', 'Application rejected')
        )
        
        serializer = self.get_serializer(application)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def withdraw(self, request, pk=None):
        """
        Withdraw an application.
        """
        application = self.get_object()
        
        if application.status == 'withdrawn':
            return Response(
                {"detail": "Application is already withdrawn."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update application
        application.status = 'withdrawn'
        application.withdrawal_reason = request.data.get('withdrawal_reason')
        application.withdrawal_date = timezone.now()
        application.last_status_change = timezone.now()
        application.save()
        
        # Create stage history
        ApplicationStageHistory.objects.create(
            application=application,
            from_stage=application.current_stage,
            to_stage=application.current_stage,
            from_status=application.status,
            to_status='withdrawn',
            changed_by_id=request.data.get('changed_by'),
            notes=request.data.get('withdrawal_reason', 'Application withdrawn')
        )
        
        serializer = self.get_serializer(application)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """
        Assign an application to an employee.
        """
        application = self.get_object()
        
        # Validate required fields
        if 'assigned_to' not in request.data:
            return Response(
                {"detail": "Field 'assigned_to' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update application
        application.assigned_to_id = request.data.get('assigned_to')
        application.save()
        
        serializer = self.get_serializer(application)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def rate(self, request, pk=None):
        """
        Rate an application.
        """
        application = self.get_object()
        
        # Validate required fields
        if 'rating' not in request.data:
            return Response(
                {"detail": "Field 'rating' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update application
        application.rating = request.data.get('rating')
        application.save()
        
        serializer = self.get_serializer(application)
        return Response(serializer.data)
