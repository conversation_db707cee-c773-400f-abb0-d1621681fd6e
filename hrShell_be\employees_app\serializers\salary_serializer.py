from rest_framework import serializers
from employees_app.models.salary import Salary


class SalarySerializer(serializers.ModelSerializer):
    """
    Serializer for the Salary model
    """
    employee_name = serializers.ReadOnlyField(source='employee.full_name')
    payment_method_display = serializers.ReadOnlyField(source='get_payment_method_display')

    class Meta:
        model = Salary
        fields = [
            'id', 'employee', 'employee_name', 'basic', 'allowance',
            'deduction', 'net_amount_payable', 'payment_method', 'payment_method_display',
            'bank_account_details', 'salary_effective_date'
        ]
        read_only_fields = ['net_amount_payable']

    def validate(self, data):
        """
        Validate that basic, allowance, and deduction are positive
        """
        if 'basic' in data and data['basic'] < 0:
            raise serializers.ValidationError("Basic salary cannot be negative")

        if 'allowance' in data and data['allowance'] < 0:
            raise serializers.ValidationError("Allowance cannot be negative")

        if 'deduction' in data and data['deduction'] < 0:
            raise serializers.ValidationError("Deduction cannot be negative")

        return data
