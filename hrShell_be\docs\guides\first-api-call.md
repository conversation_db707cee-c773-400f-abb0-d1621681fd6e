# Making Your First API Call

This guide will help you make your first API call to the HR Management API.

## Prerequisites

Before making API calls, make sure you have:

- The API running (locally or on a server)
- A tool for making HTTP requests (like curl, Postman, or a programming language with HTTP capabilities)
- Authentication credentials (if required)

## Step 1: Obtain an Authentication Token

Most endpoints in the HR Management API require authentication. To authenticate, you need to obtain a JWT token.

### Using curl

```bash
curl -X POST http://localhost:8000/api/token/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'
```

### Using Postman

1. Create a new POST request to `http://localhost:8000/api/token/`
2. Set the Content-Type header to `application/json`
3. In the request body, select "raw" and "JSON", then enter:
   ```json
   {
     "username": "your_username",
     "password": "your_password"
   }
   ```
4. Click "Send"

### Response

```json
{
  "access": "your_access_token",
  "refresh": "your_refresh_token"
}
```

Save the access token for use in subsequent requests.

## Step 2: Make a GET Request to List Employees

Now that you have an authentication token, you can make a request to list employees.

### Using curl

```bash
curl -X GET http://localhost:8000/api/v1/employees/ \
  -H "Authorization: Bearer your_access_token"
```

### Using Postman

1. Create a new GET request to `http://localhost:8000/api/v1/employees/`
2. Add an Authorization header: `Bearer your_access_token`
3. Click "Send"

### Response

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "full_name": "John Doe",
      "email": "<EMAIL>",
      "position": "Software Engineer",
      "department_name": "Engineering",
      "status": "active",
      "profile_picture": "http://localhost:8000/media/employee_images/john_doe.jpg"
    },
    {
      "id": 2,
      "full_name": "Jane Smith",
      "email": "<EMAIL>",
      "position": "Product Manager",
      "department_name": "Product",
      "status": "active",
      "profile_picture": null
    }
  ]
}
```

## Step 3: Create a New Employee

Let's create a new employee.

### Using curl

```bash
curl -X POST http://localhost:8000/api/v1/employees/ \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "Bob",
    "last_name": "Johnson",
    "email": "<EMAIL>",
    "phone": "+1122334455",
    "position": "Designer",
    "department": 3,
    "hire_date": "2023-03-01",
    "gender": "male",
    "status": "active"
  }'
```

### Using Postman

1. Create a new POST request to `http://localhost:8000/api/v1/employees/`
2. Add an Authorization header: `Bearer your_access_token`
3. Set the Content-Type header to `application/json`
4. In the request body, select "raw" and "JSON", then enter:
   ```json
   {
     "first_name": "Bob",
     "last_name": "Johnson",
     "email": "<EMAIL>",
     "phone": "+1122334455",
     "position": "Designer",
     "department": 3,
     "hire_date": "2023-03-01",
     "gender": "male",
     "status": "active"
   }
   ```
5. Click "Send"

### Response

```json
{
  "id": 3,
  "first_name": "Bob",
  "last_name": "Johnson",
  "full_name": "Bob Johnson",
  "email": "<EMAIL>",
  "phone": "+1122334455",
  "position": "Designer",
  "department": 3,
  "department_name": "Design",
  "hire_date": "2023-03-01",
  "gender": "male",
  "status": "active",
  "profile_picture": null,
  "resume": null,
  "contract": null,
  "created_at": "2023-03-01T12:00:00Z",
  "updated_at": "2023-03-01T12:00:00Z"
}
```

## Step 4: Get a Specific Employee

Let's retrieve the employee we just created.

### Using curl

```bash
curl -X GET http://localhost:8000/api/v1/employees/3/ \
  -H "Authorization: Bearer your_access_token"
```

### Using Postman

1. Create a new GET request to `http://localhost:8000/api/v1/employees/3/`
2. Add an Authorization header: `Bearer your_access_token`
3. Click "Send"

### Response

```json
{
  "id": 3,
  "first_name": "Bob",
  "last_name": "Johnson",
  "full_name": "Bob Johnson",
  "email": "<EMAIL>",
  "phone": "+1122334455",
  "position": "Designer",
  "department": 3,
  "department_name": "Design",
  "hire_date": "2023-03-01",
  "gender": "male",
  "status": "active",
  "profile_picture": null,
  "resume": null,
  "contract": null,
  "created_at": "2023-03-01T12:00:00Z",
  "updated_at": "2023-03-01T12:00:00Z"
}
```

## Step 5: Update an Employee

Let's update the employee's position.

### Using curl

```bash
curl -X PATCH http://localhost:8000/api/v1/employees/3/ \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -d '{
    "position": "Senior Designer"
  }'
```

### Using Postman

1. Create a new PATCH request to `http://localhost:8000/api/v1/employees/3/`
2. Add an Authorization header: `Bearer your_access_token`
3. Set the Content-Type header to `application/json`
4. In the request body, select "raw" and "JSON", then enter:
   ```json
   {
     "position": "Senior Designer"
   }
   ```
5. Click "Send"

### Response

```json
{
  "id": 3,
  "first_name": "Bob",
  "last_name": "Johnson",
  "full_name": "Bob Johnson",
  "email": "<EMAIL>",
  "phone": "+1122334455",
  "position": "Senior Designer",
  "department": 3,
  "department_name": "Design",
  "hire_date": "2023-03-01",
  "gender": "male",
  "status": "active",
  "profile_picture": null,
  "resume": null,
  "contract": null,
  "created_at": "2023-03-01T12:00:00Z",
  "updated_at": "2023-03-01T13:00:00Z"
}
```

## Step 6: Delete an Employee

Let's delete the employee.

### Using curl

```bash
curl -X DELETE http://localhost:8000/api/v1/employees/3/ \
  -H "Authorization: Bearer your_access_token"
```

### Using Postman

1. Create a new DELETE request to `http://localhost:8000/api/v1/employees/3/`
2. Add an Authorization header: `Bearer your_access_token`
3. Click "Send"

### Response

The server will respond with a 204 No Content status code, indicating that the employee was successfully deleted.

## Next Steps

- [Learn about authentication and authorization](authentication.md)
- [Learn about working with employees](working-with-employees.md)
- [Learn about working with departments](working-with-departments.md)
