from django.db import models
from organization_app.models.organization import Organization


class Skill(models.Model):
    """
    Model to represent skills required for jobs and possessed by candidates.
    """
    SKILL_TYPE_CHOICES = [
        ('technical', 'Technical'),
        ('soft', 'Soft Skill'),
        ('language', 'Language'),
        ('certification', 'Certification'),
        ('other', 'Other'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='skills')
    skill_type = models.CharField(max_length=20, choices=SKILL_TYPE_CHOICES)
    description = models.TextField(blank=True, null=True)
    
    # Additional Information
    is_active = models.BooleanField(default=True)
    parent_skill = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='child_skills')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = 'Skill'
        verbose_name_plural = 'Skills'
    
    def __str__(self):
        return self.name
