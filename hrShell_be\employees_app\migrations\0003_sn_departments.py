# Generated by Django 5.1.7 on 2025-03-29 10:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees_app', '0002_alter_sn_employees_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='sn_departments',
            fields=[
                ('department_id', models.AutoField(db_column='department_id', primary_key=True, serialize=False)),
                ('department_name', models.CharField(db_column='department_name', max_length=100)),
                ('number_of_employees', models.IntegerField(db_column='number_of_employees')),
                ('key_functions', models.<PERSON><PERSON><PERSON><PERSON>(db_column='key_functions', max_length=100)),
                ('location', models.<PERSON><PERSON><PERSON><PERSON>(db_column='location', max_length=100)),
            ],
            options={
                'db_table': 'sn_departments',
                'managed': True,
            },
        ),
    ]
