"""
<PERSON><PERSON>t to process documents and store their embeddings in the vector database.
This script demonstrates how to use the vector_operations.py module.

Usage:
    python process_documents.py [--pdf PDF_PATH] [--text TEXT_FILE] [--query SEARCH_QUERY]

Examples:
    # Process a PDF file
    python process_documents.py --pdf path/to/document.pdf
    
    # Process a text file
    python process_documents.py --text path/to/document.txt
    
    # Search for similar content
    python process_documents.py --query "employee onboarding process"
    
    # Process a PDF and then search
    python process_documents.py --pdf path/to/document.pdf --query "employee onboarding process"
"""

import os
import sys
import argparse
import logging
import django

# Set up Django environment
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hrShell_be.settings')
django.setup()

from common.utils.vector_operations import VectorProcessor, VectorSearch

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def process_pdf(pdf_path, metadata=None):
    """Process a PDF file and store its embeddings"""
    try:
        processor = VectorProcessor()
        num_pages = processor.process_pdf(pdf_path, metadata=metadata or {"source": os.path.basename(pdf_path)})
        logger.info(f"Processed {num_pages} pages from {pdf_path}")
        return num_pages
    except Exception as e:
        logger.error(f"Error processing PDF: {str(e)}")
        return 0

def process_text(text_path, metadata=None):
    """Process a text file and store its embeddings"""
    try:
        with open(text_path, 'r', encoding='utf-8') as f:
            text = f.read()
        
        processor = VectorProcessor()
        result = processor.process_text(text, metadata=metadata or {"source": os.path.basename(text_path)})
        logger.info(f"Processed text from {text_path}")
        return result
    except Exception as e:
        logger.error(f"Error processing text: {str(e)}")
        return 0

def search_similar(query, limit=5):
    """Search for similar content"""
    try:
        search = VectorSearch()
        results = search.search(query, limit=limit)
        
        if not results:
            logger.info(f"No results found for query: {query}")
            return []
        
        logger.info(f"Found {len(results)} results for query: {query}")
        return results
    except Exception as e:
        logger.error(f"Error searching: {str(e)}")
        return []

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Process documents and store their embeddings")
    parser.add_argument("--pdf", help="Path to a PDF file to process")
    parser.add_argument("--text", help="Path to a text file to process")
    parser.add_argument("--query", help="Search query")
    parser.add_argument("--limit", type=int, default=5, help="Maximum number of search results")
    
    args = parser.parse_args()
    
    if not any([args.pdf, args.text, args.query]):
        parser.print_help()
        return
    
    # Process PDF if provided
    if args.pdf:
        if not os.path.exists(args.pdf):
            logger.error(f"PDF file not found: {args.pdf}")
        else:
            process_pdf(args.pdf)
    
    # Process text if provided
    if args.text:
        if not os.path.exists(args.text):
            logger.error(f"Text file not found: {args.text}")
        else:
            process_text(args.text)
    
    # Search if query provided
    if args.query:
        results = search_similar(args.query, limit=args.limit)
        
        if results:
            print("\nSearch results:")
            for i, result in enumerate(results):
                print(f"\nResult {i+1}:")
                print(f"Content: {result['content'][:200]}...")
                if result.get('metadata'):
                    print(f"Metadata: {result['metadata']}")
                if 'distance' in result:
                    print(f"Distance: {result['distance']}")

if __name__ == "__main__":
    main()
