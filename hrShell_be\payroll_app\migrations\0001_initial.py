# Generated by Django 5.1.7 on 2025-05-10 09:29

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employees_app', '0012_employeehike_status_alter_employeehike_approved_by'),
        ('organization_app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Bonus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bonus_type', models.CharField(choices=[('performance', 'Performance Bonus'), ('annual', 'Annual Bonus'), ('festival', 'Festival Bonus'), ('incentive', 'Sales Incentive'), ('joining', 'Joining Bonus'), ('retention', 'Retention Bonus'), ('referral', 'Referral Bonus'), ('other', 'Other')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('payment_date', models.DateField()),
                ('reference_period_start', models.DateField(blank=True, null=True)),
                ('reference_period_end', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('paid', 'Paid'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_taxable', models.BooleanField(default=True)),
                ('is_prorated', models.BooleanField(default=False)),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bonuses_approved', to='employees_app.employee')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bonuses', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Bonus',
                'verbose_name_plural': 'Bonuses',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='BonusBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('bonus_type', models.CharField(choices=[('performance', 'Performance Bonus'), ('annual', 'Annual Bonus'), ('festival', 'Festival Bonus'), ('incentive', 'Sales Incentive'), ('joining', 'Joining Bonus'), ('retention', 'Retention Bonus'), ('referral', 'Referral Bonus'), ('other', 'Other')], max_length=20)),
                ('payment_date', models.DateField()),
                ('reference_period_start', models.DateField(blank=True, null=True)),
                ('reference_period_end', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('processing', 'Processing'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bonus_batches_approved', to='employees_app.employee')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bonus_batches_created', to='employees_app.employee')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bonus_batches', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Bonus Batch',
                'verbose_name_plural': 'Bonus Batches',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeSalary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_from', models.DateField()),
                ('effective_to', models.DateField(blank=True, null=True)),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('gross_salary', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('net_salary', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('is_active', models.BooleanField(default=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payroll_salaries', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Employee Salary',
                'verbose_name_plural': 'Employee Salaries',
                'ordering': ['-effective_from'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeTaxDeclaration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('financial_year', models.CharField(help_text='Format: YYYY-YY (e.g., 2023-24)', max_length=10)),
                ('declaration_type', models.CharField(choices=[('section_80c', 'Section 80C'), ('section_80d', 'Section 80D'), ('section_80e', 'Section 80E'), ('section_80g', 'Section 80G'), ('section_24', 'Section 24 (Home Loan Interest)'), ('hra', 'HRA Exemption'), ('lta', 'LTA Exemption'), ('other', 'Other Exemption')], max_length=20)),
                ('declaration_name', models.CharField(max_length=100)),
                ('declared_amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('proof_document', models.FileField(blank=True, null=True, upload_to='tax_proofs/%Y/%m/')),
                ('verified_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('status', models.CharField(choices=[('declared', 'Declared'), ('submitted', 'Proof Submitted'), ('verified', 'Verified'), ('rejected', 'Rejected')], default='declared', max_length=20)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('verification_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tax_declarations', to='employees_app.employee')),
                ('verified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tax_declarations_verified', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Employee Tax Declaration',
                'verbose_name_plural': 'Employee Tax Declarations',
                'ordering': ['financial_year', 'employee__first_name', 'employee__last_name', 'declaration_type'],
            },
        ),
        migrations.CreateModel(
            name='Loan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('loan_type', models.CharField(choices=[('personal', 'Personal Loan'), ('home', 'Home Loan'), ('vehicle', 'Vehicle Loan'), ('education', 'Education Loan'), ('medical', 'Medical Loan'), ('advance', 'Salary Advance'), ('other', 'Other')], max_length=20)),
                ('loan_amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('interest_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)])),
                ('term_months', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('emi_amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('remaining_amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('status', models.CharField(choices=[('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('active', 'Active'), ('closed', 'Closed'), ('defaulted', 'Defaulted')], default='pending', max_length=20)),
                ('purpose', models.TextField(blank=True, null=True)),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='loans_approved', to='employees_app.employee')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loans', to='employees_app.employee')),
            ],
            options={
                'verbose_name': 'Loan',
                'verbose_name_plural': 'Loans',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Payroll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('payment_date', models.DateField()),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('processing', 'Processing'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('net_payable', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payrolls_approved', to='employees_app.employee')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payrolls_created', to='employees_app.employee')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payrolls', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Payroll',
                'verbose_name_plural': 'Payrolls',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='BankTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference_number', models.CharField(max_length=50, unique=True)),
                ('transfer_date', models.DateField()),
                ('bank_name', models.CharField(max_length=100)),
                ('account_number', models.CharField(max_length=50)),
                ('transfer_type', models.CharField(choices=[('salary', 'Salary'), ('bonus', 'Bonus'), ('reimbursement', 'Reimbursement'), ('other', 'Other')], default='salary', max_length=20)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0)])),
                ('total_employees', models.PositiveIntegerField(default=0)),
                ('file_format', models.CharField(default='csv', max_length=20)),
                ('transfer_file', models.FileField(blank=True, null=True, upload_to='bank_transfers/%Y/%m/')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('generated', 'Generated'), ('sent', 'Sent to Bank'), ('processed', 'Processed'), ('completed', 'Completed'), ('failed', 'Failed')], default='draft', max_length=20)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bank_transfers', to='organization_app.organization')),
                ('payroll', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='bank_transfers', to='payroll_app.payroll')),
            ],
            options={
                'verbose_name': 'Bank Transfer',
                'verbose_name_plural': 'Bank Transfers',
                'ordering': ['-transfer_date'],
            },
        ),
        migrations.CreateModel(
            name='PayrollItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('working_days', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('leave_days', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('lop_days', models.DecimalField(decimal_places=2, default=0, help_text='Leave Without Pay days', max_digits=5)),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('gross_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('net_payable', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('calculated', 'Calculated'), ('approved', 'Approved'), ('paid', 'Paid'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payroll_items', to='employees_app.employee')),
                ('payroll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='payroll_app.payroll')),
            ],
            options={
                'verbose_name': 'Payroll Item',
                'verbose_name_plural': 'Payroll Items',
                'ordering': ['employee__first_name', 'employee__last_name'],
                'unique_together': {('payroll', 'employee')},
            },
        ),
        migrations.CreateModel(
            name='PayrollItemComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('component_type', models.CharField(choices=[('earning', 'Earning'), ('deduction', 'Deduction')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('is_taxable', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('payroll_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components', to='payroll_app.payrollitem')),
            ],
            options={
                'verbose_name': 'Payroll Item Component',
                'verbose_name_plural': 'Payroll Item Components',
                'ordering': ['component_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SalaryComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('component_type', models.CharField(choices=[('earning', 'Earning'), ('deduction', 'Deduction')], max_length=20)),
                ('calculation_type', models.CharField(choices=[('fixed', 'Fixed Amount'), ('percentage', 'Percentage of Basic'), ('percentage_gross', 'Percentage of Gross'), ('formula', 'Custom Formula')], max_length=20)),
                ('value', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('formula', models.TextField(blank=True, help_text='Custom formula for calculation', null=True)),
                ('is_taxable', models.BooleanField(default=True)),
                ('is_fixed', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salary_components', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Salary Component',
                'verbose_name_plural': 'Salary Components',
                'ordering': ['component_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeSalaryComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee_salary', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components', to='payroll_app.employeesalary')),
                ('salary_component', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_values', to='payroll_app.salarycomponent')),
            ],
            options={
                'verbose_name': 'Employee Salary Component',
                'verbose_name_plural': 'Employee Salary Components',
                'ordering': ['salary_component__component_type', 'salary_component__name'],
            },
        ),
        migrations.CreateModel(
            name='SalaryRevision',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('revision_date', models.DateField()),
                ('revision_type', models.CharField(choices=[('hike', 'Salary Hike'), ('promotion', 'Promotion'), ('adjustment', 'Adjustment'), ('annual_revision', 'Annual Revision'), ('other', 'Other')], max_length=20)),
                ('percentage_increase', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0)])),
                ('amount_increase', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('approval_date', models.DateField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='salary_revisions_approved', to='employees_app.employee')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salary_revisions', to='employees_app.employee')),
                ('new_salary', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revisions_to', to='payroll_app.employeesalary')),
                ('previous_salary', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revisions_from', to='payroll_app.employeesalary')),
            ],
            options={
                'verbose_name': 'Salary Revision',
                'verbose_name_plural': 'Salary Revisions',
                'ordering': ['-revision_date'],
            },
        ),
        migrations.CreateModel(
            name='SalaryStructure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('salary_type', models.CharField(choices=[('monthly', 'Monthly'), ('hourly', 'Hourly'), ('daily', 'Daily')], default='monthly', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('components', models.ManyToManyField(related_name='salary_structures', to='payroll_app.salarycomponent')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salary_structures', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Salary Structure',
                'verbose_name_plural': 'Salary Structures',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='employeesalary',
            name='salary_structure',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_salaries', to='payroll_app.salarystructure'),
        ),
        migrations.CreateModel(
            name='TaxSlab',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('financial_year', models.CharField(help_text='Format: YYYY-YY (e.g., 2023-24)', max_length=10)),
                ('name', models.CharField(max_length=100)),
                ('min_income', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('max_income', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, validators=[django.core.validators.MinValueValidator(0)])),
                ('tax_rate', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0)])),
                ('surcharge_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)])),
                ('cess_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)])),
                ('gender', models.CharField(choices=[('all', 'All'), ('male', 'Male'), ('female', 'Female'), ('other', 'Other')], default='all', max_length=10)),
                ('age_group', models.CharField(choices=[('all', 'All Ages'), ('below_60', 'Below 60 Years'), ('60_to_80', '60 to 80 Years'), ('above_80', 'Above 80 Years')], default='all', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tax_slabs', to='organization_app.organization')),
            ],
            options={
                'verbose_name': 'Tax Slab',
                'verbose_name_plural': 'Tax Slabs',
                'ordering': ['financial_year', 'min_income'],
            },
        ),
        migrations.CreateModel(
            name='LoanInstallment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('installment_number', models.PositiveIntegerField()),
                ('due_date', models.DateField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('principal_amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('interest_amount', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('missed', 'Missed'), ('partial', 'Partially Paid')], default='pending', max_length=20)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='installments', to='payroll_app.loan')),
            ],
            options={
                'verbose_name': 'Loan Installment',
                'verbose_name_plural': 'Loan Installments',
                'ordering': ['loan', 'installment_number'],
                'unique_together': {('loan', 'installment_number')},
            },
        ),
        migrations.CreateModel(
            name='Payslip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payslip_number', models.CharField(max_length=50, unique=True)),
                ('month', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MinValueValidator(12)])),
                ('year', models.PositiveIntegerField()),
                ('generation_date', models.DateField()),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)])),
                ('gross_earnings', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('net_payable', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='payslips/%Y/%m/')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('generated', 'Generated'), ('emailed', 'Emailed'), ('printed', 'Printed')], default='draft', max_length=20)),
                ('email_sent', models.BooleanField(default=False)),
                ('email_sent_at', models.DateTimeField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payslips', to='employees_app.employee')),
                ('payroll_item', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payslip', to='payroll_app.payrollitem')),
            ],
            options={
                'verbose_name': 'Payslip',
                'verbose_name_plural': 'Payslips',
                'ordering': ['-year', '-month', 'employee__first_name', 'employee__last_name'],
                'unique_together': {('employee', 'month', 'year')},
            },
        ),
    ]
