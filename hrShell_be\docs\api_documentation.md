# HR Management API Documentation

## Overview

The HR Management API provides a comprehensive set of endpoints for managing human resources in an organization. It includes functionality for managing employees, departments, employee details, attendance, leaves, job history, salaries, documents, skills, and organization structure.

## Authentication

The API uses JWT (JSON Web Token) authentication. To access protected endpoints, you need to include the J<PERSON><PERSON> token in the Authorization header of your requests.

### Getting a Token

```
POST /api/token/
```

**Request Body:**
```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "access": "access_token_here",
  "refresh": "refresh_token_here"
}
```

### Refreshing a Token

```
POST /api/token/refresh/
```

**Request Body:**
```json
{
  "refresh": "your_refresh_token"
}
```

**Response:**
```json
{
  "access": "new_access_token_here"
}
```

### Verifying a Token

```
POST /api/token/verify/
```

**Request Body:**
```json
{
  "token": "your_token"
}
```

## Endpoints

### Organization Management

#### Organizations

##### List all organizations

```
GET /api/v1/organizations/
```

##### Get a specific organization

```
GET /api/v1/organizations/{id}/
```

##### Create a new organization

```
POST /api/v1/organizations/
```

**Request Body:**
```json
{
  "name": "Acme Corporation",
  "business_type": "private_limited",
  "establishment_date": "2000-01-01",
  "industry_sector": "Technology",
  "default_currency": "usd",
  "default_timezone": "UTC",
  "fiscal_year_start_month": 1,
  "fiscal_year_start_day": 1,
  "website": "https://acme.example.com",
  "email": "<EMAIL>",
  "phone": "+*********0",
  "status": "active"
}
```

##### Update an organization

```
PUT /api/v1/organizations/{id}/
```

##### Partially update an organization

```
PATCH /api/v1/organizations/{id}/
```

##### Delete an organization

```
DELETE /api/v1/organizations/{id}/
```

##### Get locations for an organization

```
GET /api/v1/organizations/{id}/locations/
```

##### Get business units for an organization

```
GET /api/v1/organizations/{id}/business_units/
```

##### Get designations for an organization

```
GET /api/v1/organizations/{id}/designations/
```

##### Get policies for an organization

```
GET /api/v1/organizations/{id}/policies/
```

##### Get documents for an organization

```
GET /api/v1/organizations/{id}/documents/
```

#### Locations

##### List all locations

```
GET /api/v1/locations/
```

##### Get a specific location

```
GET /api/v1/locations/{id}/
```

##### Create a new location

```
POST /api/v1/locations/
```

**Request Body:**
```json
{
  "name": "Headquarters",
  "organization": 1,
  "address_line1": "123 Main St",
  "city": "San Francisco",
  "state": "CA",
  "country": "USA",
  "postal_code": "94105",
  "phone": "+*********0",
  "email": "<EMAIL>",
  "working_hours_start": "09:00:00",
  "working_hours_end": "17:00:00",
  "working_days": "Mon,Tue,Wed,Thu,Fri",
  "is_headquarters": true,
  "status": "active"
}
```

##### Update a location

```
PUT /api/v1/locations/{id}/
```

##### Partially update a location

```
PATCH /api/v1/locations/{id}/
```

##### Delete a location

```
DELETE /api/v1/locations/{id}/
```

##### Get business units for a location

```
GET /api/v1/locations/{id}/business_units/
```

##### Get headquarters locations

```
GET /api/v1/locations/headquarters/
```

##### Get locations by organization

```
GET /api/v1/locations/by_organization/?organization_id=1
```

#### Business Units

##### List all business units

```
GET /api/v1/business-units/
```

##### Get a specific business unit

```
GET /api/v1/business-units/{id}/
```

##### Create a new business unit

```
POST /api/v1/business-units/
```

**Request Body:**
```json
{
  "name": "Product Development",
  "organization": 1,
  "description": "Responsible for product development",
  "code": "PD",
  "parent": null,
  "head": 1,
  "primary_location": 1,
  "status": "active"
}
```

##### Update a business unit

```
PUT /api/v1/business-units/{id}/
```

##### Partially update a business unit

```
PATCH /api/v1/business-units/{id}/
```

##### Delete a business unit

```
DELETE /api/v1/business-units/{id}/
```

##### Get child business units

```
GET /api/v1/business-units/{id}/children/
```

##### Get departments for a business unit

```
GET /api/v1/business-units/{id}/departments/
```

##### Get business units by organization

```
GET /api/v1/business-units/by_organization/?organization_id=1
```

##### Get root business units

```
GET /api/v1/business-units/root_units/
```

##### Get organizational chart for a business unit

```
GET /api/v1/business-units/{id}/org_chart/
```

#### Designations

##### List all designations

```
GET /api/v1/designations/
```

##### Get a specific designation

```
GET /api/v1/designations/{id}/
```

##### Create a new designation

```
POST /api/v1/designations/
```

**Request Body:**
```json
{
  "title": "Software Engineer",
  "organization": 1,
  "description": "Develops software applications",
  "level": "mid",
  "department": 1,
  "pay_grade": "B2",
  "min_salary": 60000,
  "max_salary": 90000
}
```

##### Update a designation

```
PUT /api/v1/designations/{id}/
```

##### Partially update a designation

```
PATCH /api/v1/designations/{id}/
```

##### Delete a designation

```
DELETE /api/v1/designations/{id}/
```

##### Get designations by organization

```
GET /api/v1/designations/by_organization/?organization_id=1
```

##### Get designations by department

```
GET /api/v1/designations/by_department/?department_id=1
```

##### Get designations by level

```
GET /api/v1/designations/by_level/?level=mid
```

#### Organization Policies

##### List all policies

```
GET /api/v1/policies/
```

##### Get a specific policy

```
GET /api/v1/policies/{id}/
```

##### Create a new policy

```
POST /api/v1/policies/
```

**Request Body:**
```json
{
  "name": "Annual Leave Policy",
  "organization": 1,
  "description": "Policy for annual leave",
  "policy_type": "leave",
  "content": "Employees are entitled to 20 days of annual leave...",
  "applicable_to_all": true,
  "effective_from": "2023-01-01"
}
```

##### Update a policy

```
PUT /api/v1/policies/{id}/
```

##### Partially update a policy

```
PATCH /api/v1/policies/{id}/
```

##### Delete a policy

```
DELETE /api/v1/policies/{id}/
```

##### Get policies by organization

```
GET /api/v1/policies/by_organization/?organization_id=1
```

##### Get policies by type

```
GET /api/v1/policies/by_type/?policy_type=leave
```

##### Get policies by location

```
GET /api/v1/policies/by_location/?location_id=1
```

##### Get policies by department

```
GET /api/v1/policies/by_department/?department_id=1
```

##### Get policies by business unit

```
GET /api/v1/policies/by_business_unit/?business_unit_id=1
```

#### Organization Documents

##### List all documents

```
GET /api/v1/documents/
```

##### Get a specific document

```
GET /api/v1/documents/{id}/
```

##### Create a new document

```
POST /api/v1/documents/
```

**Request Body:**
```json
{
  "title": "Certificate of Incorporation",
  "organization": 1,
  "description": "Certificate of incorporation of the company",
  "document_type": "certificate",
  "version": "1.0",
  "effective_date": "2000-01-01"
}
```

##### Update a document

```
PUT /api/v1/documents/{id}/
```

##### Partially update a document

```
PATCH /api/v1/documents/{id}/
```

##### Delete a document

```
DELETE /api/v1/documents/{id}/
```

##### Get documents by organization

```
GET /api/v1/documents/by_organization/?organization_id=1
```

##### Get documents by type

```
GET /api/v1/documents/by_type/?document_type=certificate
```

##### Get documents by business unit

```
GET /api/v1/documents/by_business_unit/?business_unit_id=1
```

##### Get expired documents

```
GET /api/v1/documents/expired/
```

##### Get documents expiring soon

```
GET /api/v1/documents/expiring_soon/
```

### Employee Management

#### Employees

#### List all employees

```
GET /api/v1/employees/
```

#### Get a specific employee

```
GET /api/v1/employees/{id}/
```

#### Create a new employee

```
POST /api/v1/employees/
```

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone": "+*********0",
  "position": "Software Engineer",
  "department": 1,
  "hire_date": "2023-01-01",
  "gender": "male",
  "status": "active"
}
```

#### Update an employee

```
PUT /api/v1/employees/{id}/
```

#### Partially update an employee

```
PATCH /api/v1/employees/{id}/
```

#### Delete an employee

```
DELETE /api/v1/employees/{id}/
```

### Departments

#### List all departments

```
GET /api/v1/departments/
```

#### Get a specific department

```
GET /api/v1/departments/{id}/
```

#### Create a new department

```
POST /api/v1/departments/
```

**Request Body:**
```json
{
  "name": "Engineering",
  "description": "Software Engineering Department",
  "manager": 1,
  "status": "active"
}
```

#### Update a department

```
PUT /api/v1/departments/{id}/
```

#### Partially update a department

```
PATCH /api/v1/departments/{id}/
```

#### Delete a department

```
DELETE /api/v1/departments/{id}/
```

### Employee Details

#### List all employee details

```
GET /api/v1/employee-details/
```

#### Get specific employee details

```
GET /api/v1/employee-details/{id}/
```

#### Create employee details

```
POST /api/v1/employee-details/
```

**Request Body:**
```json
{
  "employee": 1,
  "address": "123 Main St, City, Country",
  "contact_number": "+*********0",
  "emergency_contact_name": "Jane Doe",
  "emergency_contact_number": "+0987654321",
  "email": "<EMAIL>",
  "department": 1,
  "reporting_manager": 2,
  "experience": "5 years of software development experience",
  "technical_skills": "Python, Django, JavaScript",
  "qualifications": "Bachelor's in Computer Science",
  "date_of_birth": "1990-01-01",
  "marital_status": "single",
  "blood_group": "O+",
  "hobbies": "Reading, Hiking",
  "languages_known": "English, Spanish"
}
```

#### Update employee details

```
PUT /api/v1/employee-details/{id}/
```

#### Partially update employee details

```
PATCH /api/v1/employee-details/{id}/
```

#### Delete employee details

```
DELETE /api/v1/employee-details/{id}/
```

#### Get details for a specific employee

```
GET /api/v1/employee-details/employee/{employee_id}/
```

#### Get details for employees in a specific department

```
GET /api/v1/employee-details/department/{department_id}/
```

#### Get marital status statistics

```
GET /api/v1/employee-details/marital_status_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department

Example:
```
GET /api/v1/employee-details/marital_status_statistics/?department_id=1
```

#### Get blood group statistics

```
GET /api/v1/employee-details/blood_group_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department

Example:
```
GET /api/v1/employee-details/blood_group_statistics/?department_id=1
```

### Employee Hikes

#### List all employee hikes

```
GET /api/v1/employee-hikes/
```

#### Get a specific employee hike

```
GET /api/v1/employee-hikes/{id}/
```

#### Create a new employee hike

```
POST /api/v1/employee-hikes/
```

**Request Body:**
```json
{
  "employee": 1,
  "hike_percentage": 10.5,
  "hike_effective_date": "2023-04-01",
  "hike_reason": "Annual performance review",
  "department": 1,
  "previous_salary": 50000,
  "new_salary": 55250,
  "status": "pending"
}
```

#### Update an employee hike

```
PUT /api/v1/employee-hikes/{id}/
```

#### Partially update an employee hike

```
PATCH /api/v1/employee-hikes/{id}/
```

#### Delete an employee hike

```
DELETE /api/v1/employee-hikes/{id}/
```

#### Get hikes for a specific employee

```
GET /api/v1/employee-hikes/employee/{employee_id}/
```

#### Get hikes for a specific department

```
GET /api/v1/employee-hikes/department/{department_id}/
```

#### Get hikes within a date range

```
GET /api/v1/employee-hikes/date_range_hikes/?start_date=2023-01-01&end_date=2023-12-31
```

#### Approve a hike (admin only)

```
POST /api/v1/employee-hikes/{id}/approve_hike/
```

#### Reject a hike (admin only)

```
POST /api/v1/employee-hikes/{id}/reject_hike/
```

#### Get hike statistics

```
GET /api/v1/employee-hikes/hike_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department
- `year`: Filter statistics by year

Example:
```
GET /api/v1/employee-hikes/hike_statistics/?department_id=1&year=2023
```

**Response:**
```json
{
  "total_hikes": 10,
  "average_hike_percentage": 8.5,
  "total_employees_hiked": 8,
  "department_wise_count": [
    {
      "department_id": 1,
      "department_name": "Engineering",
      "hike_count": 5,
      "average_hike_percentage": 9.2
    },
    {
      "department_id": 2,
      "department_name": "Marketing",
      "hike_count": 3,
      "average_hike_percentage": 7.5
    }
  ]
}
```

### Employee Attendance

#### List all attendance records

```
GET /api/v1/employee-attendance/
```

#### Get a specific attendance record

```
GET /api/v1/employee-attendance/{id}/
```

#### Create a new attendance record

```
POST /api/v1/employee-attendance/
```

**Request Body:**
```json
{
  "employee": 1,
  "date": "2023-04-01",
  "check_in": "09:00:00",
  "check_out": "17:00:00",
  "status": "present"
}
```

#### Update an attendance record

```
PUT /api/v1/employee-attendance/{id}/
```

#### Partially update an attendance record

```
PATCH /api/v1/employee-attendance/{id}/
```

#### Delete an attendance record

```
DELETE /api/v1/employee-attendance/{id}/
```

#### Get attendance records for a specific employee

```
GET /api/v1/employee-attendance/employee/{employee_id}/
```

#### Get attendance records for employees in a specific department

```
GET /api/v1/employee-attendance/department/{department_id}/
```

#### Get attendance records within a date range

```
GET /api/v1/employee-attendance/date_range_attendance/?start_date=2023-01-01&end_date=2023-12-31
```

#### Get attendance records for today

```
GET /api/v1/employee-attendance/today_attendance/
```

#### Get attendance statistics

```
GET /api/v1/employee-attendance/attendance_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department
- `start_date`: Start date for statistics
- `end_date`: End date for statistics

Example:
```
GET /api/v1/employee-attendance/attendance_statistics/?department_id=1&start_date=2023-01-01&end_date=2023-12-31
```

### Employee Leaves

#### List all leave records

```
GET /api/v1/employee-leaves/
```

#### Get a specific leave record

```
GET /api/v1/employee-leaves/{id}/
```

#### Create a new leave record

```
POST /api/v1/employee-leaves/
```

**Request Body:**
```json
{
  "employee": 1,
  "leave_type": "casual",
  "start_date": "2023-04-01",
  "end_date": "2023-04-03",
  "reason": "Personal reasons",
  "status": "pending",
  "approved_by": null
}
```

#### Update a leave record

```
PUT /api/v1/employee-leaves/{id}/
```

#### Partially update a leave record

```
PATCH /api/v1/employee-leaves/{id}/
```

#### Delete a leave record

```
DELETE /api/v1/employee-leaves/{id}/
```

#### Get leave records for a specific employee

```
GET /api/v1/employee-leaves/employee/{employee_id}/
```

#### Get leave records for employees in a specific department

```
GET /api/v1/employee-leaves/department/{department_id}/
```

#### Get leave records within a date range

```
GET /api/v1/employee-leaves/date_range_leaves/?start_date=2023-01-01&end_date=2023-12-31
```

#### Get pending leave requests

```
GET /api/v1/employee-leaves/pending_leaves/
```

#### Approve a leave request (admin only)

```
POST /api/v1/employee-leaves/{id}/approve_leave/
```

#### Reject a leave request (admin only)

```
POST /api/v1/employee-leaves/{id}/reject_leave/
```

#### Get leave statistics

```
GET /api/v1/employee-leaves/leave_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department
- `year`: Filter statistics by year

Example:
```
GET /api/v1/employee-leaves/leave_statistics/?department_id=1&year=2023
```

### Job History

#### List all job history records

```
GET /api/v1/job-history/
```

#### Get a specific job history record

```
GET /api/v1/job-history/{id}/
```

#### Create a new job history record

```
POST /api/v1/job-history/
```

**Request Body:**
```json
{
  "employee": 1,
  "previous_job_title": "Junior Developer",
  "previous_department": "Engineering",
  "previous_salary": 45000,
  "start_date": "2020-01-01",
  "end_date": "2022-12-31",
  "reason": "Promotion",
  "approved_by": 2
}
```

#### Update a job history record

```
PUT /api/v1/job-history/{id}/
```

#### Partially update a job history record

```
PATCH /api/v1/job-history/{id}/
```

#### Delete a job history record

```
DELETE /api/v1/job-history/{id}/
```

#### Get job history records for a specific employee

```
GET /api/v1/job-history/employee/{employee_id}/
```

#### Get job history records for employees in a specific department

```
GET /api/v1/job-history/department/{department_id}/
```

#### Get job history records within a date range

```
GET /api/v1/job-history/date_range_history/?start_date=2020-01-01&end_date=2023-12-31
```

#### Get job history statistics

```
GET /api/v1/job-history/job_history_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department
- `year`: Filter statistics by year

Example:
```
GET /api/v1/job-history/job_history_statistics/?department_id=1&year=2023
```

### Salaries

#### List all salary records

```
GET /api/v1/salaries/
```

#### Get a specific salary record

```
GET /api/v1/salaries/{id}/
```

#### Create a new salary record

```
POST /api/v1/salaries/
```

**Request Body:**
```json
{
  "employee": 1,
  "basic": 50000,
  "allowance": 5000,
  "deduction": 2000,
  "payment_method": "bank_transfer",
  "bank_account_details": "Bank: Example Bank, Account: *********",
  "salary_effective_date": "2023-01-01"
}
```

#### Update a salary record

```
PUT /api/v1/salaries/{id}/
```

#### Partially update a salary record

```
PATCH /api/v1/salaries/{id}/
```

#### Delete a salary record

```
DELETE /api/v1/salaries/{id}/
```

#### Get salary records for a specific employee

```
GET /api/v1/salaries/employee/{employee_id}/
```

#### Get salary records for employees in a specific department

```
GET /api/v1/salaries/department/{department_id}/
```

#### Get salary records within a date range

```
GET /api/v1/salaries/date_range_salaries/?start_date=2023-01-01&end_date=2023-12-31
```

#### Get payment method statistics

```
GET /api/v1/salaries/payment_method_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department

Example:
```
GET /api/v1/salaries/payment_method_statistics/?department_id=1
```

#### Get salary statistics

```
GET /api/v1/salaries/salary_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department

Example:
```
GET /api/v1/salaries/salary_statistics/?department_id=1
```

### Employee Documents

#### List all document records

```
GET /api/v1/employee-documents/
```

#### Get a specific document record

```
GET /api/v1/employee-documents/{id}/
```

#### Create a new document record

```
POST /api/v1/employee-documents/
```

**Request Body:**
```json
{
  "employee": 1,
  "resume": null,
  "contract": null,
  "signed_offer_letter": null,
  "experience_certificates": null,
  "qualification_certificates": null
}
```

Note: For file uploads, use multipart/form-data instead of JSON.

#### Update a document record

```
PUT /api/v1/employee-documents/{id}/
```

#### Partially update a document record

```
PATCH /api/v1/employee-documents/{id}/
```

#### Delete a document record

```
DELETE /api/v1/employee-documents/{id}/
```

#### Get documents for a specific employee

```
GET /api/v1/employee-documents/employee/{employee_id}/
```

#### Get documents for employees in a specific department

```
GET /api/v1/employee-documents/department/{department_id}/
```

#### Get document statistics

```
GET /api/v1/employee-documents/document_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department

Example:
```
GET /api/v1/employee-documents/document_statistics/?department_id=1
```

#### Get employees with missing documents

```
GET /api/v1/employee-documents/missing_documents/
```

Optional query parameters:
- `document_type`: Filter by document type (resume, contract, signed_offer_letter, experience_certificates, qualification_certificates)
- `department_id`: Filter by department

Example:
```
GET /api/v1/employee-documents/missing_documents/?document_type=resume&department_id=1
```

### Skill Offerings

#### List all skill records

```
GET /api/v1/skill-offerings/
```

#### Get a specific skill record

```
GET /api/v1/skill-offerings/{id}/
```

#### Create a new skill record

```
POST /api/v1/skill-offerings/
```

**Request Body:**
```json
{
  "employee": 1,
  "skill_name": "Python",
  "skill_level": "advanced",
  "years_of_experience": 5.0,
  "is_primary_skill": true,
  "department": 1,
  "actual_skill_type": "technical"
}
```

#### Update a skill record

```
PUT /api/v1/skill-offerings/{id}/
```

#### Partially update a skill record

```
PATCH /api/v1/skill-offerings/{id}/
```

#### Delete a skill record

```
DELETE /api/v1/skill-offerings/{id}/
```

#### Get skills for a specific employee

```
GET /api/v1/skill-offerings/employee/{employee_id}/
```

#### Get skills for employees in a specific department

```
GET /api/v1/skill-offerings/department/{department_id}/
```

#### Get skill statistics

```
GET /api/v1/skill-offerings/skill_statistics/
```

Optional query parameters:
- `department_id`: Filter statistics by department
- `skill_type`: Filter by skill type (technical, soft, domain, language, certification)

Example:
```
GET /api/v1/skill-offerings/skill_statistics/?department_id=1&skill_type=technical
```

#### Search for employees with specific skills

```
GET /api/v1/skill-offerings/skill_search/?skill_name=Python
```

Optional query parameters:
- `skill_name`: Search for this skill (required)
- `skill_level`: Filter by skill level (beginner, intermediate, advanced, expert)
- `min_experience`: Filter by minimum years of experience
- `department_id`: Filter by department

Example:
```
GET /api/v1/skill-offerings/skill_search/?skill_name=Python&skill_level=advanced&min_experience=3&department_id=1
```

#### Get skill matrix for the organization

```
GET /api/v1/skill-offerings/skill_matrix/
```

Optional query parameters:
- `department_id`: Filter by department
- `skill_type`: Filter by skill type (technical, soft, domain, language, certification)

Example:
```
GET /api/v1/skill-offerings/skill_matrix/?department_id=1&skill_type=technical
```

## Filtering, Searching, and Ordering

Most endpoints support filtering, searching, and ordering. Here are some examples:

### Filtering

```
GET /api/v1/employees/?status=active
GET /api/v1/departments/?status=active
GET /api/v1/employee-leaves/?status=pending
GET /api/v1/employee-attendance/?date=2023-04-01
```

### Searching

```
GET /api/v1/employees/?search=John
GET /api/v1/departments/?search=Engineering
GET /api/v1/skill-offerings/?search=Python
```

### Ordering

```
GET /api/v1/employees/?ordering=first_name
GET /api/v1/employees/?ordering=-hire_date
GET /api/v1/salaries/?ordering=-basic
```

## Pagination

All list endpoints support pagination. By default, results are paginated with 10 items per page.

```
GET /api/v1/employees/?page=2
```

You can also specify the page size:

```
GET /api/v1/employees/?page=2&page_size=20
```

## Error Handling

The API returns appropriate HTTP status codes and error messages in case of errors.

### Common Error Codes

- 400 Bad Request: Invalid input data
- 401 Unauthorized: Authentication required
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Resource not found
- 500 Internal Server Error: Server-side error

### Error Response Format

```json
{
  "detail": "Error message here"
}
```

For validation errors:

```json
{
  "field_name": [
    "Error message for this field"
  ]
}
```
