# Employee Salary Models

## EmployeeSalary

The EmployeeSalary model assigns salary structure to employees with specific component values.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `employee` | ForeignKey | Employee this salary belongs to | Yes |
| `salary_structure` | ForeignKey | Salary structure assigned to the employee | Yes |
| `effective_from` | Date | Date from which this salary is effective | Yes |
| `effective_to` | Date | Date until which this salary is effective | No |
| `basic_salary` | Decimal | Basic salary amount | Yes |
| `gross_salary` | Decimal | Gross salary amount | Yes |
| `net_salary` | Decimal | Net salary amount | Yes |
| `is_active` | Boolean | Whether this salary is active | Yes (default: True) |
| `remarks` | Text | Any remarks about this salary | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `employee` | Employee | The employee this salary belongs to |
| `salary_structure` | SalaryStructure | The salary structure assigned to the employee |
| `components` | EmployeeSalaryComponent | The component values for this salary |
| `revisions_from` | SalaryRevision | The revisions where this is the previous salary |
| `revisions_to` | SalaryRevision | The revisions where this is the new salary |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, gross salary, and effective date |

### Example

```python
employee_salary = EmployeeSalary.objects.create(
    employee=john_doe,
    salary_structure=standard_structure,
    effective_from=date(2023, 1, 1),
    basic_salary=50000.00,
    gross_salary=80000.00,
    net_salary=70000.00,
    is_active=True
)
```

## EmployeeSalaryComponent

The EmployeeSalaryComponent model stores specific component values for an employee's salary.

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `employee_salary` | ForeignKey | Employee salary this component belongs to | Yes |
| `salary_component` | ForeignKey | Salary component | Yes |
| `value` | Decimal | Value of the component for this employee | Yes |
| `is_active` | Boolean | Whether this component is active | Yes (default: True) |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `employee_salary` | EmployeeSalary | The employee salary this component belongs to |
| `salary_component` | SalaryComponent | The salary component |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, component name, and value |

### Example

```python
basic_component = EmployeeSalaryComponent.objects.create(
    employee_salary=employee_salary,
    salary_component=basic_pay,
    value=50000.00,
    is_active=True
)

hra_component = EmployeeSalaryComponent.objects.create(
    employee_salary=employee_salary,
    salary_component=hra,
    value=20000.00,
    is_active=True
)
```

## SalaryRevision

The SalaryRevision model tracks salary revisions (hikes, promotions, etc.).

### Fields

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `id` | Integer | Primary key | Auto-generated |
| `employee` | ForeignKey | Employee whose salary is revised | Yes |
| `previous_salary` | ForeignKey | Previous salary | Yes |
| `new_salary` | ForeignKey | New salary | Yes |
| `revision_date` | Date | Date of revision | Yes |
| `revision_type` | String | Type of revision (hike/promotion/etc.) | Yes |
| `percentage_increase` | Decimal | Percentage increase in salary | Yes |
| `amount_increase` | Decimal | Amount increase in salary | Yes |
| `approved_by` | ForeignKey | Employee who approved the revision | No |
| `approval_date` | Date | Date of approval | No |
| `remarks` | Text | Any remarks about this revision | No |
| `created_at` | DateTime | Date and time the record was created | Auto-generated |
| `updated_at` | DateTime | Date and time the record was last updated | Auto-generated |

### Choices

#### Revision Type Choices
- `hike`: Salary Hike
- `promotion`: Promotion
- `adjustment`: Adjustment
- `annual_revision`: Annual Revision
- `other`: Other

### Relationships

| Relationship | Related Model | Description |
|--------------|--------------|-------------|
| `employee` | Employee | The employee whose salary is revised |
| `previous_salary` | EmployeeSalary | The previous salary |
| `new_salary` | EmployeeSalary | The new salary |
| `approved_by` | Employee | The employee who approved the revision |

### Methods

| Method | Description |
|--------|-------------|
| `__str__()` | Returns the employee, revision type, and date |

### Example

```python
new_salary = EmployeeSalary.objects.create(
    employee=john_doe,
    salary_structure=standard_structure,
    effective_from=date(2023, 4, 1),
    basic_salary=55000.00,
    gross_salary=88000.00,
    net_salary=77000.00,
    is_active=True
)

salary_revision = SalaryRevision.objects.create(
    employee=john_doe,
    previous_salary=employee_salary,
    new_salary=new_salary,
    revision_date=date(2023, 4, 1),
    revision_type="hike",
    percentage_increase=10.00,
    amount_increase=8000.00,
    approved_by=jane_smith,
    approval_date=date(2023, 3, 15),
    remarks="Annual performance-based hike"
)
```

## API Endpoints

### EmployeeSalary Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/employee-salaries/` | GET | List all employee salaries |
| `/api/v1/employee-salaries/` | POST | Create a new employee salary |
| `/api/v1/employee-salaries/{id}/` | GET | Retrieve a specific employee salary |
| `/api/v1/employee-salaries/{id}/` | PUT | Update a specific employee salary |
| `/api/v1/employee-salaries/{id}/` | DELETE | Delete a specific employee salary |
| `/api/v1/employee-salaries/active/` | GET | List all active employee salaries |
| `/api/v1/employee-salaries/employee/{employee_id}/` | GET | List all salaries for a specific employee |
| `/api/v1/employee-salaries/employee/{employee_id}/current/` | GET | Get the current active salary for a specific employee |
| `/api/v1/employee-salaries/{id}/components/` | GET | List all components for a specific employee salary |
| `/api/v1/employee-salaries/{id}/revisions/` | GET | List all revisions for a specific employee salary |
| `/api/v1/employee-salaries/{id}/deactivate/` | POST | Deactivate a specific employee salary |
| `/api/v1/employee-salaries/{id}/activate/` | POST | Activate a specific employee salary and deactivate all others for the same employee |
