#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to remove all permission classes from ViewSets across all apps.
"""

import os
import re
from pathlib import Path

def find_view_files(directory):
    """Find all view files in the project."""
    view_files = []
    for root, dirs, files in os.walk(directory):
        # Skip virtual environment, cache directories, and other non-project directories
        if any(skip_dir in root for skip_dir in ['.venv', '__pycache__', '.git', 'site-packages', 'Scripts', 'Lib']):
            continue
        if 'views' in root:
            for file in files:
                if file.endswith('.py') and file != '__init__.py':
                    view_files.append(os.path.join(root, file))
    return view_files

def remove_permission_imports(content):
    """Remove permission-related imports from the content."""
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        # Skip lines that import permission classes
        if ('from rest_framework.permissions import' in line or
            'from employees_app.permissions import' in line or
            'from common.permissions import' in line or
            'from leave_app.permissions import' in line):
            continue
        
        # Remove specific permission imports from existing import lines
        if 'from rest_framework import' in line:
            # Remove IsAuthenticated, IsAdminUser from the import
            line = re.sub(r',\s*IsAuthenticated', '', line)
            line = re.sub(r',\s*IsAdminUser', '', line)
            line = re.sub(r'IsAuthenticated\s*,\s*', '', line)
            line = re.sub(r'IsAdminUser\s*,\s*', '', line)
            line = re.sub(r'IsAuthenticated', '', line)
            line = re.sub(r'IsAdminUser', '', line)
        
        new_lines.append(line)
    
    return '\n'.join(new_lines)

def remove_permission_classes(content):
    """Remove permission_classes lines from ViewSets."""
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        # Skip lines that define permission_classes
        if 'permission_classes' in line and ('IsAuthenticated' in line or 'IsAdminOrReadOnly' in line or 'IsAdminUser' in line or 'IsOwnerOrAdmin' in line):
            continue
        new_lines.append(line)
    
    return '\n'.join(new_lines)

def process_file(file_path):
    """Process a single view file to remove permissions."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Remove permission imports
        content = remove_permission_imports(content)
        
        # Remove permission_classes lines
        content = remove_permission_classes(content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Updated: {file_path}")
            return True
        else:
            print(f"⏭️  No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def main():
    """Main function to remove permissions from all view files."""
    print("🔧 Removing permission classes from all ViewSets...")
    
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # Find all view files
    view_files = find_view_files(project_root)
    
    # Filter to only our app files
    app_view_files = [f for f in view_files if any(app in f for app in [
        'employees_app', 'organization_app', 'leave_app', 'payroll_app', 
        'recruitment_app', 'onboarding_offboarding_app', 'common'
    ])]
    
    print(f"📁 Found {len(app_view_files)} view files to process")
    
    updated_count = 0
    for file_path in app_view_files:
        if process_file(file_path):
            updated_count += 1
    
    print(f"\n🎉 Completed! Updated {updated_count} out of {len(app_view_files)} files")
    print("🔓 All permission classes have been removed from ViewSets")

if __name__ == "__main__":
    main()
