import re

def extract_endpoints(file_path):
    """
    Extract all API endpoints from the API documentation file.
    """
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Regular expression to match endpoints
    pattern = r'`(GET|POST|PUT|PATCH|DELETE) ([^`]+)`'
    
    # Find all matches
    matches = re.findall(pattern, content)
    
    # Group endpoints by app
    endpoints_by_app = {}
    current_app = None
    
    # Extract app sections
    app_sections = re.findall(r'## ([^\n]+) App Endpoints(.*?)(?=## [^\n]+ App Endpoints|\Z)', content, re.DOTALL)
    
    for app_name, app_content in app_sections:
        app_name = app_name.strip()
        endpoints = re.findall(pattern, app_content)
        endpoints_by_app[app_name] = endpoints
    
    return endpoints_by_app

def main():
    """
    Main function to extract and print all endpoints.
    """
    file_path = 'docs/api_endpoints.md'
    endpoints_by_app = extract_endpoints(file_path)
    
    # Print endpoints by app
    for app, endpoints in endpoints_by_app.items():
        print(f"\n## {app} Endpoints")
        
        # Group endpoints by resource
        resources = {}
        for method, path in endpoints:
            resource = path.split('/')[3] if len(path.split('/')) > 3 else 'other'
            if resource not in resources:
                resources[resource] = []
            resources[resource].append((method, path))
        
        # Print endpoints by resource
        for resource, resource_endpoints in sorted(resources.items()):
            print(f"\n### {resource.capitalize()}")
            for method, path in sorted(resource_endpoints, key=lambda x: (x[1], x[0])):
                print(f"- {method} {path}")

if __name__ == '__main__':
    main()
