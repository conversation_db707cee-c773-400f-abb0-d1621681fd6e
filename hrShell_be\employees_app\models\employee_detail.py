from django.db import models
from django.core.validators import RegexValidator
from enum import Enum


class MaritalStatus(str, Enum):
    SINGLE = 'single'
    MARRIED = 'married'
    DIVORCED = 'divorced'
    WIDOWED = 'widowed'
    OTHER = 'other'

    @classmethod
    def choices(cls):
        return [(item.value, item.name.title()) for item in cls]


class EmployeeDetail(models.Model):
    """
    Employee detail model for storing additional employee information
    beyond the basic details stored in the Employee model.
    """
    # Basic relationship
    employee = models.OneToOneField(
        'Employee',
        on_delete=models.CASCADE,
        related_name='detail',
        help_text="The employee this detail belongs to"
    )

    # Contact information
    address = models.TextField(
        help_text="Employee's residential address"
    )
    contact_number = models.CharField(
        max_length=20,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
            ),
        ],
        help_text="Employee's contact phone number"
    )
    emergency_contact_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Name of emergency contact person"
    )
    emergency_contact_number = models.Char<PERSON>ield(
        max_length=20,
        blank=True,
        null=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
            ),
        ],
        help_text="Phone number of emergency contact person"
    )
    email = models.EmailField(
        help_text="Employee's personal email address"
    )

    # Professional information
    department = models.ForeignKey(
        'Department',
        on_delete=models.SET_NULL,
        null=True,
        related_name='employee_details',
        help_text="Department the employee belongs to"
    )
    reporting_manager = models.ForeignKey(
        'Employee',
        on_delete=models.SET_NULL,
        null=True,
        related_name='reporting_employees',
        help_text="Employee's reporting manager"
    )
    experience = models.TextField(
        help_text="Employee's work experience"
    )
    technical_skills = models.TextField(
        help_text="Employee's technical skills"
    )
    qualifications = models.TextField(
        help_text="Employee's educational qualifications"
    )

    # Personal information
    date_of_birth = models.DateField(
        null=True,
        blank=True,
        help_text="Employee's date of birth"
    )
    marital_status = models.CharField(
        max_length=10,
        choices=MaritalStatus.choices(),
        default=MaritalStatus.SINGLE.value,
        help_text="Employee's marital status"
    )
    blood_group = models.CharField(
        max_length=5,
        blank=True,
        null=True,
        help_text="Employee's blood group"
    )

    # Additional information
    hobbies = models.TextField(
        blank=True,
        null=True,
        help_text="Employee's hobbies and interests"
    )
    languages_known = models.TextField(
        blank=True,
        null=True,
        help_text="Languages known by the employee"
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Date and time when the record was created"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="Date and time when the record was last updated"
    )

    class Meta:
        verbose_name = "Employee Detail"
        verbose_name_plural = "Employee Details"
        ordering = ['employee__first_name', 'employee__last_name']

    def __str__(self):
        """String representation of the employee detail"""
        return f"{self.employee} - Details"