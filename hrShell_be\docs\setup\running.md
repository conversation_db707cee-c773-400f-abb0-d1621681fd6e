# Running the API

This guide will help you run the HR Management API in different environments.

## Development Environment

To run the API in a development environment, use the Django development server:

```bash
python manage.py runserver
```

This will start the server at http://127.0.0.1:8000/.

You can specify a different port:

```bash
python manage.py runserver 8080
```

Or a different IP address and port:

```bash
python manage.py runserver 0.0.0.0:8000
```

## Production Environment

For production, you should use a production-ready web server like Gunicorn or uWSGI, along with a reverse proxy like Nginx.

### Using Gunicorn

First, install Gunicorn:

```bash
pip install gunicorn
```

Then, run the API with Gunicorn:

```bash
gunicorn hrShell_be.wsgi:application --bind 0.0.0.0:8000
```

### Using uWSGI

First, install uWSGI:

```bash
pip install uwsgi
```

Then, create a uWSGI configuration file (`uwsgi.ini`):

```ini
[uwsgi]
http = :8000
module = hrShell_be.wsgi:application
master = true
processes = 4
threads = 2
```

Run the API with uWSGI:

```bash
uwsgi --ini uwsgi.ini
```

### Using Docker

You can also run the API using Docker. First, create a `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["gunicorn", "hrShell_be.wsgi:application", "--bind", "0.0.0.0:8000"]
```

Create a `docker-compose.yml` file:

```yaml
version: '3'

services:
  db:
    image: postgres:13
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    env_file:
      - ./.env
    environment:
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_DB=shell_dwh

  web:
    build: .
    restart: always
    depends_on:
      - db
    env_file:
      - ./.env
    volumes:
      - .:/app
    ports:
      - "8000:8000"

volumes:
  postgres_data:
```

Build and run the Docker containers:

```bash
docker-compose up -d
```

## Deployment Options

### Heroku

To deploy the API to Heroku, follow these steps:

1. Create a `Procfile`:

```
web: gunicorn hrShell_be.wsgi --log-file -
```

2. Create a `runtime.txt` file:

```
python-3.9.7
```

3. Install the Heroku CLI and log in:

```bash
heroku login
```

4. Create a new Heroku app:

```bash
heroku create your-app-name
```

5. Add a PostgreSQL database:

```bash
heroku addons:create heroku-postgresql:hobby-dev
```

6. Set environment variables:

```bash
heroku config:set DJANGO_SETTINGS_MODULE=hrShell_be.settings.production
heroku config:set SECRET_KEY=your-secret-key
```

7. Deploy the app:

```bash
git push heroku main
```

8. Run migrations:

```bash
heroku run python manage.py migrate
```

9. Create a superuser:

```bash
heroku run python manage.py createsuperuser
```

### AWS Elastic Beanstalk

To deploy the API to AWS Elastic Beanstalk, follow these steps:

1. Install the AWS CLI and EB CLI:

```bash
pip install awscli awsebcli
```

2. Configure the AWS CLI:

```bash
aws configure
```

3. Initialize the EB CLI:

```bash
eb init
```

4. Create an Elastic Beanstalk environment:

```bash
eb create your-environment-name
```

5. Deploy the app:

```bash
eb deploy
```

## Next Steps

- [Make your first API call](../guides/first-api-call.md)
- [Learn about the API endpoints](../api/README.md)
