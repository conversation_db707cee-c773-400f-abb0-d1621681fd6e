from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

# Import models when they are created
# from onboarding_offboarding_app.models.onboarding import OnboardingPlan, OnboardingTask
# from onboarding_offboarding_app.models.offboarding import OffboardingRequest, OffboardingTask

# Example signal handlers to be implemented
"""
@receiver(post_save, sender=OnboardingPlan)
def create_default_onboarding_tasks(sender, instance, created, **kwargs):
    if created:
        # Create default onboarding tasks for the new plan
        default_tasks = [
            {
                'name': 'Complete Personal Information Form',
                'description': 'Fill out the personal information form with your details.',
                'category': 'documentation',
                'due_days': 1,
            },
            {
                'name': 'Submit ID Proof',
                'description': 'Upload a copy of your ID proof (Passport, Driver\'s License, etc.).',
                'category': 'documentation',
                'due_days': 1,
            },
            {
                'name': 'Submit Address Proof',
                'description': 'Upload a copy of your address proof.',
                'category': 'documentation',
                'due_days': 1,
            },
            {
                'name': 'Submit PAN Card',
                'description': 'Upload a copy of your PAN card.',
                'category': 'documentation',
                'due_days': 1,
            },
            {
                'name': 'Submit Bank Details',
                'description': 'Provide your bank account details for salary processing.',
                'category': 'documentation',
                'due_days': 2,
            },
            {
                'name': 'Complete IT Setup',
                'description': 'Setup your laptop, email, and other IT resources.',
                'category': 'it_setup',
                'due_days': 1,
                'assigned_to_role': 'IT',
            },
            {
                'name': 'Attend Orientation Session',
                'description': 'Attend the company orientation session.',
                'category': 'orientation',
                'due_days': 3,
            },
            {
                'name': 'Meet Your Team',
                'description': 'Introduction to your team members.',
                'category': 'orientation',
                'due_days': 1,
            },
            {
                'name': 'Review Company Policies',
                'description': 'Read and acknowledge company policies.',
                'category': 'orientation',
                'due_days': 5,
            },
        ]
        
        for i, task_data in enumerate(default_tasks):
            due_date = instance.start_date + timezone.timedelta(days=task_data.pop('due_days', 1))
            OnboardingTask.objects.create(
                plan=instance,
                name=task_data.pop('name'),
                description=task_data.pop('description', ''),
                category=task_data.pop('category', 'other'),
                due_date=due_date,
                sequence=i + 1,
                **task_data
            )
        
        logger.info(f"Created default onboarding tasks for employee: {instance.employee}")

@receiver(post_save, sender=OffboardingRequest)
def create_default_offboarding_tasks(sender, instance, created, **kwargs):
    if created and instance.status == 'approved':
        # Create default offboarding tasks for the new request
        default_tasks = [
            {
                'name': 'Exit Interview',
                'description': 'Schedule and complete exit interview with HR.',
                'category': 'hr',
                'due_days': -3,  # 3 days before last working day
                'assigned_to_role': 'HR',
            },
            {
                'name': 'Knowledge Transfer',
                'description': 'Complete knowledge transfer to team members.',
                'category': 'department',
                'due_days': -5,  # 5 days before last working day
            },
            {
                'name': 'Return Laptop and Equipment',
                'description': 'Return all company equipment to IT department.',
                'category': 'it',
                'due_days': -1,  # 1 day before last working day
                'assigned_to_role': 'IT',
            },
            {
                'name': 'Return Access Card',
                'description': 'Return access card to Admin department.',
                'category': 'admin',
                'due_days': -1,  # 1 day before last working day
                'assigned_to_role': 'Admin',
            },
            {
                'name': 'Revoke System Access',
                'description': 'Revoke access to all systems and applications.',
                'category': 'it',
                'due_days': 0,  # On last working day
                'assigned_to_role': 'IT',
            },
            {
                'name': 'Final Settlement',
                'description': 'Process final settlement including salary, gratuity, etc.',
                'category': 'finance',
                'due_days': 30,  # 30 days after last working day
                'assigned_to_role': 'Finance',
            },
            {
                'name': 'Experience Letter',
                'description': 'Prepare and issue experience letter.',
                'category': 'hr',
                'due_days': 7,  # 7 days after last working day
                'assigned_to_role': 'HR',
            },
        ]
        
        for i, task_data in enumerate(default_tasks):
            due_days = task_data.pop('due_days', 0)
            due_date = instance.last_working_day + timezone.timedelta(days=due_days)
            OffboardingTask.objects.create(
                request=instance,
                name=task_data.pop('name'),
                description=task_data.pop('description', ''),
                category=task_data.pop('category', 'other'),
                due_date=due_date,
                sequence=i + 1,
                **task_data
            )
        
        logger.info(f"Created default offboarding tasks for employee: {instance.employee}")
"""
