from django.db import models
from django.core.validators import MinValueValidator
from employees_app.models.employee import Employee
from payroll_app.models.salary_structure import SalaryStructure, SalaryComponent


class EmployeeSalary(models.Model):
    """
    Model to assign salary structure to employees with specific component values.
    """
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='payroll_salaries')
    salary_structure = models.ForeignKey(SalaryStructure, on_delete=models.CASCADE, related_name='employee_salaries')
    effective_from = models.DateField()
    effective_to = models.DateField(null=True, blank=True)
    basic_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    gross_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    net_salary = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    is_active = models.BooleanField(default=True)
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-effective_from']
        verbose_name = 'Employee Salary'
        verbose_name_plural = 'Employee Salaries'

    def __str__(self):
        return f"{self.employee} - {self.gross_salary} (from {self.effective_from})"


class EmployeeSalaryComponent(models.Model):
    """
    Model to store specific component values for an employee's salary.
    """
    employee_salary = models.ForeignKey(EmployeeSalary, on_delete=models.CASCADE, related_name='components')
    salary_component = models.ForeignKey(SalaryComponent, on_delete=models.CASCADE, related_name='employee_values')
    value = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['salary_component__component_type', 'salary_component__name']
        verbose_name = 'Employee Salary Component'
        verbose_name_plural = 'Employee Salary Components'

    def __str__(self):
        return f"{self.employee_salary.employee} - {self.salary_component.name}: {self.value}"


class SalaryRevision(models.Model):
    """
    Model to track salary revisions (hikes, promotions, etc.).
    """
    REVISION_TYPE_CHOICES = [
        ('hike', 'Salary Hike'),
        ('promotion', 'Promotion'),
        ('adjustment', 'Adjustment'),
        ('annual_revision', 'Annual Revision'),
        ('other', 'Other'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='salary_revisions')
    previous_salary = models.ForeignKey(EmployeeSalary, on_delete=models.CASCADE, related_name='revisions_from')
    new_salary = models.ForeignKey(EmployeeSalary, on_delete=models.CASCADE, related_name='revisions_to')
    revision_date = models.DateField()
    revision_type = models.CharField(max_length=20, choices=REVISION_TYPE_CHOICES)
    percentage_increase = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0)])
    amount_increase = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0)])
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='salary_revisions_approved')
    approval_date = models.DateField(null=True, blank=True)
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-revision_date']
        verbose_name = 'Salary Revision'
        verbose_name_plural = 'Salary Revisions'

    def __str__(self):
        return f"{self.employee} - {self.revision_type} on {self.revision_date}"
