from rest_framework import serializers
from recruitment_app.models.recruitment_source import RecruitmentSource
from organization_app.serializers.organization_serializer import OrganizationSerializer


class RecruitmentSourceSerializer(serializers.ModelSerializer):
    """
    Serializer for the RecruitmentSource model.
    """
    organization_details = OrganizationSerializer(source='organization', read_only=True)
    
    class Meta:
        model = RecruitmentSource
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']
        extra_kwargs = {
            'api_key': {'write_only': True}
        }
