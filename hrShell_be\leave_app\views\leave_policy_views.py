from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from leave_app.models.leave_policy import LeavePolicy
from leave_app.serializers.leave_policy_serializer import LeavePolicySerializer
from leave_app.filters.leave_filters import LeavePolicyFilter


from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
class LeavePolicyViewSet(viewsets.ModelViewSet):

    @swagger_auto_schema(tags=['Leave Management'])
    def list(self, request, *args, **kwargs):
        """List all items"""
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def create(self, request, *args, **kwargs):
        """Create a new item"""
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def retrieve(self, request, *args, **kwargs):
        """Retrieve an item"""
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def update(self, request, *args, **kwargs):
        """Update an item"""
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def partial_update(self, request, *args, **kwargs):
        """Partially update an item"""
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Leave Management'])
    def destroy(self, request, *args, **kwargs):
        """Delete an item"""
        return super().destroy(request, *args, **kwargs)

    """
    API endpoint for managing leave policies
    
    list:
    Return a list of all leave policies
    
    create:
    Create a new leave policy
    
    retrieve:
    Return the given leave policy
    
    update:
    Update the given leave policy
    
    partial_update:
    Partially update the given leave policy
    
    destroy:
    Delete the given leave policy
    
    Additional actions:
    - organization_policies: Get leave policies for a specific organization
    - department_policies: Get leave policies for a specific department
    - location_policies: Get leave policies for a specific location
    - designation_policies: Get leave policies for a specific designation
    - business_unit_policies: Get leave policies for a specific business unit
    - employee_type_policies: Get leave policies for a specific employee type
    """
    queryset = LeavePolicy.objects.all()
    serializer_class = LeavePolicySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LeavePolicyFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']
    
    @action(detail=False, methods=['get'], url_path='organization/(?P<organization_id>[^/.]+)')
    def organization_policies(self, request, organization_id=None):
        """
        Get leave policies for a specific organization
        """
        policies = self.queryset.filter(organization_id=organization_id)
        page = self.paginate_queryset(policies)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='department/(?P<department_id>[^/.]+)')
    def department_policies(self, request, department_id=None):
        """
        Get leave policies for a specific department
        """
        policies = self.queryset.filter(department_id=department_id)
        page = self.paginate_queryset(policies)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='location/(?P<location_id>[^/.]+)')
    def location_policies(self, request, location_id=None):
        """
        Get leave policies for a specific location
        """
        policies = self.queryset.filter(location_id=location_id)
        page = self.paginate_queryset(policies)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='designation/(?P<designation_id>[^/.]+)')
    def designation_policies(self, request, designation_id=None):
        """
        Get leave policies for a specific designation
        """
        policies = self.queryset.filter(designation_id=designation_id)
        page = self.paginate_queryset(policies)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='business-unit/(?P<business_unit_id>[^/.]+)')
    def business_unit_policies(self, request, business_unit_id=None):
        """
        Get leave policies for a specific business unit
        """
        policies = self.queryset.filter(business_unit_id=business_unit_id)
        page = self.paginate_queryset(policies)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'], url_path='employee-type/(?P<employee_type>[^/.]+)')
    def employee_type_policies(self, request, employee_type=None):
        """
        Get leave policies for a specific employee type
        """
        policies = self.queryset.filter(employee_type=employee_type)
        page = self.paginate_queryset(policies)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(policies, many=True)
        return Response(serializer.data)
