# Authentication API Documentation

This document provides comprehensive documentation for the HR Shell authentication endpoints, following Augment guidelines for API documentation.

## Overview

The HR Shell application uses JWT (JSON Web Token) authentication for secure user access. The authentication system provides:

- **Enhanced Login Endpoint**: Modern login with comprehensive user information
- **Token Management**: Access and refresh token handling
- **Security Features**: Login attempt logging and account validation
- **User Registration**: Account creation for new users

## Authentication Endpoints

### Login Endpoint

**POST** `/api/auth/login/`

Authenticate a user with username and password credentials and receive JWT tokens.

#### Features

- ✅ Validates user credentials
- ✅ Returns JWT access and refresh tokens
- ✅ Provides comprehensive user information
- ✅ Logs authentication attempts for security
- ✅ Updates user's last login timestamp
- ✅ Handles inactive accounts
- ✅ Comprehensive error messages

#### Request

```http
POST /api/auth/login/
Content-Type: application/json

{
  "username": "john_doe",
  "password": "secure_password123"
}
```

#### Request Parameters

| Parameter | Type   | Required | Description                    |
|-----------|--------|----------|--------------------------------|
| username  | string | Yes      | Username for authentication    |
| password  | string | Yes      | Password for authentication    |

#### Success Response (200 OK)

```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "john_doe",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "full_name": "John Doe",
    "is_staff": false,
    "is_superuser": false,
    "last_login": "2024-01-15T10:30:00Z",
    "date_joined": "2024-01-01T09:00:00Z"
  },
  "message": "Login successful"
}
```

#### Error Responses

**400 Bad Request - Invalid Credentials**
```json
{
  "detail": "Invalid username or password. Please check your credentials and try again."
}
```

**400 Bad Request - Missing Fields**
```json
{
  "detail": "Both username and password are required."
}
```

**401 Unauthorized - Account Deactivated**
```json
{
  "detail": "This account has been deactivated. Please contact your administrator."
}
```

#### Usage Examples

**cURL Example**
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john_doe",
    "password": "secure_password123"
  }'
```

**JavaScript Example**
```javascript
const response = await fetch('/api/auth/login/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    username: 'john_doe',
    password: 'secure_password123'
  })
});

const data = await response.json();
if (response.ok) {
  // Store tokens
  localStorage.setItem('access_token', data.access);
  localStorage.setItem('refresh_token', data.refresh);
  console.log('Login successful:', data.user);
} else {
  console.error('Login failed:', data.detail);
}
```

**Python Example**
```python
import requests

url = 'http://localhost:8000/api/auth/login/'
data = {
    'username': 'john_doe',
    'password': 'secure_password123'
}

response = requests.post(url, json=data)
if response.status_code == 200:
    result = response.json()
    access_token = result['access']
    user_info = result['user']
    print(f"Login successful for {user_info['full_name']}")
else:
    print(f"Login failed: {response.json()['detail']}")
```

### Token Refresh

**POST** `/api/token/refresh/`

Refresh an expired access token using a valid refresh token.

#### Request
```json
{
  "refresh": "your_refresh_token_here"
}
```

#### Response
```json
{
  "access": "new_access_token_here"
}
```

### Token Verification

**POST** `/api/token/verify/`

Verify if a token is valid and not expired.

#### Request
```json
{
  "token": "your_token_here"
}
```

#### Response
- **200 OK**: Token is valid
- **401 Unauthorized**: Token is invalid or expired

## Using Authentication Tokens

### Including Tokens in Requests

Include the access token in the Authorization header for authenticated requests:

```http
GET /api/v1/employees/
Authorization: Bearer your_access_token_here
```

### Token Lifecycle

1. **Access Token**: Expires in 1 hour (configurable)
2. **Refresh Token**: Expires in 1 day (configurable)
3. **Automatic Refresh**: Use refresh token to get new access token

### Security Best Practices

1. **Store Tokens Securely**: Use secure storage mechanisms
2. **Handle Token Expiration**: Implement automatic token refresh
3. **Logout Properly**: Clear tokens from storage
4. **Monitor Failed Attempts**: Check logs for security issues

## Legacy Endpoints

For backward compatibility, the following legacy endpoints are still available:

- **POST** `/api/token/` - Legacy login endpoint
- **POST** `/api/register/` - Legacy registration endpoint

**Note**: New applications should use the enhanced endpoints under `/api/auth/`.

## Error Handling

The authentication system provides detailed error messages to help with troubleshooting:

| Error Type | Status Code | Description |
|------------|-------------|-------------|
| Invalid Credentials | 400 | Username or password is incorrect |
| Missing Fields | 400 | Required fields are missing |
| Account Deactivated | 401 | User account has been disabled |
| Token Expired | 401 | Access token has expired |
| Invalid Token | 401 | Token is malformed or invalid |

## Security Features

- **Login Attempt Logging**: All login attempts are logged for security monitoring
- **Account Validation**: Inactive accounts are rejected
- **Token Expiration**: Tokens have configurable expiration times
- **Secure Headers**: Proper security headers are included in responses

## Next Steps

- [Learn about working with employees](employees.md)
- [Learn about API rate limiting](rate-limiting.md)
- [Learn about error handling](error-handling.md)
