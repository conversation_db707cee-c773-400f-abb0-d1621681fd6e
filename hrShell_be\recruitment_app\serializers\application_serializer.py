from rest_framework import serializers
from recruitment_app.models.application import Application, ApplicationStageHistory
from recruitment_app.serializers.job_posting_serializer import JobPostingSerializer
from recruitment_app.serializers.candidate_serializer import CandidateSerializer
from recruitment_app.serializers.recruitment_source_serializer import RecruitmentSourceSerializer
from employees_app.serializers.employee_serializer import EmployeeSerializer


class ApplicationStageHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for the ApplicationStageHistory model.
    """
    changed_by_details = EmployeeSerializer(source='changed_by', read_only=True)
    
    class Meta:
        model = ApplicationStageHistory
        fields = '__all__'
        read_only_fields = ['changed_at']


class ApplicationSerializer(serializers.ModelSerializer):
    """
    Serializer for the Application model.
    """
    job_posting_details = JobPostingSerializer(source='job_posting', read_only=True)
    candidate_details = CandidateSerializer(source='candidate', read_only=True)
    source_details = RecruitmentSourceSerializer(source='source', read_only=True)
    assigned_to_details = EmployeeSerializer(source='assigned_to', read_only=True)
    rejected_by_details = EmployeeSerializer(source='rejected_by', read_only=True)
    stage_history = ApplicationStageHistorySerializer(many=True, read_only=True)
    
    class Meta:
        model = Application
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at', 'last_status_change']
    
    def create(self, validated_data):
        """
        Create a new application and record the initial stage.
        """
        application = Application.objects.create(**validated_data)
        
        # Record the initial stage
        ApplicationStageHistory.objects.create(
            application=application,
            to_stage=application.current_stage,
            to_status=application.status,
            changed_by=self.context.get('request').user.employee if hasattr(self.context.get('request').user, 'employee') else None,
            notes="Initial application created"
        )
        
        return application
    
    def update(self, instance, validated_data):
        """
        Update an application and record stage changes.
        """
        old_stage = instance.current_stage
        old_status = instance.status
        
        # Update the application
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        # Check if stage or status has changed
        if instance.current_stage != old_stage or instance.status != old_status:
            instance.last_status_change = serializers.DateTimeField().to_representation(
                serializers.DateTimeField().to_internal_value(serializers.DateTimeField().to_representation(serializers.DateTimeField().now))
            )
            
            # Record the stage change
            ApplicationStageHistory.objects.create(
                application=instance,
                from_stage=old_stage,
                to_stage=instance.current_stage,
                from_status=old_status,
                to_status=instance.status,
                changed_by=self.context.get('request').user.employee if hasattr(self.context.get('request').user, 'employee') else None,
                notes=validated_data.get('notes', '')
            )
        
        instance.save()
        return instance
